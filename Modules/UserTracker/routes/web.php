<?php

use Illuminate\Support\Facades\Route;
use Modules\UserTracker\app\Http\Controllers\UserTrackerController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('admin')->middleware(['web', 'auth'])->name('admin.')->group(function () {    // User Tracker Module Routes
    Route::get('/user-tracker', [UserTrackerController::class, 'index'])->name('user-tracker.index');
    Route::get('/user-tracker/online-users', [UserTrackerController::class, 'onlineUsers'])->name('user-tracker.online-users');
    Route::get('/user-tracker/activities', [UserTrackerController::class, 'activities'])->name('user-tracker.activities');
    Route::get('/user-tracker/screen-time', [UserTrackerController::class, 'screenTime'])->name('user-tracker.screen-time');
}); 