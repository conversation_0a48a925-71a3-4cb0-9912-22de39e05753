@php 
use Illuminate\Support\Str; 
use Illuminate\Support\Facades\Route;
@endphp

@extends('layouts/layoutMaster')

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">المستخدمون المتصلون</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="online-users-table">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>الدور</th>
                                        <th>عنوان IP</th>
                                        <th>المتصفح</th>
                                        <th>آخر نشاط</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($onlineUsers as $session)
                                        <tr>
                                            <td>{{ $session->user->name }}</td>
                                            <td>{{ $session->user->roles->pluck('name')->implode(', ') }}</td>
                                            <td>{{ $session->ip_address }}</td>
                                            <td>{{ \Illuminate\Support\Str::limit($session->user_agent, 50) }}</td>
                                            <td>{{ $session->last_activity->diffForHumans() }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">لا يوجد مستخدمون متصلون</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">معدل وقت الاستخدام اليومي</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filters mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <select id="user-time-filter" class="form-control">
                                        <option value="">جميع المستخدمين</option>
                                        @foreach($screenTimeStats as $userId => $stats)
                                            @if(isset($usersMap[$userId]))
                                                <option value="{{ $userId }}">{{ $usersMap[$userId]->name }}</option>
                                            @endif
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select id="days-filter" class="form-control">
                                        <option value="7">آخر 7 أيام</option>
                                        <option value="30">آخر 30 يوم</option>
                                        <option value="90">آخر 90 يوم</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="screen-time-table">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>عدد الجلسات</th>
                                        <th>إجمالي الوقت (دقائق)</th>
                                        <th>متوسط الوقت يوميا (دقائق)</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($screenTimeStats as $userId => $stats)
                                        @if(isset($usersMap[$userId]))
                                            <tr>
                                                <td>{{ $usersMap[$userId]->name }}</td>
                                                <td>{{ array_sum(array_column($stats['days'], 'session_count')) }}</td>
                                                <td>{{ $stats['total_minutes'] }}</td>
                                                <td>{{ $stats['average_minutes_per_day'] }}</td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#user-time-details-{{ $userId }}">
                                                        عرض التفاصيل
                                                    </button>
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="user-time-details-{{ $userId }}" tabindex="-1" role="dialog" aria-labelledby="userTimeDetailsLabel" aria-hidden="true">
                                                        <div class="modal-dialog modal-lg" role="document">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="userTimeDetailsLabel">تفاصيل وقت الاستخدام: {{ $usersMap[$userId]->name }}</h5>
                                                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="إغلاق">
                                                                        <span aria-hidden="true">&times;</span>
                                                                    </button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <table class="table table-bordered">
                                                                        <thead>
                                                                            <tr>
                                                                                <th>التاريخ</th>
                                                                                <th>عدد الجلسات</th>
                                                                                <th>إجمالي الوقت (دقائق)</th>
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody>
                                                                            @foreach($stats['days'] as $day)
                                                                                <tr>
                                                                                    <td>{{ $day['date'] }}</td>
                                                                                    <td>{{ $day['session_count'] }}</td>
                                                                                    <td>{{ $day['total_minutes'] }}</td>
                                                                                </tr>
                                                                            @endforeach
                                                                        </tbody>
                                                                    </table>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">لا توجد بيانات وقت الاستخدام</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">الأنشطة الأخيرة</h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filters mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <select id="user-filter" class="form-control">
                                        <option value="">جميع المستخدمين</option>
                                        @foreach($onlineUsers->pluck('user')->unique('id') as $user)
                                            <option value="{{ $user->id }}">{{ $user->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select id="action-filter" class="form-control">
                                        <option value="">جميع الإجراءات</option>
                                        <option value="created">تم الإنشاء</option>
                                        <option value="updated">تم التحديث</option>
                                        <option value="deleted">تم الحذف</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="activities-table">
                                <thead>
                                    <tr>
                                        <th>المستخدم</th>
                                        <th>الإجراء</th>
                                        <th>العنصر</th>
                                        <th>الوقت</th>
                                        <th>التفاصيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($recentActivities as $activity)
                                        <tr>
                                            <td>{{ optional($activity->causer)->name ?? 'النظام' }}</td>
                                            <td>
                                                @php
                                                    $formattedDesc = $activity->description;
                                                    
                                                    // Format common activity patterns to readable Arabic text
                                                    if (Str::contains($formattedDesc, 'GET')) {
                                                        $formattedDesc = 'تم مشاهدة';
                                                    } elseif (Str::contains($formattedDesc, 'POST')) {
                                                        $formattedDesc = 'تم إنشاء';
                                                    } elseif (Str::contains($formattedDesc, 'PUT')) {
                                                        $formattedDesc = 'تم تحديث';
                                                    } elseif (Str::contains($formattedDesc, 'DELETE')) {
                                                        $formattedDesc = 'تم حذف';
                                                    }
                                                @endphp
                                                @if($formattedDesc == 'تم مشاهدة')
                                                    <span class="badge bg-primary">تم مشاهدة</span>
                                                @elseif($formattedDesc == 'تم إنشاء')
                                                    <span class="badge bg-success">تم إنشاء</span>
                                                @elseif($formattedDesc == 'تم تحديث')
                                                    <span class="badge bg-warning">تم تحديث</span>
                                                @elseif($formattedDesc == 'تم حذف')
                                                    <span class="badge bg-danger">تم حذف</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($activity->subject)
                                                    @php
                                                        $modelName = class_basename(get_class($activity->subject));
                                                        $modelId = $activity->subject->getKey();
                                                        $routeName = '';
                                                        
                                                        // Try to determine route name based on model name
                                                        switch(strtolower($modelName)) {
                                                            case 'user':
                                                                $routeName = 'admin.users.show';
                                                                break;
                                                            // Add other model types as needed
                                                            default:
                                                                $routeName = '';
                                                        }
                                                    @endphp
                                                    
                                                    @if($routeName && Route::has($routeName))
                                                        <a href="{{ route($routeName, $modelId) }}">
                                                            {{ $modelName }} #{{ $modelId }}
                                                        </a>
                                                    @else
                                                        {{ $modelName }} #{{ $modelId }}
                                                    @endif
                                                @elseif($activity->subject_type && $activity->subject_id)
                                                    {{ class_basename($activity->subject_type) }} #{{ $activity->subject_id }}
                                                @else
                                                    غير متوفر
                                                @endif
                                            </td>
                                            <td>{{ $activity->created_at->diffForHumans() }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#activity-details-{{ $activity->id }}">
                                                    عرض التفاصيل
                                                </button>
                                                <!-- Modal -->
                                                <div class="modal fade" id="activity-details-{{ $activity->id }}" tabindex="-1" role="dialog" aria-labelledby="activityDetailsLabel" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="activityDetailsLabel">تفاصيل النشاط</h5>
                                                                <button type="button" class="close" data-bs-dismiss="modal" aria-label="إغلاق">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <h6>المستخدم: {{ optional($activity->causer)->name ?? 'النظام' }}</h6>
                                                                <h6>الإجراء: 
                                                                    @php
                                                                        $formattedDesc = $activity->description;
                                                                        
                                                                        // Format common activity patterns to readable Arabic text
                                                                        if (Str::contains($formattedDesc, 'Accessed:')) {
                                                                            $formattedDesc = 'تم مشاهدة';
                                                                        } elseif (Str::contains($formattedDesc, 'updated')) {
                                                                            $formattedDesc = 'تم تحديث';
                                                                        } elseif (Str::contains($formattedDesc, 'created')) {
                                                                            $formattedDesc = 'تم إنشاء';
                                                                        } elseif (Str::contains($formattedDesc, 'deleted')) {
                                                                            $formattedDesc = 'تم حذف';
                                                                        }
                                                                    @endphp
                                                                    {{ $formattedDesc }}
                                                                </h6>
                                                                <h6>العنصر: 
                                                                    @if($activity->subject)
                                                                        {{ class_basename(get_class($activity->subject)) }} #{{ $activity->subject->getKey() }}
                                                                    @elseif($activity->subject_type && $activity->subject_id)
                                                                        {{ class_basename($activity->subject_type) }} #{{ $activity->subject_id }}
                                                                    @else
                                                                        غير متوفر
                                                                    @endif
                                                                </h6>
                                                                <h6>الوقت: {{ $activity->created_at->format('Y-m-d H:i:s') }}</h6>
                                                                @if($activity->properties->count() > 0)
                                                                    <h6>الخصائص:</h6>
                                                                    <pre>{{ json_encode($activity->properties, JSON_PRETTY_PRINT) }}</pre>
                                                                @endif
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">لا توجد أنشطة</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Initialize DataTables
        $('#online-users-table').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
        
        $('#screen-time-table').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
        
        const activitiesTable = $('#activities-table').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
        });
        
        // Filter screen time stats
        $('#user-time-filter, #days-filter').on('change', function() {
            const userId = $('#user-time-filter').val();
            const days = $('#days-filter').val();
            
            $.ajax({
                url: '{{ route("admin.user-tracker.screen-time") }}',
                type: 'GET',
                data: {
                    user_id: userId,
                    days: days
                },
                success: function(response) {
                    const screenTimeTable = $('#screen-time-table').DataTable();
                    screenTimeTable.clear();
                    
                    if (Object.keys(response.screen_time_stats).length > 0) {
                        Object.keys(response.screen_time_stats).forEach(userId => {
                            const stats = response.screen_time_stats[userId];
                            const user = response.users[userId];
                            
                            if (user) {
                                const totalSessions = Object.values(stats.days).reduce((sum, day) => sum + parseInt(day.session_count), 0);
                                
                                screenTimeTable.row.add([
                                    user.name,
                                    totalSessions,
                                    stats.total_minutes,
                                    stats.average_minutes_per_day,
                                    '<button type="button" class="btn btn-sm btn-info">عرض التفاصيل</button>'
                                ]).draw();
                            }
                        });
                    } else {
                        screenTimeTable.row.add([
                            '<td colspan="5" class="text-center">لا توجد بيانات وقت الاستخدام</td>',
                            '', '', '', ''
                        ]).draw();
                    }
                }
            });
        });
        
        // Filter activities
        $('#user-filter, #action-filter').on('change', function() {
            const userId = $('#user-filter').val();
            const action = $('#action-filter').val();
            
            // AJAX request to get filtered activities
            $.ajax({
                url: '{{ route("admin.user-tracker.activities") }}',
                type: 'GET',
                data: {
                    user_id: userId,
                    action: action
                },
                success: function(response) {
                    // Clear table
                    activitiesTable.clear();
                    
                    // Add new data
                    if (response.activities.length > 0) {
                        response.activities.forEach(activity => {
                            const userName = activity.causer ? activity.causer.name : 'النظام';
                            let subjectDisplay = 'غير متوفر';
                            
                            if (activity.subject_type) {
                                const modelName = activity.subject_type.split('\\').pop();
                                if (activity.subject_id) {
                                    subjectDisplay = `${modelName} #${activity.subject_id}`;
                                } else {
                                    subjectDisplay = modelName;
                                }
                            }
                            
                            // Format description
                            let formattedDesc = activity.description;
                            if (formattedDesc.includes('Accessed:')) {
                                formattedDesc = 'تم مشاهدة';
                            } else if (formattedDesc.includes('updated')) {
                                formattedDesc = 'تم تحديث';
                            } else if (formattedDesc.includes('created')) {
                                formattedDesc = 'تم إنشاء';
                            } else if (formattedDesc.includes('deleted')) {
                                formattedDesc = 'تم حذف';
                            }
                            
                            const createdAt = new Date(activity.created_at).toLocaleString();
                            
                            activitiesTable.row.add([
                                userName,
                                formattedDesc,
                                subjectDisplay,
                                createdAt,
                                `<button type="button" class="btn btn-sm btn-info">عرض التفاصيل</button>`
                            ]).draw();
                        });
                    } else {
                        activitiesTable.row.add([
                            '<td colspan="5" class="text-center">لا توجد أنشطة</td>',
                            '', '', '', ''
                        ]).draw();
                    }
                }
            });
        });
        
    });
</script>
@endsection 