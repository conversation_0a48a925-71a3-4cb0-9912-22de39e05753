<?php

return [
    'name' => 'UserTracker',
    
    /*
    |--------------------------------------------------------------------------
    | Online Status Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the user online status tracking settings.
    |
    */
    'online_status' => [
        // Minutes to consider a user still online after their last activity
        'timeout' => 10,
    ],
    
    /*
    |--------------------------------------------------------------------------
    | Session Tracking Configuration
    |--------------------------------------------------------------------------
    |
    | Here you can configure the session tracking behavior.
    |
    */
    'session_tracking' => [
        // Minutes interval for creating new sessions
        'interval' => 15,
    ],
]; 