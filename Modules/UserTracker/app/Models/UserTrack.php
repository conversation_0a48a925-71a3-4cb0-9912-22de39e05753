<?php

namespace Modules\UserTracker\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;

class UserTrack extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'ip_address',
        'user_agent',
        'last_activity',
        'session_start',
        'session_end',
        'is_active',
    ];

    protected $casts = [
        'last_activity' => 'datetime',
        'session_start' => 'datetime',
        'session_end' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Calculate the average session time per day for a user.
     *
     * @param int|null $userId The user ID to calculate for (null for all users)
     * @param int $days Number of days to look back
     * @return array
     */
    public static function getAverageTimeOnScreenPerDay($userId = null, $days = 7)
    {
        $connection = DB::connection()->getPdo()->getAttribute(\PDO::ATTR_DRIVER_NAME);
        $isSqlite = ($connection === 'sqlite');
        
        if ($isSqlite) {
            // SQLite compatible query
            $query = self::selectRaw('
                    user_id,
                    date(session_start) as date,
                    COUNT(*) as session_count,
                    SUM((julianday(COALESCE(session_end, last_activity)) - julianday(session_start)) * 1440) as total_minutes
                ')
                ->whereNotNull('session_start')
                ->where('session_start', '>=', now()->subDays($days))
                ->groupBy('user_id', 'date');
        } else {
            // MySQL/PostgreSQL compatible query
            $query = self::selectRaw('
                    user_id,
                    DATE(session_start) as date,
                    COUNT(*) as session_count,
                    SUM(TIMESTAMPDIFF(MINUTE, session_start, COALESCE(session_end, last_activity))) as total_minutes
                ')
                ->whereNotNull('session_start')
                ->where('session_start', '>=', now()->subDays($days))
                ->groupBy('user_id', 'date');
        }
            
        if ($userId) {
            $query->where('user_id', $userId);
        }
        
        $sessions = $query->get();
        
        $result = [];
        foreach ($sessions as $session) {
            if (!isset($result[$session->user_id])) {
                $result[$session->user_id] = [
                    'user_id' => $session->user_id, 
                    'total_minutes' => 0,
                    'days' => [],
                    'average_minutes_per_day' => 0
                ];
            }
            
            $result[$session->user_id]['days'][$session->date] = [
                'date' => $session->date,
                'session_count' => $session->session_count,
                'total_minutes' => $session->total_minutes
            ];
            
            $result[$session->user_id]['total_minutes'] += $session->total_minutes;
        }
        
        // Calculate average per day
        foreach ($result as $userId => $data) {
            $activeDays = count($data['days']);
            $result[$userId]['average_minutes_per_day'] = $activeDays > 0 ? 
                round($data['total_minutes'] / $activeDays, 1) : 0;
        }
        
        return $result;
    }
} 