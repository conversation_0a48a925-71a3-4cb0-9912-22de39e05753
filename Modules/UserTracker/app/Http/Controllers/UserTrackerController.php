<?php

namespace Modules\UserTracker\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\UserTracker\app\Models\UserTrack;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class UserTrackerController extends Controller
{
    /**
     * Display the user tracking dashboard.
     */
    public function index()
    {
        // Check if user is a superadmin
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $onlineUsers = $this->getOnlineUsers();
        $recentActivities = $this->getRecentActivities();
        $screenTimeStats = UserTrack::getAverageTimeOnScreenPerDay(null, 7);
        
        // Get user information for the screen time stats
        $userIds = array_keys($screenTimeStats);
        $usersMap = [];
        
        if (!empty($userIds)) {
            $users = User::whereIn('id', $userIds)->get();
            foreach ($users as $user) {
                $usersMap[$user->id] = $user;
            }
        }
        
        return view('usertracker::index', compact('onlineUsers', 'recentActivities', 'screenTimeStats', 'usersMap'));
    }

    /**
     * Get all currently online users.
     */
    public function onlineUsers()
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $onlineUsers = $this->getOnlineUsers();
        
        return response()->json([
            'users' => $onlineUsers,
        ]);
    }

    /**
     * Get all recent activities.
     */
    public function activities(Request $request)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $limit = $request->input('limit', 50);
        $userId = $request->input('user_id');
        $action = $request->input('action');
        
        $activities = $this->getRecentActivities($limit, $userId, $action);
        
        return response()->json([
            'activities' => $activities,
        ]);
    }

    /**
     * Get screen time statistics.
     */
    public function screenTime(Request $request)
    {
        if (!Auth::user()->isSuperAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $userId = $request->input('user_id');
        $days = $request->input('days', 7);
        
        $screenTimeStats = UserTrack::getAverageTimeOnScreenPerDay($userId, $days);
        
        // Get user information
        $userIds = array_keys($screenTimeStats);
        $usersMap = [];
        
        if (!empty($userIds)) {
            $users = User::whereIn('id', $userIds)->get(['id', 'name', 'email']);
            foreach ($users as $user) {
                $usersMap[$user->id] = $user;
            }
        }
        
        return response()->json([
            'screen_time_stats' => $screenTimeStats,
            'users' => $usersMap,
        ]);
    }

    /**
     * Get online users from database.
     */
    private function getOnlineUsers()
    {
        $timeout = Config::get('usertracker.online_status.timeout', 10);
        
        return UserTrack::where('last_activity', '>=', now()->subMinutes($timeout))
            ->where('is_active', true)
            ->with('user')
            ->get();
    }

    /**
     * Get recent activities from database.
     */
    private function getRecentActivities($limit = 50, $userId = null, $action = null)
    {
        $query = Activity::with('causer')
            ->orderBy('created_at', 'desc');
            
        if ($userId) {
            $query->where('causer_id', $userId);
        }
        
        if ($action) {
            $query->where('description', 'LIKE', "%{$action}%");
        }
        
        return $query->limit($limit)->get();
    }
} 