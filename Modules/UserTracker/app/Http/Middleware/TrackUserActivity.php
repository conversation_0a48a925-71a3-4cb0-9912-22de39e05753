<?php

namespace Modules\UserTracker\app\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\UserTracker\app\Models\UserTrack;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Carbon;

class TrackUserActivity
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        if (Auth::check()) {
            $user = Auth::user();
            if (!$user->isSuperAdmin()) {
                $this->trackUserTrack($user, $request);

                $action = request()->route()->getActionName();
                $method = request()->method();
                $url = request()->fullUrl();
                $routeName = request()->route()->getName() ?? '';
                $routeParams = request()->route()->parameters();
                $modelId = null;
                $subjectModel = null;
                
                // Try to extract model ID and model from route parameters
                if (!empty($routeParams)) {
                    // Most routes have parameter like 'user' => User object or 'post' => 1
                    foreach ($routeParams as $key => $param) {
                        if (is_object($param) && method_exists($param, 'getKey')) {
                            $modelId = $param->getKey();
                            $subjectModel = $param;
                            break;
                        } elseif (is_numeric($param)) {
                            $modelId = $param;
                            // We don't have the model object for numeric IDs
                            break;
                        }
                    }
                }

                // If we have subject model, use performedOn to record subject
                $activity = activity()
                    ->causedBy(Auth::user());
                
                if ($subjectModel) {
                    $activity = $activity->performedOn($subjectModel);
                }
                
                $activity->withProperties([
                        'method' => $method,
                        'url' => $url,
                        'action' => $action,
                        'ip' => request()->ip(),
                        'route_name' => $routeName,
                        'route_params' => $routeParams,
                        'model_id' => $modelId,
                    ])
                    ->log("Accessed: {$action} ({$method})");
            }
        }

        return $response;
    }

    /**
     * Track user session with 15-minute intervals
     * 
     * @param \App\Models\User $user
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function trackUserTrack($user, $request): void
    {
        // Check for active session
        $activeSession = UserTrack::where('user_id', $user->id)
            ->where('is_active', true)
            ->latest('last_activity')
            ->first();
            
        $now = Carbon::now();
        
        if ($activeSession) {
            // If last activity was more than 15 minutes ago, create new session
            if ($activeSession->last_activity->diffInMinutes($now) >= 15) {
                // Close current session
                $activeSession->update([
                    'is_active' => false,
                    'session_end' => $now,
                ]);
                
                // Create new session
                UserTrack::create([
                    'user_id' => $user->id,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'last_activity' => $now,
                    'session_start' => $now,
                    'is_active' => true,
                ]);
            } else {
                // Just update last activity
                $activeSession->update([
                    'last_activity' => $now,
                ]);
            }
        } else {
            // Create new session if no active session exists
            UserTrack::create([
                'user_id' => $user->id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'last_activity' => $now,
                'session_start' => $now,
                'is_active' => true,
            ]);
        }
    }
}
