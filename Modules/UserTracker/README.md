# UserTracker Module

This module allows superadmins to track all other admins/operators' dashboard access and actions they perform in real-time.

## Features

- Track who is online (live dashboard presence)
- Log all admin/operator actions (CRUD, access, etc.)
- Dashboard for superAdmin to view online users and recent actions
- Real-time updates with Laravel Echo (optional)

## Installation

1. Install the module's dependencies:

```bash
composer require spatie/laravel-activitylog
```

2. Publish and run migrations:

```bash
php artisan vendor:publish --provider="Spatie\Activitylog\ActivitylogServiceProvider"
php artisan migrate
```

3. Add the activity logging trait to your User model:

```php
use Spatie\Activitylog\Traits\LogsActivity;

class User extends Authenticatable
{
    use LogsActivity;

    protected static $logAttributes = ['name', 'email']; // or ['*'] for all
    protected static $logOnlyDirty = true;
}
```

4. Register the TrackUserActivity middleware in your `app/Http/Kernel.php`:

```php
protected $middlewareGroups = [
    'web' => [
        // ... other middleware
        \Modules\UserTracker\app\Http\Middleware\TrackUserActivity::class,
    ],
];
```

## Usage

1. For custom activity logging:

```php
activity()
   ->causedBy(auth()->user())
   ->log('Deleted user #' . $user->id);
```

2. For real-time broadcasting:

```php
event(new \Modules\UserTracker\app\Events\AdminActionPerformed(auth()->user(), 'Deleted User #'.$user->id));
```

3. Access the dashboard at: `/admin/user-tracker`

## Requirements

- Laravel 10+
- PHP 8.1+
- Spatie Activity Log package
- Laravel Echo (optional, for real-time updates) 