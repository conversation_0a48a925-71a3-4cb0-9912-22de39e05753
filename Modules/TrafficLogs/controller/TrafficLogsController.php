<?php

namespace Modules\TrafficLogs\controller;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use Modules\TrafficLogs\Models\TrafficLogs;
use Modules\TrafficLogs\Models\TrafficLogsDetails;

class TrafficLogsController extends Controller
{
    public function index(Request $request): View
    {
        $list = TrafficLogs::with('details')->orderBy('id', 'desc')->paginate(10);
        return view('modules.traffic.index', compact('list'));
    }
    public function logs(Request $request): View
    {
        $trafficLog = TrafficLogs::where('id', $request->id)->orderBy('id', 'desc')->first();
        $list = TrafficLogsDetails::where('traffic_logs_id', $request->id)->orderBy('id', 'desc')->paginate(10);
        return view('modules.traffic.logs', compact('list', 'trafficLog'));
    }
}
