<?php

namespace Modules\TrafficLogs\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrafficLogs extends Model
{
    use HasFactory;

    protected $guarded = [];
    public function details()
    {
        return $this->hasMany(TrafficLogsDetails::class);
    }
    public function user()
    {
        return $this->belongsTo('\App\Models\User');
    }
}
