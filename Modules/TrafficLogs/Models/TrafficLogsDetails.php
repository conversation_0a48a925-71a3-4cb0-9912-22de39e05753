<?php

namespace Modules\TrafficLogs\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrafficLogsDetails extends Model
{
    use HasFactory;
    protected $guarded = ['id', 'created_at', 'updated_at'];
    public function traffic_logs()
    {
        return $this->belongsTo(TrafficLogs::class);
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
