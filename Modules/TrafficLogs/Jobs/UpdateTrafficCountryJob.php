<?php

namespace Modules\TrafficLogs\Jobs;

use App\Helpers\UserSystemInfoHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\TrafficLogs\Models\TrafficLogs;

class UpdateTrafficCountryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 30;

    /**
     * The traffic log ID to update.
     *
     * @var int
     */
    protected $trafficLogId;

    /**
     * The IP address to look up.
     *
     * @var string
     */
    protected $ipAddress;

    /**
     * Create a new job instance.
     *
     * @param int $trafficLogId
     * @param string $ipAddress
     * @return void
     */
    public function __construct(int $trafficLogId, string $ipAddress)
    {
        $this->trafficLogId = $trafficLogId;
        $this->ipAddress = $ipAddress;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // Get the traffic log record
            $trafficLog = TrafficLogs::find($this->trafficLogId);
            
            if (!$trafficLog) {
                Log::warning('Traffic log not found for ID: ' . $this->trafficLogId);
                return;
            }

            // Get country information from IP
            $countryInfo = (new UserSystemInfoHelper())->get_country_from_ip($this->ipAddress);
            
            // Update the traffic log record with country information
            $trafficLog->country = $countryInfo['country'] ?? 'Unknown';
            $trafficLog->save();
            
        } catch (\Exception $e) {
            Log::error('Failed to update country information for traffic log: ' . $e->getMessage());
            
            // If we have retries left, throw the exception to trigger a retry
            if ($this->attempts() < $this->tries) {
                throw $e;
            }
        }
    }
}
