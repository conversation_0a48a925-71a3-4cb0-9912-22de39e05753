<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('traffic_logs_details', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('traffic_logs_id')->nullable();
            $table->foreign('traffic_logs_id')->references('id')->on("traffic_logs")->onDelete('cascade');
            $table->string('prev_link')->nullable()->index();
            $table->string('url')->nullable()->index();
            $table->string('ip')->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('traffic_logs_details');
    }
};
