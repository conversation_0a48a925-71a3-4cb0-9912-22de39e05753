<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('under_attacks', function (Blueprint $table) {
            $table->id();
            $table->string('status')->default('UNDER_ATTACK');
            $table->text('note')->nullable();
            $table->timestamp('release_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('under_attacks');
    }
};
