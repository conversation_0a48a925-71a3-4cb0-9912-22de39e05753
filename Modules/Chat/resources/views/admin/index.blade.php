@extends('layouts/layoutMaster')

@section('content')
<div class="container-fluid py-5">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2 fw-bold text-primary">مراقبة المحادثات</h1>
            </div>

            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="bg-light">
                                <tr>
                                    <th class="py-3">رقم المحادثة</th>
                                    <th class="py-3">المرسل</th>
                                    <th class="py-3">المستلم</th>
                                    <th class="py-3">آخر رسالة</th>
                                    <th class="py-3">آخر نشاط</th>
                                    <th class="py-3">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($chats as $chat)
                                    <tr>
                                        <td class="py-3">{{ $chat->id }}</td>
                                        <td class="py-3">
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ $chat->sender->name }}</h6>
                                                    <div class="btn-group">
                                                        <form action="{{ route('admin.users.report', $chat->sender) }}" method="POST" class="me-1">
                                                            @csrf
                                                            <button type="submit" class="btn btn-soft-warning btn-sm rounded-pill px-3" 
                                                                onclick="return confirm('هل أنت متأكد من الإبلاغ عن هذا المستخدم؟')">
                                                                <i class="ti ti-flag me-1"></i> إبلاغ
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('admin.users.block', $chat->sender) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="btn btn-soft-danger btn-sm rounded-pill px-3" 
                                                                onclick="return confirm('هل أنت متأكد من حظر هذا المستخدم؟')">
                                                                <i class="ti ti-ban me-1"></i> حظر
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            <div class="d-flex align-items-center">
                                                <div>
                                                    <h6 class="mb-1">{{ $chat->receiver->name }}</h6>
                                                    <div class="btn-group">
                                                        <form action="{{ route('admin.users.report', $chat->receiver) }}" method="POST" class="me-1">
                                                            @csrf
                                                            <button type="submit" class="btn btn-soft-warning btn-sm rounded-pill px-3" 
                                                                onclick="return confirm('هل أنت متأكد من الإبلاغ عن هذا المستخدم؟')">
                                                                <i class="ti ti-flag me-1"></i> إبلاغ
                                                            </button>
                                                        </form>
                                                        <form action="{{ route('admin.users.block', $chat->receiver) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="btn btn-soft-danger btn-sm rounded-pill px-3" 
                                                                onclick="return confirm('هل أنت متأكد من حظر هذا المستخدم؟')">
                                                                <i class="ti ti-ban me-1"></i> حظر
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="py-3">
                                            @if($chat->lastMessage)
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;">
                                                    {{ \Illuminate\Support\Str::limit($chat->lastMessage->message, 30) }}
                                                </span>
                                            @else
                                                <span class="text-muted">لا توجد رسائل</span>
                                            @endif
                                        </td>
                                        <td class="py-3">{{ $chat->last_message_at ? $chat->last_message_at->diffForHumans() : 'غير متوفر' }}</td>
                                        <td class="py-3">
                                            <div class="btn-group">
                                                <a href="{{ route('admin.chats.show', $chat) }}" class="btn btn-soft-primary btn-sm rounded-pill px-3 me-1">
                                                    <i class="ti ti-eye me-1"></i> عرض
                                                </a>
                                                <form action="{{ route('admin.chats.destroy', $chat) }}" method="POST">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-soft-danger btn-sm rounded-pill px-3" 
                                                        onclick="return confirm('هل أنت متأكد من حذف هذه المحادثة؟')">
                                                        <i class="ti ti-trash me-1"></i> حذف
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="ti ti-message-circle-off fs-2 mb-2"></i>
                                                <p>لا توجد محادثات</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center p-4">
                        {{ $chats->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-soft-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--bs-primary);
}
.btn-soft-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
}
.btn-soft-warning {
    background-color: rgba(var(--bs-warning-rgb), 0.1);
    color: var(--bs-warning);
}
</style>
@endsection 