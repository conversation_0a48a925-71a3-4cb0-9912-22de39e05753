@extends('layouts/layoutMaster')

@section('content')
<div class="container-fluid py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 fw-bold text-primary">تفاصيل المحادثة</h1>
        <div>
            <a href="{{ route('admin.chats.index') }}" class="btn btn-soft-secondary rounded-pill px-4">
                <i class="ti ti-arrow-right me-1"></i> العودة للمحادثات
            </a>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-4">
            <div class="card shadow-sm border-0 rounded-lg mb-4">
                <div class="card-header bg-transparent border-bottom">
                    <h5 class="card-title mb-0 fw-semibold">معلومات المحادثة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">رقم المحادثة</h6>
                        <p class="mb-0 fs-5">{{ $chat->id }}</p>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">تاريخ الإنشاء</h6>
                        <p class="mb-0">{{ $chat->created_at->format('Y-m-d H:i:s') }}</p>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">آخر نشاط</h6>
                        <p class="mb-0">{{ $chat->last_message_at ? $chat->last_message_at->format('Y-m-d H:i:s') : 'غير متوفر' }}</p>
                    </div>
                </div>
            </div>

            @if($chat->product_id && $chat->product)
            <div class="card shadow-sm border-0 rounded-lg mb-4">
                <div class="card-header bg-transparent border-bottom">
                    <h5 class="card-title mb-0 fw-semibold">معلومات المنتج</h5>
                </div>
                <div class="card-body">
                    @if($chat->product->primaryMediaUrl())
                    <div class="text-center mb-3">
                        <img src="{{ $chat->product->primaryMediaUrl() }}" alt="{{ $chat->product->title }}"
                             class="img-fluid rounded" style="max-height: 150px;">
                    </div>
                    @endif
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">عنوان المنتج</h6>
                        <p class="mb-0 fs-5">{{ $chat->product->title }}</p>
                    </div>
                    @if($chat->product->price)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">السعر</h6>
                        <p class="mb-0 fs-5 text-primary">{{ $chat->product->price }}</p>
                    </div>
                    @endif
                    @if($chat->product->desc)
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">الوصف</h6>
                        <p class="mb-0">{{ \Illuminate\Support\Str::limit($chat->product->desc, 100) }}</p>
                    </div>
                    @endif
                    <div class="d-grid">
                        <a href="{{ route('products.show', $chat->product->id) }}" target="_blank"
                           class="btn btn-soft-primary rounded-pill">
                            <i class="ti ti-external-link me-1"></i> عرض المنتج
                        </a>
                    </div>
                </div>
            </div>
            @endif

            <div class="card shadow-sm border-0 rounded-lg mb-4">
                <div class="card-header bg-transparent border-bottom">
                    <h5 class="card-title mb-0 fw-semibold">معلومات المرسل</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">الاسم</h6>
                        <p class="mb-0 fs-5">{{ $chat->sender->name }}</p>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">البريد الإلكتروني</h6>
                        <p class="mb-0">{{ $chat->sender->email }}</p>
                    </div>
                    <div class="d-grid gap-3">
                        <form action="{{ route('admin.users.report', $chat->sender) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-soft-warning w-100 rounded-pill"
                                onclick="return confirm('هل أنت متأكد من الإبلاغ عن هذا المستخدم؟')">
                                <i class="ti ti-flag me-1"></i> إبلاغ عن المستخدم
                            </button>
                        </form>
                        <form action="{{ route('admin.users.block', $chat->sender) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-soft-danger w-100 rounded-pill"
                                onclick="return confirm('هل أنت متأكد من حظر هذا المستخدم؟')">
                                <i class="ti ti-ban me-1"></i> حظر المستخدم
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-header bg-transparent border-bottom">
                    <h5 class="card-title mb-0 fw-semibold">معلومات المستلم</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">الاسم</h6>
                        <p class="mb-0 fs-5">{{ $chat->receiver->name }}</p>
                    </div>
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">البريد الإلكتروني</h6>
                        <p class="mb-0">{{ $chat->receiver->email }}</p>
                    </div>
                    <div class="d-grid gap-3">
                        <form action="{{ route('admin.users.report', $chat->receiver) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-soft-warning w-100 rounded-pill"
                                onclick="return confirm('هل أنت متأكد من الإبلاغ عن هذا المستخدم؟')">
                                <i class="ti ti-flag me-1"></i> إبلاغ عن المستخدم
                            </button>
                        </form>
                        <form action="{{ route('admin.users.block', $chat->receiver) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-soft-danger w-100 rounded-pill"
                                onclick="return confirm('هل أنت متأكد من حظر هذا المستخدم؟')">
                                <i class="ti ti-ban me-1"></i> حظر المستخدم
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-header bg-transparent border-bottom">
                    <h5 class="card-title mb-0 fw-semibold">الرسائل</h5>
                </div>
                <div class="card-body">
                    <div class="chat-messages p-4">
                        @forelse($chat->messages as $message)
                            @php
                                $otherUser = $message->user_id == $chat->sender_id ? $chat->receiver : $chat->sender;
                            @endphp
                            <div class="mb-4 {{ $message->user_id == $chat->sender_id ? 'text-start' : 'text-end' }}">
                                <div class="d-flex {{ $message->user_id == $chat->sender_id ? '' : 'flex-row-reverse' }} align-items-start">
                                    <div class="flex-shrink-0">
                                       <div class="avatar">
                                    <div class="w-10 h-10 rounded-full">
                                        <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                    </div>
                                </div>
                                    <div class="flex-grow-1 {{ $message->user_id == $chat->sender_id ? 'ms-3' : 'me-3' }}">
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="fw-semibold">{{ $message->user->name }}</span>
                                            <small class="text-muted mx-2">{{ $message->created_at->format('M d, Y H:i') }}</small>
                                            <form action="{{ route('admin.messages.delete', $message) }}" method="POST">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-soft-danger btn-sm rounded-pill px-3"
                                                    onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                                    <i class="ti ti-trash me-1"></i> حذف
                                                </button>
                                            </form>
                                        </div>

                                        @if($message->isText())
                                        <div class="message-content p-3 rounded-3
                                            {{ $message->user_id == $chat->sender_id ? 'bg-light' : 'bg-primary text-white' }}">
                                            {{ $message->content }}
                                        </div>
                                        @elseif($message->isImage())
                                        <div class="message-content p-2 rounded-3
                                            {{ $message->user_id == $chat->sender_id ? 'bg-light' : 'bg-primary' }}">
                                            <img src="{{ asset('storage/' . $message->file_path) }}" alt="Image"
                                                 class="img-fluid rounded" style="max-width: 100%; max-height: 300px;">
                                        </div>
                                        @elseif($message->isAudio())
                                        <div class="message-content p-3 rounded-3
                                            {{ $message->user_id == $chat->sender_id ? 'bg-light' : 'bg-primary text-white' }}">
                                            <audio controls class="w-100">
                                                <source src="{{ asset('storage/' . $message->file_path) }}" type="{{ $message->mime_type }}">
                                                Your browser does not support the audio element.
                                            </audio>
                                        </div>
                                        @elseif($message->isVideo())
                                        <div class="message-content p-2 rounded-3
                                            {{ $message->user_id == $chat->sender_id ? 'bg-light' : 'bg-primary' }}">
                                            <video controls class="w-100" style="max-height: 300px;">
                                                <source src="{{ asset('storage/' . $message->file_path) }}" type="{{ $message->mime_type }}">
                                                Your browser does not support the video element.
                                            </video>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="text-center text-muted py-5">
                                <i class="ti ti-message-circle-off fs-1 mb-3 d-block"></i>
                                <p class="mb-0">لا توجد رسائل في هذه المحادثة</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.btn-soft-primary {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--bs-primary);
}
.btn-soft-secondary {
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
    color: var(--bs-secondary);
}
.btn-soft-danger {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--bs-danger);
}
.btn-soft-warning {
    background-color: rgba(var(--bs-warning-rgb), 0.1);
    color: var(--bs-warning);
}
.message-content {
    max-width: 80%;
    display: inline-block;
}
.chat-messages {
    max-height: 600px;
    overflow-y: auto;
}
</style>
@endsection