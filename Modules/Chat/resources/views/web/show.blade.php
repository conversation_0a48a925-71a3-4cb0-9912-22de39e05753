@extends('web.layouts.layout')
@section('title', __('محادثة مع') . ' ' . ($chat->sender_id == auth()->id() ? $chat->receiver->name : $chat->sender->name))

@section('page-meta')
<meta name="description" content="{{ __('محادثة في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, محادثات, رسائل, دردشة') }}">
@endsection

@section('page-style')
<style>
    .chat-container {
        height: calc(100vh - 300px);
        min-height: 500px;
    }
    .chat-sidebar {
        width: 280px;
        border-right: 1px solid hsl(var(--b3));
    }
    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 1rem;
    }
    .message-bubble {
        max-width: 80%;
        padding: 0.75rem 1rem;
        border-radius: 1.2rem;
        margin-bottom: 0.5rem;
        position: relative;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
        word-break: break-word;
        overflow-wrap: break-word;
    }
    .message-bubble:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    }
    .message-bubble img {
        border-radius: 0.8rem;
        margin-bottom: 0.5rem;
        max-width: 300px;
        max-height: 250px;
        object-fit: contain;
    }
    .message-bubble.sent {
        background-color: #1e88e5; /* Blue color for own messages */
        color: white;
        border-bottom-right-radius: 0.3rem;
        margin-left: auto;
        margin-right: 0;
        text-align: right;
    }
    .message-bubble.received {
        background-color: hsl(var(--b2));
        color: hsl(var(--bc));
        border-bottom-left-radius: 0.3rem;
        margin-right: auto;
        margin-left: 0;
        text-align: left;
    }
    .message-actions {
        position: absolute;
        top: 0.5rem;
        opacity: 0;
        transition: opacity 0.2s ease;
    }
    .message-bubble:hover .message-actions {
        opacity: 1;
    }
    .sent .message-actions {
        left: 0.5rem;
    }
    .received .message-actions {
        right: 0.5rem;
    }
    .message-time {
        font-size: 0.7rem;
        opacity: 0.7;
        margin-top: 0.25rem;
    }
    .items-end .message-time {
        text-align: right;
    }
    .items-start .message-time {
        text-align: left;
    }
    .chat-input {
        border-top: 1px solid hsl(var(--b3));
        padding: 1rem;
    }
    .chat-list-item {
        transition: all 0.2s ease;
    }
    .chat-list-item:hover {
        background-color: rgba(var(--p), 0.1);
    }
    .chat-list-item.active {
        background-color: rgba(var(--p), 0.15);
        border-right: 3px solid hsl(var(--p));
    }
    .unread-badge {
        min-width: 20px;
        height: 20px;
        border-radius: 10px;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li><a href="{{ route('web.chat.index') }}">{{ __('المحادثات') }}</a></li>
                <li>{{ $chat->sender_id == auth()->id() ? $chat->receiver->name : $chat->sender->name }}</li>
            </ul>
        </div>

        <div class="bg-base-100 rounded-lg shadow-md overflow-hidden">
            <div class="flex flex-col md:flex-row">
                <!-- Chat Sidebar (only on desktop) -->
                @if($otherChats->count() > 0)
                <div class="chat-sidebar hidden md:block">
                    <div class="p-4 border-b border-base-300">
                        <h2 class="font-bold">{{ __('المحادثات الأخرى') }}</h2>
                    </div>
                    <div class="overflow-y-auto" style="max-height: calc(100vh - 350px);">
                        @foreach($otherChats as $otherChat)
                            @php
                                $otherUser = $otherChat->sender_id == auth()->id() ? $otherChat->receiver : $otherChat->sender;
                                $unreadCount = $otherChat->messages->where('user_id', '!=', auth()->id())->where('is_read', false)->count();
                            @endphp
                            <a href="{{ route('web.chat.show', $otherChat->id) }}" class="chat-list-item flex items-center p-3 border-b border-base-200">
                                <div class="avatar">
                                    <div class="w-10 h-10 rounded-full">
                                        <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                    </div>
                                </div>
                                <div class="ml-3 rtl:mr-3 rtl:ml-0 flex-1">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-sm">{{ $otherUser->name }}</h3>
                                        @if($unreadCount > 0)
                                            <div class="unread-badge bg-primary text-primary-content">{{ $unreadCount }}</div>
                                        @endif
                                    </div>
                                    <p class="text-xs text-base-content/70 truncate max-w-[150px]">
                                        @if($otherChat->lastMessage)
                                            {{ $otherChat->lastMessage->content }}
                                        @else
                                            <span class="italic">{{ __('لا توجد رسائل') }}</span>
                                        @endif
                                    </p>
                                </div>
                            </a>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Chat Main Area -->
                <div class="flex-1 flex flex-col chat-container">
                    <!-- Chat Header -->
                    <div class="p-4 border-b border-base-300 flex items-center justify-between">
                        <div class="flex items-center">
                            @php
                                $otherUser = $chat->sender_id == auth()->id() ? $chat->receiver : $chat->sender;
                            @endphp
                            <div class="avatar">
                                <div class="w-10 h-10 rounded-full">
                                    <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                </div>
                            </div>
                            <div class="ml-3 rtl:mr-3 rtl:ml-0">
                                <h2 class="font-bold">{{ $otherUser->name }}</h2>
                                @if($chat->product)
                                    <a href="{{ route('web.product', $chat->product->slug) }}" class="text-xs text-primary hover:underline">
                                        {{ __('منتج') }}: {{ $chat->product->title }}
                                    </a>
                                @endif
                            </div>
                        </div>
                        <a href="{{ route('web.sellerProfile', $otherUser->username) }}" class="btn btn-sm btn-outline">
                            <i class="fas fa-user mr-1 rtl:ml-1 rtl:mr-0"></i> {{ __('الملف الشخصي') }}
                        </a>
                    </div>

                    <!-- Chat Messages -->
                    <div class="chat-messages" id="chat-messages">
                        @if($chat->messages->count() > 0)
                            @foreach($chat->messages as $message)
                                <div class="flex flex-col {{ $message->user_id == auth()->id() ? 'items-end' : 'items-start' }} mb-4">
                                    <div class="message-bubble {{ $message->user_id == auth()->id() ? 'sent' : 'received' }}">
                                        <!-- Message Actions -->
                                        @if($message->user_id != auth()->id())
                                            <div class="message-actions">
                                                <div class="dropdown dropdown-end">
                                                    <label tabindex="0" class="btn btn-xs btn-ghost btn-circle">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </label>
                                                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                                        <li>
                                                            <a href="#" class="report-message" data-message-id="{{ $message->id }}" data-bs-toggle="modal" data-bs-target="#reportModal">
                                                                <i class="fas fa-flag text-error"></i> {{ __('الإبلاغ عن مخالفة') }}
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        @endif

                                        <!-- Message Content -->
                                        @if($message->type == 'text')
                                            {{ $message->content }}
                                        @elseif($message->type == 'image')
                                            <img src="{{ asset('storage/' . $message->file_path) }}" alt="Image" class="max-w-full rounded-lg max-h-64" style="max-width: 300px; object-fit: contain;">
                                            @if($message->content)
                                                <p class="mt-2">{{ $message->content }}</p>
                                            @endif
                                        @elseif($message->type == 'audio')
                                            <audio controls class="w-full">
                                                <source src="{{ asset('storage/' . $message->file_path) }}" type="audio/mpeg">
                                                {{ __('متصفحك لا يدعم تشغيل الصوت') }}
                                            </audio>
                                            @if($message->content)
                                                <p class="mt-2">{{ $message->content }}</p>
                                            @endif
                                        @elseif($message->type == 'video')
                                            <video controls class="max-w-full rounded-lg">
                                                <source src="{{ asset('storage/' . $message->file_path) }}" type="video/mp4">
                                                {{ __('متصفحك لا يدعم تشغيل الفيديو') }}
                                            </video>
                                            @if($message->content)
                                                <p class="mt-2">{{ $message->content }}</p>
                                            @endif
                                        @endif
                                    </div>
                                    <div class="message-time">
                                        {{ $message->created_at->format('h:i A') }}
                                        @if($message->user_id == auth()->id())
                                            <i class="fas fa-check {{ $message->is_read ? 'text-primary' : 'text-base-content/50' }} ml-1 rtl:mr-1 rtl:ml-0"></i>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-12">
                                <i class="far fa-comments text-6xl text-base-content/30 mb-4"></i>
                                <h2 class="text-xl font-bold mb-2">{{ __('لا توجد رسائل') }}</h2>
                                <p class="text-base-content/70">{{ __('ابدأ المحادثة بإرسال رسالة') }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Chat Input -->
                    <div class="chat-input mt-auto">
                        <form action="{{ route('web.chat.send', $chat->id) }}" method="POST" class="flex flex-col gap-2" id="message-form" enctype="multipart/form-data">
                            @csrf
                            <input type="hidden" name="type" value="text" id="message-type">

                            <div class="flex gap-2">
                                <input type="text" name="message" class="input input-bordered flex-1" placeholder="{{ __('اكتب رسالتك هنا...') }}" id="message-input" required>
                                <label for="image-upload" class="btn btn-outline btn-square" title="{{ __('إرفاق صورة') }}">
                                    <i class="fas fa-image"></i>
                                </label>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>

                            <!-- Hidden file input -->
                            <input type="file" id="image-upload" name="file" accept="image/*" class="hidden" />

                            <!-- Image preview container -->
                            <div id="image-preview-container" class="hidden mt-2 relative">
                                <img id="image-preview" class="max-h-48 max-w-xs rounded-lg object-contain" style="max-width: 300px;" />
                                <button type="button" id="remove-image" class="absolute top-1 right-1 bg-base-100 text-error p-1 rounded-full">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
@endsection

@section('page-script')
<script>
    // Add hidden inputs for chat ID and user ID
    document.write('<input type="hidden" id="chat-id" value="{{ $chat->id }}">');
    document.write('<input type="hidden" id="user-id" value="{{ auth()->id() }}">');
</script>
<script src="{{ asset('modules/chat/js/chat.js') }}"></script>

<script>
    // Image upload preview
    const imageUpload = document.getElementById('image-upload');
    const imagePreview = document.getElementById('image-preview');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const removeImageBtn = document.getElementById('remove-image');
    const messageTypeInput = document.getElementById('message-type');
    const messageInput = document.getElementById('message-input');

    if (imageUpload) {
        imageUpload.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.classList.remove('hidden');
                    messageTypeInput.value = 'image';
                    // Make message input optional when image is selected
                    messageInput.removeAttribute('required');
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }

    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', function() {
            imageUpload.value = '';
            imagePreviewContainer.classList.add('hidden');
            messageTypeInput.value = 'text';
            // Make message input required again when image is removed
            messageInput.setAttribute('required', 'required');
        });
    }

    // Report message functionality
    const reportLinks = document.querySelectorAll('.report-message');
    reportLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const messageId = this.getAttribute('data-message-id');
            document.getElementById('report-message-id').value = messageId;
            document.getElementById('reportModal').showModal();
        });
    });
</script>
@endsection

<!-- Report Modal -->
<dialog id="reportModal" class="modal">
    <div class="modal-box">
        <h3 class="font-bold text-lg">{{ __('الإبلاغ عن مخالفة') }}</h3>
        <form action="{{ route('web.report.store') }}" method="POST" class="mt-4">
            @csrf
            <input type="hidden" name="type" value="chat_message">
            <input type="hidden" name="model_id" id="report-message-id">

            <div class="form-control mb-4">
                <label class="label">
                    <span class="label-text">{{ __('سبب البلاغ') }}</span>
                </label>
                <textarea name="text" class="textarea textarea-bordered" rows="4" required placeholder="{{ __('يرجى ذكر سبب البلاغ...') }}"></textarea>
            </div>

            <div class="modal-action">
                <button type="submit" class="btn btn-primary">{{ __('إرسال البلاغ') }}</button>
                <button type="button" class="btn" onclick="reportModal.close()">{{ __('إلغاء') }}</button>
            </div>
        </form>
    </div>
</dialog>
