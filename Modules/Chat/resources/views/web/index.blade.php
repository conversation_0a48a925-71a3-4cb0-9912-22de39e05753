@extends('web.layouts.layout')
@section('title', __('المحادثات'))

@section('page-meta')
<meta name="description" content="{{ __('محادثاتي في موقع حراجي') }}">
<meta name="keywords" content="{{ __('حراجي, محادثات, رسائل, دردشة') }}">
@endsection

@section('page-style')
<style>
    .chat-list-item {
        transition: all 0.2s ease;
    }
    .chat-list-item:hover {
        background-color: rgba(var(--p), 0.1);
    }
    .chat-list-item.active {
        background-color: rgba(var(--p), 0.15);
        border-right: 3px solid hsl(var(--p));
    }
    .unread-badge {
        min-width: 20px;
        height: 20px;
        border-radius: 10px;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endsection

@section('content')
<main class="py-8">
    <div class="container mx-auto px-4">
        <!-- Breadcrumbs -->
        <div class="text-sm breadcrumbs mb-6">
            <ul>
                <li><a href="{{ route('web.index') }}">{{ __('الرئيسية') }}</a></li>
                <li>{{ __('المحادثات') }}</li>
            </ul>
        </div>

        <div class="bg-base-100 rounded-lg shadow-md overflow-hidden">
            <div class="p-6">
                <h1 class="text-2xl font-bold mb-6">{{ __('المحادثات') }}</h1>

                @if($chats->count() > 0)
                    <div class="grid grid-cols-1 gap-4">
                        @foreach($chats as $chat)
                            @php
                                $otherUser = $chat->sender_id == auth()->id() ? $chat->receiver : $chat->sender;
                                $unreadCount = $chat->messages->where('user_id', '!=', auth()->id())->where('is_read', false)->count();
                            @endphp
                            <a href="{{ route('web.chat.show', $chat->id) }}" class="chat-list-item flex items-center p-4 rounded-lg border border-base-300">
                                <div class="avatar">
                                    <div class="w-12 h-12 rounded-full">
                                        <img src="{{ $otherUser->avatar() }}" alt="{{ $otherUser->name }}">
                                    </div>
                                </div>
                                <div class="ml-4 rtl:mr-4 rtl:ml-0 flex-1">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold">{{ $otherUser->name }}</h3>
                                        <span class="text-sm text-base-content/70">{{ $chat->last_message_at ? $chat->last_message_at->diffForHumans() : $chat->created_at->diffForHumans() }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <p class="text-sm text-base-content/70 truncate max-w-xs">
                                            @if($chat->lastMessage)
                                                @if($chat->lastMessage->user_id == auth()->id())
                                                    <span class="text-primary">{{ __('أنت') }}: </span>
                                                @endif
                                                {{ $chat->lastMessage->content }}
                                            @else
                                                <span class="italic">{{ __('لا توجد رسائل') }}</span>
                                            @endif
                                        </p>
                                        @if($unreadCount > 0)
                                            <div class="unread-badge bg-primary text-primary-content">{{ $unreadCount }}</div>
                                        @endif
                                    </div>
                                    @if($chat->product)
                                        <div class="mt-2 flex items-center">
                                            <span class="badge badge-sm">{{ __('منتج') }}: {{ $chat->product->title }}</span>
                                        </div>
                                    @endif
                                </div>
                            </a>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="far fa-comments text-6xl text-base-content/30 mb-4"></i>
                        <h2 class="text-xl font-bold mb-2">{{ __('لا توجد محادثات') }}</h2>
                        <p class="text-base-content/70">{{ __('ابدأ محادثة جديدة من صفحة المنتج أو الملف الشخصي للبائع') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</main>
@endsection
