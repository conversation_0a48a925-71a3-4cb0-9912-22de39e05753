/**
 * Chat functionality for the chat module
 */

// Initialize chat when D<PERSON> is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Get chat elements
    const chatMessages = document.getElementById('chat-messages');
    const messageForm = document.getElementById('message-form');
    const chatId = document.getElementById('chat-id').value;
    const userId = document.getElementById('user-id').value;

    // Scroll to bottom of chat messages
    if (chatMessages) {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to handle new messages
    function handleNewMessage(data) {
        console.log('Processing new message:', data);

        // If data is wrapped in a message property, extract it
        const messageData = data.message || data;

        // Create message element
        const messageContainer = document.createElement('div');

        if (messageData.user_id == userId) {
            messageContainer.className = 'flex flex-col items-end mb-4';
        } else {
            messageContainer.className = 'flex flex-col items-start mb-4';
        }

        const messageBubble = document.createElement('div');

        if (messageData.user_id == userId) {
            messageBubble.className = 'message-bubble sent';
        } else {
            messageBubble.className = 'message-bubble received';
        }

        // Handle different message types
        if (messageData.type === 'image') {
            // Create image element
            const img = document.createElement('img');
            img.src = messageData.file_path;
            img.alt = 'Image';
            img.className = 'max-w-full rounded-lg max-h-64';
            img.style.maxWidth = '300px';
            messageBubble.appendChild(img);

            // Add text content if any
            if (messageData.content) {
                const textContent = document.createElement('p');
                textContent.className = 'mt-2';
                textContent.textContent = messageData.content;
                messageBubble.appendChild(textContent);
            }
        } else {
            // Text message
            messageBubble.textContent = messageData.content;
        }

        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        const date = new Date(messageData.created_at);
        messageTime.textContent = date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        if (messageData.user_id == userId) {
            const readIcon = document.createElement('i');
            readIcon.className = 'fas fa-check text-base-content/50 ml-1 rtl:mr-1 rtl:ml-0';
            messageTime.appendChild(readIcon);
        }

        messageContainer.appendChild(messageBubble);
        messageContainer.appendChild(messageTime);

        // Add to chat
        chatMessages.appendChild(messageContainer);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // Mark as read if it's not our message
        if (messageData.user_id != userId) {
            fetch(`/api/chat/${messageData.chat_id}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            }).catch(error => console.error('Error marking message as read:', error));
        }
    }

    // Setup Echo listener for real-time chat
    console.log('Setting up Echo listener for chat.' + chatId);

    // Check if Echo is properly initialized
    if (!window.Echo) {
        console.error('Echo is not initialized! Check your Echo configuration.');
    } else {
        console.log('Echo is initialized');

        // Test the channel
        try {
            // The channel name should match what's in the server logs: private-chat.1
            const channel = window.Echo.private(`chat.${chatId}`);

            console.log('Subscribed to channel chat.' + chatId);

            // Add this to debug channel subscription
            channel.listenForWhisper('typing', () => {
                console.log('Whisper received - channel is working');
            });

            // Try to send a whisper to test the connection
            setTimeout(() => {
                channel.whisper('typing', {});
                console.log('Whisper sent to test channel');
            }, 3000);

            // Listen for the event with the correct name
            // Based on your network debug, the event name is "Modules\Chat\app\Events\NewMessageEvent"
            // But we also need to listen for the broadcastAs name which is "NewMessageEvent"

            // Listen with the broadcastAs name
            channel.listen('.NewMessageEvent', function(e) {
                console.log('Received message with broadcastAs name:', e);
                handleNewMessage(e);
            });
            channel.error(function(error) {
                console.error('Channel error:', error);
            });
        } catch (error) {
            console.error('Error setting up Echo:', error);
        }
    }

    // Handle form submission
    if (messageForm) {
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear input
                    messageForm.reset();

                    // Add message to chat if not already added by Echo
                    if (!window.Echo) {
                        const messageContainer = document.createElement('div');
                        messageContainer.className = 'flex flex-col items-end mb-4';

                        const messageBubble = document.createElement('div');
                        messageBubble.className = 'message-bubble sent';

                        // Handle different message types
                        if (data.data.type === 'image') {
                            // Create image element
                            const img = document.createElement('img');
                            img.src = data.data.file_path;
                            img.alt = 'Image';
                            img.className = 'max-w-full rounded-lg max-h-64';
                            img.style.maxWidth = '300px';
                            messageBubble.appendChild(img);

                            // Add text content if any
                            if (data.data.content) {
                                const textContent = document.createElement('p');
                                textContent.className = 'mt-2';
                                textContent.textContent = data.data.content;
                                messageBubble.appendChild(textContent);
                            }
                        } else {
                            // Text message
                            messageBubble.textContent = data.data.content;
                        }

                        const messageTime = document.createElement('div');
                        messageTime.className = 'message-time';
                        const date = new Date(data.data.created_at);
                        messageTime.textContent = date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                        const readIcon = document.createElement('i');
                        readIcon.className = 'fas fa-check text-base-content/50 ml-1 rtl:mr-1 rtl:ml-0';
                        messageTime.appendChild(readIcon);

                        messageContainer.appendChild(messageBubble);
                        messageContainer.appendChild(messageTime);

                        chatMessages.appendChild(messageContainer);
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    }

                    // Clear image preview if exists
                    const imagePreviewContainer = document.getElementById('image-preview-container');
                    if (imagePreviewContainer) {
                        imagePreviewContainer.classList.add('hidden');
                    }

                    // Reset message type to text
                    const messageTypeInput = document.getElementById('message-type');
                    if (messageTypeInput) {
                        messageTypeInput.value = 'text';
                    }

                    // Make message input required again
                    const messageInput = document.getElementById('message-input');
                    if (messageInput) {
                        messageInput.setAttribute('required', 'required');
                    }
                }
            });
        });
    }
});
