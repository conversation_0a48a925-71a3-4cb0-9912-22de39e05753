<?php

namespace Modules\Chat\app\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ChatMessageResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'chat_id' => $this->chat_id,
            'user_id' => $this->user_id,
            'type' => $this->type,
            'content' => $this->content,
            'file_path' => $this->file_path ? asset('storage/' . $this->file_path) : null,
            'mime_type' => $this->mime_type,
            'is_read' => $this->is_read,
            'read_at' => $this->read_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'user' => $this->whenLoaded('user'),
        ];
    }
} 