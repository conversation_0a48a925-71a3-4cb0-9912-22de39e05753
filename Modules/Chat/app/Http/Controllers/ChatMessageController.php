<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Chat\app\Models\Chat;
use Modules\Chat\app\Models\ChatMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Modules\Chat\app\Events\NewMessageEvent;
use Modules\Chat\app\Http\Resources\ChatMessageResource;

class ChatMessageController extends Controller
{
    public function index(Request $request, $chatId)
    {
        //validate that requested in auth user is part of the chat
        $chat = Chat::where(function ($query) {
            $query->where('sender_id', Auth::id())->orWhere('receiver_id', Auth::id());
        })->findOrFail($chatId);

        if ($chat->sender_id !== Auth::id() && $chat->receiver_id !== Auth::id()) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'You are not part of this chat',
                ],
                Response::HTTP_FORBIDDEN,
            );
        }

        $messages = ChatMessage::where('chat_id', $chat->id)->get();

        return response()->json([
            'success' => true,
            'data' => ChatMessageResource::collection($messages),
        ]);
    }
    public function store(Request $request, $chatId)
    {
        $request->validate([
            'type' => 'required|in:text,audio,image,video',
            'content' => 'required_if:type,text',
            'file' => 'required_if:type,audio,image,video|file|max:20480',
        ]);

        $chat = Chat::where(function ($query) {
            $query->where('sender_id', Auth::id())->orWhere('receiver_id', Auth::id());
        })->findOrFail($chatId);

        $message = new ChatMessage();
        $message->chat_id = $chat->id;
        $message->user_id = Auth::id();
        $message->type = $request->type;

        if ($request->type === 'text') {
            $message->content = $request->content;
        } else {
            if ($request->hasFile('file')) {
                $file = $request->file('file');
                $fileName = time() . '_' . $file->getClientOriginalName();
                $filePath = $file->storeAs('chat_files', $fileName, 'public');
                $message->file_path = $filePath;
                $message->mime_type = $file->getMimeType();
            }
        }

        $message->save();

        // Update the last_message_at of the chat
        $chat->last_message_at = now();
        $chat->save();

        // Broadcast the message
        broadcast(new NewMessageEvent($message->load('user')))->toOthers();

        return response()->json(
            [
                'status' => 'success',
                'data' => $message->load('user'),
            ],
            Response::HTTP_CREATED,
        );
    }

    public function markAsRead(Request $request, $chatId)
    {
        $chat = Chat::where(function ($query) {
            $query->where('sender_id', Auth::id())->orWhere('receiver_id', Auth::id());
        })->findOrFail($chatId);

        // Mark all unread messages sent by the other user as read
        ChatMessage::where('chat_id', $chat->id)
            ->where('user_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

        return response()->json([
            'status' => 'success',
            'message' => 'Messages marked as read',
        ]);
    }
}
