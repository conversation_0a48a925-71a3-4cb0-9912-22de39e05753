<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Chat\app\Models\Chat;
use Modules\Chat\app\Models\ChatMessage;
use Modules\Chat\app\Http\Resources\ChatResource;
use Illuminate\Support\Facades\Auth;

class ChatApiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $userId = Auth::id();
        
        $chats = Chat::where('sender_id', $userId)
            ->orWhere('receiver_id', $userId)
            ->with(['sender', 'receiver', 'lastMessage'])
            ->orderBy('last_message_at', 'desc')
            ->get();
            
        return response()->json([
            'success' => true,
            'status' => 'success',
            'data' => ChatResource::collection($chats)
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,hashId',
            'product_id' => 'nullable|exists:products,hashId',
        ]);

        $senderId = Auth::id();
        $receiverId = User::where('hashId', $request->receiver_id)->first()->id;
        $productId = $request->has('product_id') ? Product::where('hashId', $request->product_id)->first()->id : null;
        
        // Check if chat already exists
        $chat = Chat::where(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $senderId)
                      ->where('receiver_id', $receiverId);
            })
            ->orWhere(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $receiverId)
                      ->where('receiver_id', $senderId);
            })
            ->when($request->has('product_id'), function($query) use ($productId) {
                return $query->where('product_id', $productId);
            })
            ->first();
            
        if (!$chat) {
            $chat = Chat::create([
                'sender_id' => $senderId,
                'receiver_id' => $receiverId,
                'product_id' => $productId,
                'last_message_at' => now(),
            ]);
        }
        
        return response()->json([
            'success' => true,
            'status' => 'success',
            'data' => new ChatResource($chat->load(['sender', 'receiver']))
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        $userId = Auth::id();
        
        $chat = Chat::with(['sender', 'receiver', 'messages.user'])
            ->where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($id);
            
        return response()->json([
            'status' => 'success',
            'data' => new ChatResource($chat)
        ]);
    }

    /**
     * Send a message in the chat.
     */
    public function sendMessage(Request $request, $chatId)
    {
        $request->validate([
            'message' => 'required|string',
            'type' => 'required|in:text,audio,image,video',
            'file' => 'required_if:type,audio,image,video|file|max:20480',
        ]);

        $userId = Auth::id();
        
        $chat = Chat::where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                    ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($chatId);
            
        $attachmentPath = null;
        if ($request->hasFile('file')) {
            $attachmentPath = $request->file('file')->store('chat_attachments', 'public');
        }
        
        $message = ChatMessage::create([
            'chat_id' => $chat->id,
            'user_id' => $userId,
            'content' => $request->message,
            'file_path' => $attachmentPath,
            'type' => $request->type,
        ]);
        
        $chat->update([
            'last_message_at' => now(),
        ]);
        
        return response()->json([
            'success' => true,
            'status' => 'success',
            'data' => $message
        ], Response::HTTP_CREATED);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $userId = Auth::id();
        
        $chat = Chat::where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($id);
            
        $chat->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Chat deleted successfully'
        ]);
    }
} 