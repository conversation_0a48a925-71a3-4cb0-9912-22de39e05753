<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Chat\app\Models\Chat;
use Modules\Chat\app\Models\ChatMessage;
use Illuminate\Support\Facades\Auth;

class ChatController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $userId = Auth::id();
        
        $chats = Chat::where('sender_id', $userId)
            ->orWhere('receiver_id', $userId)
            ->with(['sender', 'receiver', 'lastMessage'])
            ->orderBy('last_message_at', 'desc')
            ->get();
            
        return response()->json([
            'status' => 'success',
            'data' => $chats
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('chat::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'product_id' => 'nullable|exists:products,id',
        ]);

        $senderId = Auth::id();
        $receiverId = $request->receiver_id;
        
        // Check if chat already exists
        $chat = Chat::where(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $senderId)
                      ->where('receiver_id', $receiverId);
            })
            ->orWhere(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $receiverId)
                      ->where('receiver_id', $senderId);
            })
            ->when($request->has('product_id'), function($query) use ($request) {
                return $query->where('product_id', $request->product_id);
            })
            ->first();
            
        if (!$chat) {
            $chat = Chat::create([
                'sender_id' => $senderId,
                'receiver_id' => $receiverId,
                'product_id' => $request->product_id,
                'last_message_at' => now(),
            ]);
        }
        
        return response()->json([
            'status' => 'success',
            'data' => $chat->load(['sender', 'receiver'])
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        $userId = Auth::id();
        
        $chat = Chat::with(['sender', 'receiver', 'messages.user'])
            ->where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($id);
            
        return response()->json([
            'status' => 'success',
            'data' => $chat
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('chat::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $userId = Auth::id();
        
        $chat = Chat::where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($id);
            
        $chat->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => 'Chat deleted successfully'
        ]);
    }
}
