<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Modules\Chat\app\Models\Chat;
use Modules\Chat\app\Models\ChatMessage;
use Illuminate\Support\Facades\Auth;

class ChatAdminController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $chats = Chat::with(['sender', 'receiver', 'lastMessage'])
            ->orderBy('last_message_at', 'desc')
            ->paginate(15);
        return view('chat::admin.index', compact('chats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('chat::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:users,id',
            'product_id' => 'nullable|exists:products,id',
        ]);

        $senderId = Auth::id();
        $receiverId = $request->receiver_id;
        
        // Check if chat already exists
        $chat = Chat::where(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $senderId)
                      ->where('receiver_id', $receiverId);
            })
            ->orWhere(function($query) use ($senderId, $receiverId) {
                $query->where('sender_id', $receiverId)
                      ->where('receiver_id', $senderId);
            })
            ->when($request->has('product_id'), function($query) use ($request) {
                return $query->where('product_id', $request->product_id);
            })
            ->first();
            
        if (!$chat) {
            $chat = Chat::create([
                'sender_id' => $senderId,
                'receiver_id' => $receiverId,
                'product_id' => $request->product_id,
                'last_message_at' => now(),
            ]);
        }
        
        return response()->json([
            'status' => 'success',
            'data' => $chat->load(['sender', 'receiver'])
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        $chat = Chat::with(['sender', 'receiver', 'messages.user'])
            ->findOrFail($id);
            
        return view('chat::admin.show', compact('chat'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('chat::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Delete a specific message
     */
    public function deleteMessage($id)
    {
        $message = ChatMessage::findOrFail($id);
        $message->delete();
        
        return redirect()->back()->with('success', 'Message deleted successfully');
    }

    /**
     * Report a user
     */
    public function reportUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        // Logic to report the user
        // This might involve creating a report record in a reports table
        
        return redirect()->back()->with('success', 'User reported successfully');
    }

    /**
     * Block a user
     */
    public function blockUser(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->update(['status' => 'blocked']);
        
        return redirect()->back()->with('success', 'User blocked successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $chat = Chat::findOrFail($id);
        $chat->delete();
        
        return redirect()->route('admin.chats.index')->with('success', 'Chat deleted successfully');
    }
}
