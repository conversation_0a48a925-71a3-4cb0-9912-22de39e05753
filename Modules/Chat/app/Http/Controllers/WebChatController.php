<?php

namespace Modules\Chat\app\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;
use Modules\Chat\app\Models\Chat;
use Modules\Chat\app\Models\ChatMessage;
use Modules\Chat\app\Events\NewMessageEvent;
use Illuminate\Support\Facades\Auth;

class WebChatController extends Controller
{
    /**
     * Display a listing of the user's chats.
     */
    public function index()
    {
        $userId = Auth::id();

        $chats = Chat::where('sender_id', $userId)
            ->orWhere('receiver_id', $userId)
            ->with(['sender', 'receiver', 'lastMessage', 'product'])
            ->orderBy('last_message_at', 'desc')
            ->get();

        return view('chat::web.index', compact('chats'));
    }

    /**
     * Start a new chat with a user.
     */
    public function startChat(Request $request)
    {
        $request->validate([
            'user' => 'required|exists:users,id',
            'product' => 'nullable|exists:products,id',
        ]);

        $senderId = Auth::id();
        $receiverId = $request->user;
        $productId = $request->product;

        // Check if product allows chat contact
        if ($productId) {
            $product = Product::findOrFail($productId);
            if (!$product->allow_chat) {
                return redirect()->back()->with('error', __('هذا المنتج لا يسمح بالمحادثات'));
            }
        }

        // Check if chat already exists
        $chat = Chat::where(function($query) use ($senderId, $receiverId, $productId) {
                $query->where('sender_id', $senderId)
                      ->where('receiver_id', $receiverId);
                if ($productId) {
                    $query->where('product_id', $productId);
                }
            })
            ->orWhere(function($query) use ($senderId, $receiverId, $productId) {
                $query->where('sender_id', $receiverId)
                      ->where('receiver_id', $senderId);
                if ($productId) {
                    $query->where('product_id', $productId);
                }
            })
            ->first();

        if (!$chat) {
            $chat = Chat::create([
                'sender_id' => $senderId,
                'receiver_id' => $receiverId,
                'product_id' => $productId,
                'last_message_at' => now(),
            ]);
        }

        return redirect()->route('web.chat.show', $chat->id);
    }

    /**
     * Show the specified chat.
     */
    public function show($id)
    {
        $userId = Auth::id();

        $chat = Chat::with(['sender', 'receiver', 'messages.user', 'product'])
            ->where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($id);

        // Mark all messages as read
        ChatMessage::where('chat_id', $chat->id)
            ->where('user_id', '!=', $userId)
            ->where('is_read', false)
            ->update(['is_read' => true, 'read_at' => now()]);

        // Get other chats for sidebar
        $otherChats = Chat::where('id', '!=', $chat->id)
            ->where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->with(['sender', 'receiver', 'lastMessage'])
            ->orderBy('last_message_at', 'desc')
            ->limit(10)
            ->get();

        return view('chat::web.show', compact('chat', 'otherChats'));
    }

    /**
     * Send a message in the chat.
     */
    public function sendMessage(Request $request, $chatId)
    {
        $request->validate([
            'message' => 'required_if:type,text|string',
            'type' => 'required|in:text,audio,image,video',
            'file' => 'required_if:type,audio,image,video|file|max:20480',
        ]);

        $userId = Auth::id();

        $chat = Chat::where(function($query) use ($userId) {
                $query->where('sender_id', $userId)
                    ->orWhere('receiver_id', $userId);
            })
            ->findOrFail($chatId);

        $attachmentPath = null;
        if ($request->hasFile('file')) {
            $attachmentPath = $request->file('file')->store('chat_attachments', 'public');
        }

        $message = ChatMessage::create([
            'chat_id' => $chat->id,
            'user_id' => $userId,
            'content' => $request->message,
            'file_path' => $attachmentPath,
            'type' => $request->type,
        ]);

        // Update last message timestamp
        $chat->update(['last_message_at' => now()]);

        // Broadcast event for real-time chat
        try {
            event(new NewMessageEvent($message));
            \Illuminate\Support\Facades\Log::info('Chat message event dispatched successfully', [
                'chat_id' => $chat->id,
                'message_id' => $message->id,
                'user_id' => $userId
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to dispatch chat message event', [
                'chat_id' => $chat->id,
                'message_id' => $message->id,
                'error' => $e->getMessage()
            ]);
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message->load('user')
            ]);
        }

        return redirect()->back();
    }
}
