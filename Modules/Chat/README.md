# Chat Module

A real-time messaging module for Laravel applications, enabling communication between users with support for text, audio, image, and video messages.

## Features

- Chat between users (one-to-one messaging)
- Optional product context for marketplace conversations
- Support for multiple message types:
  - Text messages
  - Audio messages
  - Image messages
  - Video messages
- Real-time messaging using Laravel Echo and WebSockets
- Message read status tracking
- Message history

## API Endpoints

### Chats

- `GET /api/v1/chats` - Get all user chats
- `POST /api/v1/chats` - Create a new chat
- `GET /api/v1/chats/{id}` - Get a specific chat with messages
- `DELETE /api/v1/chats/{id}` - Delete a chat

### Messages

- `POST /api/v1/chats/{chatId}/messages` - Send a message to a chat
- `POST /api/v1/chats/{chatId}/read` - Mark all messages as read

## Installation

1. Make sure Laravel Echo is installed and configured in your frontend application
2. Configure Pusher or Laravel Websockets for broadcasting
3. Run database migrations:
```bash
php artisan module:migrate Chat
```

## Usage

### Frontend Implementation

To listen for new messages in real-time, use Laravel Echo:

```javascript
Echo.private(`chat.${chatId}`)
    .listen('NewMessageEvent', (e) => {
        // Handle new message
        console.log(e.message);
    });
```

### Sending Different Message Types

**Text Message:**
```
POST /api/v1/chats/{chatId}/messages
{
    "type": "text",
    "content": "Hello, how are you?"
}
```

**Image/Audio/Video Message:**
Send as multipart/form-data with the file attached.
```
POST /api/v1/chats/{chatId}/messages
{
    "type": "image", // or "audio" or "video"
    "file": [file object]
}
``` 