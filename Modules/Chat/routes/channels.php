<?php

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;
use Modules\Chat\app\Models\Chat;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('chat.{chatId}', function ($user, $chatId) {
    return true; // or custom logic like $user->id === $someCondition
    $chat = \Modules\Chat\app\Models\Chat::find($chatId);
    if (!$chat) {
        \Illuminate\Support\Facades\Log::error('Chat not found', ['chat_id' => $chatId]);
        return false;
    }

    \Illuminate\Support\Facades\Log::info('Channel authorization check', [
        'user_id' => $user->id,
        'chat_id' => $chatId,
        'sender_id' => $chat->sender_id,
        'receiver_id' => $chat->receiver_id,
        'authorized' => $user->id === $chat->sender_id || $user->id === $chat->receiver_id,
    ]);

    return $user->id === $chat->sender_id || $user->id === $chat->receiver_id;
});
