<?php

use Illuminate\Support\Facades\Route;
use Modules\Chat\app\Http\Controllers\WebChatController;
use Modules\Chat\app\Http\Controllers\ChatAdminController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Web frontend chat routes
Route::middleware(['web', 'auth'])->name('web.')->group(function () {
    Route::get('chats', [WebChatController::class, 'index'])->name('chat.index');
    Route::get('chats/{chat}', [WebChatController::class, 'show'])->name('chat.show');
    Route::post('chats/{chat}/send', [WebChatController::class, 'sendMessage'])->name('chat.send');
    Route::get('chat/start', [WebChatController::class, 'startChat'])->name('chat.start');
});

Route::prefix('admin')
    ->middleware(['auth'])
    ->name('admin.')
    ->group(function () {
        Route::get('chats', [ChatAdminController::class, 'index'])->name('chats.index');
        Route::get('chats/{chat}', [ChatAdminController::class, 'show'])->name('chats.show');
        Route::delete('chats/{chat}', [ChatAdminController::class, 'destroy'])->name('chats.destroy');
        Route::delete('messages/{message}', [ChatAdminController::class, 'deleteMessage'])->name('messages.delete');
        Route::post('users/{user}/report', [ChatAdminController::class, 'reportUser'])->name('users.report');
        Route::post('users/{user}/block', [ChatAdminController::class, 'blockUser'])->name('users.block');
    });
