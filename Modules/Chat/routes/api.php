<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Chat\app\Http\Controllers\ChatApiController;
use Modules\Chat\app\Http\Controllers\ChatMessageController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->group(function () {
    Route::apiResource('chats', ChatApiController::class);
    Route::get('chat/{chatId}/messages', [ChatMessageController::class, 'index']);
    Route::post('chat/{chatId}/send', [ChatApiController::class, 'sendMessage']);
    
});
