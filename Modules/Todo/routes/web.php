<?php

use Illuminate\Support\Facades\Route;
use Modules\Todo\app\Http\Controllers\TodoController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('admin')->middleware(['web', 'auth'])->group(function () {
    Route::resource('todos', TodoController::class);
    Route::patch('todos/{todo}/toggle-status', [TodoController::class, 'toggleStatus'])->name('todos.toggle-status');
    Route::delete('todos/{todo}/images/{image}', [TodoController::class, 'deleteImage'])->name('todos.delete-image');
});
