@extends('layouts/layoutMaster')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="h4 mb-0">تعديل المهمة</h2>
                </div>
                <div class="card-body">
                    <form action="{{ route('todos.update', $todo) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                id="title" name="title" value="{{ old('title', $todo->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                id="description" name="description" rows="3">{{ old('description', $todo->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select @error('priority') is-invalid @enderror" 
                                id="priority" name="priority" required>
                                <option value="low" {{ old('priority', $todo->priority) == 'low' ? 'selected' : '' }}>منخفضة</option>
                                <option value="medium" {{ old('priority', $todo->priority) == 'medium' ? 'selected' : '' }}>متوسطة</option>
                                <option value="high" {{ old('priority', $todo->priority) == 'high' ? 'selected' : '' }}>عالية</option>
                            </select>
                            @error('priority')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                            <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                id="due_date" name="due_date" value="{{ old('due_date', $todo->due_date ? $todo->due_date->format('Y-m-d') : '') }}">
                            @error('due_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="status" name="status" value="1" 
                                    {{ old('status', $todo->status) ? 'checked' : '' }}>
                                <label class="form-check-label" for="status">مكتملة</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">الصور الحالية</label>
                            <div class="row g-3">
                                @foreach($todo->images as $image)
                                    <div class="col-md-4">
                                        <div class="card">
                                            <img src="{{ Storage::url($image->image_path) }}" class="card-img-top" alt="Todo image">
                                            <div class="card-body p-2">
                                                <form action="{{ route('todos.delete-image', [$todo, $image]) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-danger btn-sm w-100" 
                                                        onclick="return confirm('هل أنت متأكد من حذف هذه الصورة؟')">
                                                        حذف الصورة
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="images" class="form-label">إضافة صور جديدة (الحد الأقصى {{ 3 - $todo->images->count() }} صور)</label>
                            <input type="file" class="form-control @error('images.*') is-invalid @enderror" 
                                id="images" name="images[]" multiple accept="image/*" 
                                {{ $todo->images->count() >= 3 ? 'disabled' : '' }}>
                            <div class="form-text">يمكنك رفع حتى {{ 3 - $todo->images->count() }} صور إضافية. الأنواع المسموحة: JPEG, PNG, JPG, GIF. الحجم الأقصى: 2MB لكل صورة.</div>
                            @error('images.*')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('todos.index') }}" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection 