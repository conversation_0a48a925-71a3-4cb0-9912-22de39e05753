@extends('layouts/layoutMaster')

@php
use Illuminate\Support\Facades\Storage;
@endphp

@section('content')
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">المهام</h1>
        <a href="{{ route('todos.create') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> إضافة مهمة جديدة
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <a href="{{ route('todos.index', ['sort' => 'title', 'direction' => $sortDirection]) }}" class="text-decoration-none text-dark">
                                    العنوان
                                    @if($sortField === 'title')
                                        <i class="bi bi-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th>الصور</th>
                            <th>الأولوية</th>
                            <th>
                                <a href="{{ route('todos.index', ['sort' => 'due_date', 'direction' => $sortDirection]) }}" class="text-decoration-none text-dark">
                                    تاريخ الاستحقاق
                                    @if($sortField === 'due_date')
                                        <i class="bi bi-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th>الحالة</th>
                            <th>
                                <a href="{{ route('todos.index', ['sort' => 'created_at', 'direction' => $sortDirection]) }}" class="text-decoration-none text-dark">
                                    تاريخ الإنشاء
                                    @if($sortField === 'created_at')
                                        <i class="bi bi-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                                    @endif
                                </a>
                            </th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($todos as $todo)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="{{ $todo->status ? 'text-decoration-line-through text-muted' : '' }}">
                                            {{ $todo->title }}
                                        </span>
                                        @if($todo->description)
                                            <i class="bi bi-info-circle ms-2 text-muted" data-bs-toggle="tooltip" title="{{ $todo->description }}"></i>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    @if($todo->images->count() > 0)
                                        <div class="d-flex gap-1">
                                            @foreach($todo->images as $image)
                                                <a href="{{ Storage::url($image->image_path) }}" target="_blank" class="text-decoration-none">
                                                    <img src="{{ Storage::url($image->image_path) }}" 
                                                        alt="Todo image" 
                                                        class="rounded" 
                                                        style="width: 40px; height: 40px; object-fit: cover;">
                                                </a>
                                            @endforeach
                                        </div>
                                    @else
                                        <span class="text-muted">لا توجد صور</span>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge 
                                        @if($todo->priority === 'high') bg-danger
                                        @elseif($todo->priority === 'medium') bg-warning
                                        @else bg-success @endif">
                                        @if($todo->priority === 'high') عالية
                                        @elseif($todo->priority === 'medium') متوسطة
                                        @else منخفضة @endif
                                    </span>
                                </td>
                                <td>
                                    @if($todo->due_date)
                                        {{ $todo->due_date->format('Y-m-d') }}
                                    @else
                                        <span class="text-muted">غير محدد</span>
                                    @endif
                                </td>
                                <td>
                                    <form action="{{ route('todos.toggle-status', $todo) }}" method="POST" class="d-inline">
                                        @csrf
                                        @method('PATCH')
                                        <button type="submit" class="btn btn-sm {{ $todo->status ? 'btn-success' : 'btn-outline-secondary' }}">
                                            {{ $todo->status ? 'مكتملة' : 'قيد التنفيذ' }}
                                        </button>
                                    </form>
                                </td>
                                <td>{{ $todo->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('todos.edit', $todo) }}" class="btn btn-sm btn-warning">
                                            <i class="ti ti-pencil"></i>
                                        </a>
                                        <form action="{{ route('todos.destroy', $todo) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟')">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">لا توجد مهام</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-center mt-4">
                {{ $todos->links() }}
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
    });
</script>
@endpush
@endsection
