<?php

namespace Modules\Todo\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Todo\app\Models\Todo;
use Modules\Todo\app\Models\TodoImage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Storage;

class TodoController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Todo::where('user_id', Auth::id());
        
        // Handle sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        // Validate sort field
        $allowedSortFields = ['title', 'due_date', 'created_at'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }
        
        // Validate sort direction
        if (!in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }
        
        $query->orderBy($sortField, $sortDirection);
        
        // Get paginated results
        $todos = $query->paginate(10);
        
        return view('todo::index', compact('todos', 'sortField', 'sortDirection'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('todo::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'priority' => 'required|in:low,medium,high',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $todo = Todo::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'due_date' => $validated['due_date'],
            'priority' => $validated['priority'],
            'user_id' => Auth::id(),
        ]);

        if ($request->hasFile('images')) {
            $images = $request->file('images');
            $count = 0;
            
            foreach ($images as $image) {
                if ($count >= 3) break;
                
                $path = $image->store('todo-images', 'public');
                TodoImage::create([
                    'todo_id' => $todo->id,
                    'image_path' => $path
                ]);
                
                $count++;
            }
        }

        return redirect()->route('todos.index')
            ->with('success', 'تم إنشاء المهمة بنجاح');
    }

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('todo::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Todo $todo)
    {
        $this->authorize('update', $todo);
        return view('todo::edit', compact('todo'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Todo $todo)
    {
        $this->authorize('update', $todo);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'due_date' => 'nullable|date',
            'priority' => 'required|in:low,medium,high',
            'status' => 'boolean',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $todo->update([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'due_date' => $validated['due_date'],
            'priority' => $validated['priority'],
            'status' => $request->has('status') ? $validated['status'] : $todo->status,
        ]);

        if ($request->hasFile('images')) {
            $currentImageCount = $todo->images()->count();
            $remainingSlots = 3 - $currentImageCount;
            
            if ($remainingSlots > 0) {
                $images = $request->file('images');
                $count = 0;
                
                foreach ($images as $image) {
                    if ($count >= $remainingSlots) break;
                    
                    $path = $image->store('todo-images', 'public');
                    TodoImage::create([
                        'todo_id' => $todo->id,
                        'image_path' => $path
                    ]);
                    
                    $count++;
                }
            }
        }

        return redirect()->route('todos.index')
            ->with('success', 'تم تحديث المهمة بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Todo $todo)
    {
        $this->authorize('delete', $todo);
        
        // Delete associated images from storage
        foreach ($todo->images as $image) {
            Storage::disk('public')->delete($image->image_path);
        }
        
        $todo->delete();

        return redirect()->route('todos.index')
            ->with('success', 'تم حذف المهمة بنجاح');
    }

    public function toggleStatus(Todo $todo)
    {
        $this->authorize('update', $todo);
        
        $todo->update(['status' => !$todo->status]);

        return redirect()->route('todos.index')
            ->with('success', 'تم تحديث حالة المهمة بنجاح');
    }

    /**
     * Delete a specific image from a todo
     */
    public function deleteImage(Todo $todo, TodoImage $image)
    {
        $this->authorize('update', $todo);
        
        if ($image->todo_id !== $todo->id) {
            return redirect()->back()->with('error', 'غير مصرح لك بحذف هذه الصورة');
        }

        Storage::disk('public')->delete($image->image_path);
        $image->delete();

        return redirect()->back()->with('success', 'تم حذف الصورة بنجاح');
    }
}
