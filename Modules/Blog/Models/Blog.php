<?php

namespace Modules\Blog\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\Blog\Database\Factories\BlogFactory;

class Blog extends Model
{
    use HasFactory;

    public $guarded = [];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $slug = self::generateSlug($model->title);
            if (self::where('slug', $slug)->exists()) {
                $slug = $slug . '-' . time();
            }
            $model->slug = $slug;

        });

        static::updating(function ($model) {
            if ($model->isDirty('title')) {
                $slug = self::generateSlug($model->title);
                if (self::where('slug', $slug)->exists()) {
                    $slug = $slug . '-' . time();
                }
                $model->slug = $slug;
            }
        });
    }

    public static function generateSlug($title)
    {
        // Convert Arabic title to a slug
        $slug = preg_replace('/\s+/u', '-', trim($title)); // Replace spaces with hyphens
        $slug = preg_replace('/[^\p{L}\p{N}\-]+/u', '', $slug); // Remove non-alphanumeric characters except hyphens
        $slug = mb_strtolower($slug); // Convert to lowercase
        return $slug;
    }

    public function categories()
    {
        return $this->belongsToMany(BlogCategory::class, 'blog_categories_relation');
    }
}

