<?php

namespace Modules\Blog\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogCategory extends Model
{
    use HasFactory;

    protected $fillable = ['title', 'parent_id', 'slug'];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->slug = self::generateSlug($model->title);
        });

        static::updating(function ($model) {
            if ($model->isDirty('title')) {
                $model->slug = self::generateSlug($model->title);
            }
        });
    }

    public static function generateSlug($title)
    {
        // Convert Arabic title to a slug
        $slug = preg_replace('/\s+/u', '-', trim($title)); // Replace spaces with hyphens
        $slug = preg_replace('/[^\p{L}\p{N}\-]+/u', '', $slug); // Remove non-alphanumeric characters except hyphens
        $slug = mb_strtolower($slug); // Convert to lowercase
        return $slug;
    }

    public function parent()
    {
        return $this->belongsTo(BlogCategory::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(BlogCategory::class, 'parent_id');
    }

    public function blogs()
    {
        return $this->belongsToMany(Blog::class, 'blog_categories_relation');
    }
}
