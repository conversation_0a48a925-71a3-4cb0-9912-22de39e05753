<?php

namespace Modules\Blog\Controller;

use App\Http\Controllers\Controller;
use App\Helpers\Helpers;
use Modules\Blog\Requests\UpdateBlogRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;
use Modules\Blog\Models\Blog;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Requests\CreateBlogRequest;

class BlogController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (!$request->user()->hasPermissionTo('blog_read')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية  لرؤية المقالات');
        }
        $items = Blog::orderBy('id', 'desc')->paginate(10);
        return view('modules.blog.index', compact( 'items'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = BlogCategory::all();
        return view('modules.blog.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CreateBlogRequest $request)
    {


        if (!$request->user()->hasPermissionTo('blog_create')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لإنشاء المقالات');
        }

        $keywords = json_decode($request->meta_keywords);
        $keywords = array_map(function ($keyword) {
            return $keyword->value;
        }, $keywords);

        $data = [
            'title' => $request->title,
            'content' => $request->content,
            'summary' => $request->summary,
            'is_published' => $request->is_published == 'on' ? true : false,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => implode(',', $keywords),
        ];
        if ($request->hasFile('image')) {
            unset($data['image']);
        //if image bigger than 10MB resize it
            $manager = new ImageManager(Driver::class);
            $image = $manager->read($request->file('image'));
            $generateImageName = time() . '.' . $request->file('image')->getClientOriginalExtension();
            //create directory if not exists
            if (!file_exists(public_path('storage/blogs'))) {
                mkdir(public_path('storage/blogs'), 0777, true);
            }
            $imageResult = $image->save('storage/blogs/' . $generateImageName);
            $imagePath = Storage::url('blogs/' . $generateImageName);

            $data['image'] = $imagePath;
        }

        $data['created_by'] = $request->user()->id;
        $blog = Blog::create($data);

        foreach ($request->categories as $category) {
            $blog->categories()->attach($category);
        }
        return redirect()->route('blogs.index')->with('success', 'تم إنشاء المقال بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(Blog $blog)
    {
        return redirect()->route('blogs.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Blog $blog)
    {
        $categories = BlogCategory::all();
        return  view('modules.blog.update', compact('blog', 'categories'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBlogRequest $request, Blog $blog)
    {
        if (!$request->user()->hasPermissionTo('blog_update')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لتحديث المقالات');
        }
        $data = [
            'title' => $request->title,
            'content' => $request->content,
            'summary' => $request->summary,
            'is_published' => $request->is_published == 'on' ? true : false,
            'meta_title' => $request->meta_title,
            'meta_description' => $request->meta_description,
            'meta_keywords' => $request->meta_keywords,
        ];
        if ($request->hasFile('image')) {
            unset($data['image']);
            $manager = new ImageManager(Driver::class);
            $image = $manager->read($request->file('image'));
            $generateImageName = time() . '.' . $request->file('image')->getClientOriginalExtension();
            //create directory if not exists
            if (!file_exists(public_path('storage/blogs'))) {
                mkdir(public_path('storage/blogs'), 0777, true);
            }
            $imageResult = $image->save('storage/blogs/' . $generateImageName);
            $imagePath = Storage::url('blogs/' . $generateImageName);

            $data['image'] = $imagePath;
        }

        $blog->update($data);
        $blog->categories()->sync($request->categories);
        return redirect()->route('blogs.index')->with('success', 'تم تحديث المقال بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Blog $blog)
    {
        $user = auth()->user();
        if (!$user->hasPermissionTo('blog_delete')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لحذف المقالات');
        }

        $blog->delete();
        Helpers::notifyAdmins([
            'title' => 'تم حذف قسم',
            'message' => 'المقال ' . $blog->title . ' تم حذف بواسطة ' . auth()->user()->name,
            'href' => route('blogs.index'),
        ]);
        return redirect()->route('blogs.index')->with('success', 'تم حذف المقال بنجاح');
    }
}
