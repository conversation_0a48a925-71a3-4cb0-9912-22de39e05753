<?php

namespace Modules\Blog\Controller;

use App\Http\Controllers\Controller;
use App\Helpers\Helpers;
use Modules\Blog\Requests\StoreBlogCategoryRequest;
use Modules\Blog\Requests\UpdateBlogCategoryRequest;
use Illuminate\Http\Request;
use Modules\Blog\Models\BlogCategory;

class BlogCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (!$request->user()->hasPermissionTo('blog_category_read')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لعرض الأقسام');
        }
        $parentCategories = BlogCategory::whereNull('parent_id')->get();
        $categories = BlogCategory::withCount('blogs')->paginate(20);
        return view('modules.blog_category.index', compact('parentCategories', 'categories'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return redirect()->route('blog-categories.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBlogCategoryRequest $request)
    {
        if (!$request->user()->hasPermissionTo('blog_category_create')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لعرض الأقسام');
        }
        $data = $request->all();
        $category = BlogCategory::create($data);
        return back()->with('success', 'تم إنشاء القسم بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(BlogCategory $blogCategory)
    {
        return redirect()->route('blog-categories.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BlogCategory $blogCategory)
    {
        return redirect()->route('blog-categories.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBlogCategoryRequest $request, BlogCategory $blogCategory)
    {
        if (!$request->user()->hasPermissionTo('blog_category_update')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لتحديث الأقسام');
        }
        if (count($blogCategory->children) > 0 && $request->parent_id != null) {
            return back()->with('error', 'القسم لديه أقسام فرعية لا يمكن تحويلة لقسم فرعي');
        }
        $data = $request->validated();
        $blogCategory->update($data);
        return back()->with('success', 'تم تحديث القسم بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BlogCategory $blogCategory)
    {
        $user = auth()->user();
        if (!$user->hasPermissionTo('blog_category_delete')) {
            return redirect()->route('dashboard')->with('error', 'ليس لديك صلاحية لحذف الأقسام');
        }
        if ($blogCategory->children->count() > 0) {
            return back()->with('error', 'القسم لديه أقسام فرعية لا يمكن حذفة');
        }
        if($blogCategory->blogs->count() > 0) {
            return back()->with('error', 'لا يمكن حذف القسم لأنه يحتوي على مقالات');
        }
        $blogCategory->delete();
        Helpers::notifyAdmins([
            'title' => 'تم حذف قسم',
            'message' => 'القسم ' . $blogCategory->title . ' تم حذف بواسطة ' . auth()->user()->name,
            'href' => route('blog-categories.index'),
        ]);
        return redirect()->route('blog-categories.index')->with('success', 'تم حذف القسم بنجاح');
    }
}
