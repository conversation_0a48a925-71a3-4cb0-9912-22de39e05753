<?php

namespace Modules\Blog\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateBlogRequest extends FormRequest
{
    

   
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'summary' => 'nullable|string',
            'is_published' => 'sometimes',
            'image' => 'nullable|file',
            'meta_title' => 'required|string',
            'meta_description' => 'required|string',
            'meta_keywords' => 'required|string',
            'categories' => 'required|exists:blog_categories,id',
        ];
    }

    public function authorize(): bool
    {
        return true;
    }
}
