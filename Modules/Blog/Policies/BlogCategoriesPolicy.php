<?php

namespace Modules\Blog\Policies;

use App\Models\User;
use Modules\Blog\Models\BlogCategory;

class BlogCategoryPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('blog_category_read');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, BlogCategory $model): bool
    {
        return $user->hasPermissionTo('blog_category_read');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('blog_category_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, BlogCategory $model): bool
    {
        return $user->hasPermissionTo('blog_category_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, BlogCategory $model): bool
    {
        return $user->hasPermissionTo('blog_category_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, BlogCategory $model): bool
    {
        return $user->hasPermissionTo('blog_category_delete');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, BlogCategory $model): bool
    {
        return  $user->hasPermissionTo('blog_category_delete');
    }
}
