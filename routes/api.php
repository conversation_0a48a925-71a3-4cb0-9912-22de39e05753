<?php

use App\Http\Controllers\Api\AppMenuApiController;
use App\Http\Controllers\Api\AppNotificationApiController;
use App\Http\Controllers\Api\BannerAdApiController;
use App\Http\Controllers\Api\BrandApiController;
use App\Http\Controllers\Api\CategoryApiController;
use App\Http\Controllers\Api\FollowApiController;
use App\Http\Controllers\Api\LocationApiController;
use App\Http\Controllers\Api\ProductApiController;
use App\Http\Controllers\Api\ProductCommentApiController;
use App\Http\Controllers\Api\SettingsApiController;
use App\Http\Controllers\Api\TagApiController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Api\UserRateApiController;
use App\Http\Controllers\API\PageController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\TelegramErrorBotController;
use App\Http\Middleware\OptionalAuthentication;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application.
| Routes are organized into logical groups for better readability.
|
*/

/**
 * ========================================================================
 * Authentication Routes
 * ========================================================================
 */
Route::prefix('auth')->group(function () {
    // Public auth routes
    Route::post('/register', [UserApiController::class, 'register']);
    Route::post('/login', [UserApiController::class, 'login']);
    Route::post('/social', [UserApiController::class, 'social']);
    Route::post('/reset-password', [UserApiController::class, 'resetPassword']);
    Route::post('/forget-password', [UserApiController::class, 'forgetPassword']);
    
    // Protected auth routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [UserApiController::class, 'logout']);
    });
});

/**
 * ========================================================================
 * Email & OTP Verification Routes
 * ========================================================================
 */
// Public email verification
Route::get('/email/verify/{id}/{hash}', [App\Http\Controllers\Api\EmailVerificationController::class, 'verify'])
    ->name('verification.verify');

// Protected verification routes
Route::middleware('auth:sanctum')->group(function () {
    // Email verification
    Route::post('/email/verification-notification', [App\Http\Controllers\Api\EmailVerificationController::class, 'sendVerificationEmail'])
        ->middleware(['throttle:6,1'])
        ->name('verification.send');

    // OTP verification
    Route::prefix('email/otp')->group(function () {
        Route::post('/send', [App\Http\Controllers\Api\OtpVerificationController::class, 'sendOtp'])
            ->middleware(['throttle:6,1'])
            ->name('verification.otp.send');
        Route::post('/verify', [App\Http\Controllers\Api\OtpVerificationController::class, 'verifyOtp'])
            ->middleware(['throttle:6,1'])
            ->name('verification.otp.verify');
    });
});

/**
 * ========================================================================
 * User Profile Routes
 * ========================================================================
 */
Route::middleware('auth:sanctum')->prefix('user')->group(function () {
    Route::get('/', [UserApiController::class, 'profile']);
    Route::get('/{hashId}', [UserApiController::class, 'userProfile']);
    Route::put('/', [UserApiController::class, 'updateProfile']);
    Route::post('/update', [UserApiController::class, 'updateProfile']);
    Route::delete('/delete', [UserApiController::class, 'deleteAccount']);

    // User achievements
    Route::get('/achievements', [UserApiController::class, 'getUserAchievements']);
});

/**
 * ========================================================================
 * Category Routes
 * ========================================================================
 */
Route::prefix('categories')->group(function () {
    Route::get('/', [CategoryApiController::class, 'index']);
    Route::get('/parent', [CategoryApiController::class, 'getParentCategories']);
    Route::get('/{category}', [CategoryApiController::class, 'show']);
    Route::get('/{category}/children', [CategoryApiController::class, 'getChildren']);
});

/**
 * ========================================================================
 * Location Routes
 * ========================================================================
 */
Route::prefix('locations')->group(function () {
    Route::get('/', [LocationApiController::class, 'index']);
    Route::get('/{location}/children', [LocationApiController::class, 'getChildren']);
});

/**
 * ========================================================================
 * Content Routes (Pages, Menus, Banners)
 * ========================================================================
 */
// Pages
Route::prefix('pages')->group(function () {
    Route::get('/', [PageController::class, 'index']);
    Route::get('/{slug}', [PageController::class, 'show']);
});

// App Menus
Route::prefix('app-menus')->group(function () {
    Route::get('/', [AppMenuApiController::class, 'index']);
    Route::get('/{id}', [AppMenuApiController::class, 'show']);
});

// Banner Ads
Route::prefix('banner-ads')->group(function () {
    Route::get('/{screen}', [BannerAdApiController::class, 'getForScreen']);
});

/**
 * ========================================================================
 * Product Routes
 * ========================================================================
 */
// Products with optional authentication
Route::middleware(OptionalAuthentication::class)->group(function () {
    Route::get('products', [ProductApiController::class, 'index']);
    Route::get('products/{product}', [ProductApiController::class, 'show']);
    Route::get('products/{product}/similar', [ProductApiController::class, 'similarProducts']);
});

// Protected product routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('products', [ProductApiController::class, 'store']);
    Route::put('products/{product}', [ProductApiController::class, 'update']);
    Route::delete('products/{product}', [ProductApiController::class, 'destroy']);
    Route::post('product/favorite/{product}', [ProductApiController::class, 'favorite']);
    Route::get('favorites', [ProductApiController::class, 'favorites']);
});

// User Products with optional authentication
Route::get('user/products', function (Request $request) {
    if ($request->bearerToken()) {
        // Try to authenticate with token if present
        try {
            Auth::shouldUse('sanctum');
            Auth::authenticate();
        } catch (\Exception $e) {
            // Token is invalid, continue as unauthenticated
        }
    }

    return app(ProductApiController::class)->userProducts($request);
});

/**
 * ========================================================================
 * Product Comments Routes
 * ========================================================================
 */
// Public comment routes
Route::get('product-comments', [ProductCommentApiController::class, 'index']);

// Protected comment routes
Route::middleware('auth:sanctum')->prefix('product-comments')->group(function () {
    Route::get('/{productComment}', [ProductCommentApiController::class, 'show']);
    Route::post('/', [ProductCommentApiController::class, 'store']);
    Route::put('/{productComment}', [ProductCommentApiController::class, 'update']);
    Route::delete('/{productComment}', [ProductCommentApiController::class, 'destroy']);
    Route::post('/{productComment}/reply', [ProductCommentApiController::class, 'reply']);
});

/**
 * ========================================================================
 * Notification Routes
 * ========================================================================
 */
Route::middleware('auth:sanctum')->prefix('notifications')->group(function () {
    Route::get('/', [AppNotificationApiController::class, 'index']);
    Route::post('/{notification}/read', [AppNotificationApiController::class, 'markAsRead']);
    Route::post('/{notification}/delete', [AppNotificationApiController::class, 'delete']);
    Route::post('/mark-all-as-read', [AppNotificationApiController::class, 'markAllAsRead']);
    Route::post('/unread-count', [AppNotificationApiController::class, 'unreadCount']);
    Route::post('/save-fcm-token', [AppNotificationApiController::class, 'saveFcmToken']);
    Route::post('/delete-fcm-token', [AppNotificationApiController::class, 'deleteFcmToken']);
});

/**
 * ========================================================================
 * Settings Routes
 * ========================================================================
 */
Route::middleware(OptionalAuthentication::class)->group(function () {
    Route::post('/settings', [SettingsApiController::class, 'getSettings']);
});

/**
 * ========================================================================
 * Tag Routes
 * ========================================================================
 */
Route::get('tags', [TagApiController::class, 'index']);

/**
 * ========================================================================
 * Brand Routes
 * ========================================================================
 */
Route::get('brands', [BrandApiController::class, 'index']);

/**
 * ========================================================================
 * User Rating Routes
 * ========================================================================
 */
// Public rating routes
Route::prefix('ratings')->group(function () {
    Route::get('/', [UserRateApiController::class, 'index']);
    Route::get('/{id}', [UserRateApiController::class, 'show']);
    Route::get('/user/{userHashId}', [UserRateApiController::class, 'getUserRatings']);
});

// Protected rating routes
Route::middleware('auth:sanctum')->prefix('ratings')->group(function () {
    Route::post('/', [UserRateApiController::class, 'store']);
    Route::put('/{id}', [UserRateApiController::class, 'update']);
    Route::delete('/{id}', [UserRateApiController::class, 'destroy']);
    Route::get('/my/list', [UserRateApiController::class, 'getMyRatings']);
});

/**
 * ========================================================================
 * Social Interaction Routes
 * ========================================================================
 */
Route::middleware('auth:sanctum')->group(function () {
    // Follows
    Route::prefix('follow')->group(function () {
        Route::post('/', [FollowApiController::class, 'toggleFollow']);
        Route::get('/list', [FollowApiController::class, 'getFollows']);
    });

    // Reports
    Route::post('/reports', [ReportController::class, 'store']);
});

/**
 * ========================================================================
 * Telegram Webhook Routes
 * ========================================================================
 */
Route::prefix('telegram/webhook')->group(function () {
    Route::post('error', [TelegramErrorBotController::class, 'handleUpdate']);
});

/**
 * ========================================================================
 * Broadcasting Routes
 * ========================================================================
 */
Broadcast::routes(['middleware' => ['auth:sanctum']]);