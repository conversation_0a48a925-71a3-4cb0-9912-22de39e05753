<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Schedule achievement checks
Schedule::command('achievements:check --all')
    ->daily()
    ->at('02:00')
    ->description('Check achievements for all users daily');

Schedule::command('achievements:check --all')
    ->weekly()
    ->sundays()
    ->at('03:00')
    ->description('Weekly comprehensive achievement check');
