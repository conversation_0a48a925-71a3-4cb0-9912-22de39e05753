<?php

use App\Http\Controllers\API\PageController;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\ShortUrlController;
use App\Http\Controllers\front\Front2Controller;
use App\Http\Controllers\front\Front2AuthController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\Web\NotificationController;
use App\Http\Middleware\TrackUserSession;
use Illuminate\Support\Facades\Route;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

Route::middleware(TrackUserSession::class)
    ->name('web.')
    ->group(function () {
        // Auth routes
        Route::prefix('auth')
            ->name('auth.')
            ->group(function () {
                Route::controller(Front2AuthController::class)->group(function () {
                    // Guest routes
                    Route::middleware('guest')->group(function () {
                        Route::get('/login', 'showLoginForm')->name('login');
                        Route::post('/login', 'login');
                        Route::get('/register', 'showRegistrationForm')->name('register');
                        Route::post('/register', 'register');
                        Route::get('/forgot-password', 'showForgotPasswordForm')->name('forgot-password');
                        Route::post('/forgot-password', 'sendResetLinkEmail')->name('forgot-password.send');
                        Route::get('/reset-password/{token}', 'showResetPasswordForm')->name('password.reset');
                        Route::post('/reset-password', 'resetPassword')->name('reset-password');
                    });

                    // Auth routes
                    Route::middleware('web.auth')->group(function () {
                        Route::post('/logout', 'logout')->name('logout');
                        Route::get('/email/verify', 'verificationNotice')->name('verification.notice');
                        Route::post('/email/verification-notification', 'sendVerificationEmail')->name('verification.send');
                    });

                    // Verification route (special case)
                    Route::get('/email/verify/{id}/{hash}', 'verifyEmail')
                        ->middleware(['signed', 'throttle:6,1'])
                        ->name('verification.verify');
                });
            });

        // Main front2 routes
        Route::controller(Front2Controller::class)->group(function () {
            // Public routes
            Route::get('/', 'index')->name('index');
            Route::get('/locale/{locale}', 'setLocale')->name('setLocale');
            Route::get('/theme/{theme}', 'setTheme')->name('setTheme');
            Route::get('/categories', 'categories')->name('categories');
            Route::get('/category/{slug}', 'category')->name('category');
            Route::get('/search', 'search')->name('search');
            Route::get('/product/{slug}', 'product')->name('product');
            Route::get('/seller/{username}', 'sellerProfile')->name('sellerProfile');
            Route::get('/about', 'about')->name('about');
            Route::get('/contact', 'contactUs')->name('contactUs');
            Route::post('/newsletter/subscribe', 'subscribeNewsletter')->name('newsletter.subscribe');

            // Auth required routes
            Route::middleware(['web.auth'])->group(function () {
                Route::get('/favorites', 'favorites')->name('favorites');
                Route::post('/favorites/toggle/{productId}', 'toggleFavorite')->name('toggleFavorite');
                Route::post('/product/{product}/comment', 'addComment')->name('product.comment');
                Route::get('/report/{productId}', 'reportProduct')->name('report');
                Route::post('/report/{productId}', 'submitReport')->name('submitReport');
                Route::post('/report', 'storeReport')->name('report.store');

                // Notification routes
                Route::prefix('notifications')
                    ->name('notifications.')
                    ->controller(NotificationController::class)
                    ->group(function () {
                        Route::get('/', 'index')->name('index');
                        Route::get('/{notification}', 'show')->name('show');
                        Route::post('/{notification}/read', 'markAsRead')->name('read');
                        Route::post('/mark-all-read', 'markAllAsRead')->name('mark-all-read');
                        Route::delete('/{notification}', 'destroy')->name('destroy');
                    });

                // User profile routes
                Route::get('/profile', 'profileShow')->name('profile.show');
                Route::get('/profile/edit', 'profileEdit')->name('profile.edit');
                Route::put('/profile', 'profileUpdate')->name('profile.update');
                Route::get('/profile/products', 'profileProducts')->name('profile.products');
                Route::get('/profile/favorites', 'profileFavorites')->name('profile.favorites');
                Route::get('/profile/followers', 'profileFollowers')->name('profile.followers');
                Route::get('/profile/following', 'profileFollowing')->name('profile.following');
                Route::get('/profile/followed-products', 'profileFollowedProducts')->name('profile.followed_products');
                Route::post('/profile/toggle-follow', 'toggleFollow')->name('toggle.follow');
                Route::post('/profile/toggle-favorite/{productId}', 'profileToggleFavorite')->name('profileToggleFavorite');
                Route::get('/profile/product/{id}/edit', 'editProduct')->name('editProduct');
                Route::put('/profile/product/{id}', 'updateProduct')->name('updateProduct');
                Route::delete('/profile/product/{id}', 'deleteProduct')->name('deleteProduct');
                Route::put('/profile/product/{id}/mark-sold', 'markProductSold')->name('markProductSold');

                Route::get('/verification/form', [VerificationController::class, 'showVerificationForm'])->name('verification.form');
                Route::post('/verification/submit', [VerificationController::class, 'submitVerification'])->name('verification.submit');
            });

            // Email verification required routes
            Route::middleware(['web.auth', 'verified'])->group(function () {
                Route::get('/products/create', 'createProduct')->name('products.create');
                Route::post('/products', 'storeProduct')->name('products.store');
            });
        });
    });

// Email verification and password reset pages
Route::get('/email/verify/success', function () {
    return view('auth.verification-success');
})->name('verification.success');

Route::get('/Reset-Pass/{token}', function ($token) {
    return view('auth.reset-password', ['token' => $token]);
})->name('password.reset.form');

Route::get('/password/reset/success', function () {
    return view('auth.reset-success');
})->name('password.reset.success');

Route::get('/lang/{locale}', [LanguageController::class, 'swap']);

Route::get('page/{slug}', [PageController::class, 'open'])->name('page');

Route::get('/srt/{shortCode}', [ShortUrlController::class, 'redirect']);
