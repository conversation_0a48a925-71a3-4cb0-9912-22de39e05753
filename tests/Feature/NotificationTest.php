<?php

namespace Tests\Feature;

use App\Models\AppNotification;
use App\Models\AppUserNotification;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class NotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a user for testing
        $this->user = User::factory()->create([
            'email_verified_at' => now(),
        ]);
    }

    /** @test */
    public function user_can_view_notifications_index()
    {
        // Create some notifications for the user
        $notification = AppNotification::factory()->create();
        AppUserNotification::create([
            'app_notification_id' => $notification->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('web.notifications.index'));

        $response->assertStatus(200);
        $response->assertSee($notification->title);
        $response->assertSee('الإشعارات');
    }

    /** @test */
    public function user_can_view_specific_notification()
    {
        $notification = AppNotification::factory()->create();
        AppUserNotification::create([
            'app_notification_id' => $notification->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('web.notifications.show', $notification));

        $response->assertStatus(200);
        $response->assertSee($notification->title);
        $response->assertSee($notification->body);
    }

    /** @test */
    public function user_can_mark_notification_as_read()
    {
        $notification = AppNotification::factory()->create();
        $userNotification = AppUserNotification::create([
            'app_notification_id' => $notification->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->post(route('web.notifications.read', $notification));

        $response->assertRedirect();
        $this->assertNotNull($userNotification->fresh()->read_at);
    }

    /** @test */
    public function user_can_mark_all_notifications_as_read()
    {
        // Create multiple notifications
        $notification1 = AppNotification::factory()->create();
        $notification2 = AppNotification::factory()->create();
        
        AppUserNotification::create([
            'app_notification_id' => $notification1->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);
        
        AppUserNotification::create([
            'app_notification_id' => $notification2->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->post(route('web.notifications.mark-all-read'));

        $response->assertRedirect();
        
        // Check that all notifications are marked as read
        $unreadCount = AppUserNotification::where('user_id', $this->user->id)
            ->whereNull('read_at')
            ->count();
        
        $this->assertEquals(0, $unreadCount);
    }

    /** @test */
    public function user_can_delete_notification()
    {
        $notification = AppNotification::factory()->create();
        $userNotification = AppUserNotification::create([
            'app_notification_id' => $notification->id,
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->delete(route('web.notifications.destroy', $notification));

        $response->assertRedirect();
        $this->assertNull($userNotification->fresh());
    }

    /** @test */
    public function guest_cannot_access_notifications()
    {
        $response = $this->get(route('web.notifications.index'));
        
        // Should redirect to login
        $response->assertRedirect();
    }

    /** @test */
    public function user_cannot_access_other_users_notifications()
    {
        $otherUser = User::factory()->create();
        $notification = AppNotification::factory()->create();
        
        // Create notification for other user
        AppUserNotification::create([
            'app_notification_id' => $notification->id,
            'user_id' => $otherUser->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->get(route('web.notifications.show', $notification));

        $response->assertStatus(404);
    }
}
