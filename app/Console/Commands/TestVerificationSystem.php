<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Route;

class TestVerificationSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'verification:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the verification system components';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Verification System...');
        $this->newLine();

        // Test 1: Check verification requirements
        $this->info('1. Checking verification requirements...');
        $requirements = \App\Models\Settings::where('key', 'verification_requirements')->first();
        if ($requirements) {
            $this->info('✅ Verification requirements found');
            $data = json_decode($requirements->value, true);
            $this->info('   - Number of requirements: ' . count($data));
        } else {
            $this->error('❌ Verification requirements not found');
        }

        // Test 2: Check User model verification methods
        $this->newLine();
        $this->info('2. Checking User model verification methods...');
        $user = new \App\Models\User();

        $methods = ['isVerified', 'hasPendingVerification', 'markAsVerified', 'markAsUnverified'];
        foreach ($methods as $method) {
            if (method_exists($user, $method)) {
                $this->info("✅ {$method} method exists");
            } else {
                $this->error("❌ {$method} method missing");
            }
        }

        // Test 3: Check UserVerification model
        $this->newLine();
        $this->info('3. Checking UserVerification model...');
        if (class_exists('App\Models\UserVerification')) {
            $this->info('✅ UserVerification model exists');

            $verification = new \App\Models\UserVerification();
            $relationships = ['user', 'reviewer'];
            foreach ($relationships as $relationship) {
                if (method_exists($verification, $relationship)) {
                    $this->info("✅ {$relationship} relationship exists");
                } else {
                    $this->error("❌ {$relationship} relationship missing");
                }
            }
        } else {
            $this->error('❌ UserVerification model missing');
        }

        // Test 4: Check database tables
        $this->newLine();
        $this->info('4. Checking database tables...');
        try {
            if (Schema::hasTable('users')) {
                $this->info('✅ users table exists');

                $columns = ['is_verified', 'verified_at', 'verified_by', 'verification_notes'];
                $hasColumns = Schema::hasColumns('users', $columns);
                if ($hasColumns) {
                    $this->info('✅ verification columns exist in users table');
                } else {
                    $this->error('❌ verification columns missing in users table');
                }
            } else {
                $this->error('❌ users table missing');
            }

            if (Schema::hasTable('user_verifications')) {
                $this->info('✅ user_verifications table exists');
            } else {
                $this->error('❌ user_verifications table missing');
            }
        } catch (\Exception $e) {
            $this->error('❌ Database connection error: ' . $e->getMessage());
        }

        // Test 5: Check Livewire components
        $this->newLine();
        $this->info('5. Checking Livewire components...');
        $components = [
            'App\Livewire\VerificationRequirementsForm',
            'App\Livewire\VerificationRequestsTable',
            'App\Livewire\VerificationModal',
            'App\Livewire\UsersTable'
        ];

        foreach ($components as $component) {
            if (class_exists($component)) {
                $this->info('✅ ' . class_basename($component) . ' exists');
            } else {
                $this->error('❌ ' . class_basename($component) . ' missing');
            }
        }

        // Test 6: Check routes
        $this->newLine();
        $this->info('6. Checking verification routes...');
        $routes = Route::getRoutes();
        $verificationRoutes = [];

        foreach ($routes as $route) {
            if (strpos($route->uri(), 'verification') !== false) {
                $verificationRoutes[] = $route->uri();
            }
        }

        if (count($verificationRoutes) > 0) {
            $this->info('✅ Verification routes found (' . count($verificationRoutes) . ' routes)');
        } else {
            $this->error('❌ No verification routes found');
        }

        $this->newLine();
        $this->info('=== Test Complete ===');
        $this->info('If all tests pass, the verification system should be working correctly!');

        return 0;
    }
}
