<?php

namespace App\Console\Commands;

use App\Jobs\ProcessAchievementQueueJob;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * Check Achievements Command
 * 
 * This command can be run periodically to check achievements for all users
 * or for a specific user. It dispatches the ProcessAchievementQueueJob.
 */
class CheckAchievementsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'achievements:check 
                            {--user-id= : Check achievements for a specific user ID}
                            {--all : Check achievements for all users}
                            {--sync : Run synchronously instead of queuing}';

    /**
     * The console command description.
     */
    protected $description = 'Check and award achievements to users';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $userId = $this->option('user-id');
        $checkAll = $this->option('all');
        $runSync = $this->option('sync');

        if (!$userId && !$checkAll) {
            $this->error('You must specify either --user-id or --all option');
            return self::FAILURE;
        }

        if ($userId && $checkAll) {
            $this->error('You cannot specify both --user-id and --all options');
            return self::FAILURE;
        }

        try {
            if ($userId) {
                return $this->checkUserAchievements($userId, $runSync);
            } else {
                return $this->checkAllUsersAchievements($runSync);
            }
        } catch (\Exception $e) {
            $this->error('An error occurred: ' . $e->getMessage());
            Log::error('CheckAchievementsCommand failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return self::FAILURE;
        }
    }

    /**
     * Check achievements for a specific user
     */
    private function checkUserAchievements(int $userId, bool $runSync): int
    {
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return self::FAILURE;
        }

        $this->info("Checking achievements for user: {$user->name} (ID: {$userId})");

        if ($runSync) {
            // Run synchronously
            $achievementService = app(\App\Services\AchievementConditionsService::class);
            $awardedAchievements = $achievementService->checkAllAchievementsForUser($user);
            
            if (!empty($awardedAchievements)) {
                $this->info("Awarded achievements: " . implode(', ', $awardedAchievements));
            } else {
                $this->info("No new achievements awarded");
            }
        } else {
            // Queue the job
            ProcessAchievementQueueJob::dispatchForUser($user);
            $this->info("Achievement check job queued for user {$userId}");
        }

        return self::SUCCESS;
    }

    /**
     * Check achievements for all users
     */
    private function checkAllUsersAchievements(bool $runSync): int
    {
        $userCount = User::count();
        
        $this->info("Checking achievements for all users ({$userCount} users)");
        
        if ($runSync) {
            $this->warn("Running synchronously for all users may take a long time...");
            
            if (!$this->confirm('Do you want to continue?')) {
                return self::SUCCESS;
            }

            // Run synchronously
            $achievementService = app(\App\Services\AchievementConditionsService::class);
            $results = $achievementService->checkAchievementsForAllUsers();
            
            $this->info("Processing completed:");
            $this->info("- Processed users: {$results['processed_users']}");
            $this->info("- Total achievements awarded: {$results['total_achievements_awarded']}");
            
            if (!empty($results['errors'])) {
                $this->warn("- Errors encountered: " . count($results['errors']));
                foreach ($results['errors'] as $error) {
                    $this->error("  User {$error['user_id']}: {$error['error']}");
                }
            }
        } else {
            // Queue the job
            ProcessAchievementQueueJob::dispatchForAllUsers();
            $this->info("Achievement check job queued for all users");
        }

        return self::SUCCESS;
    }
}
