<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CreateModuleAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:module {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a new module with requests, policies, and resources';

    /**
     * Execute the console command.
     */
    public function handle()
    {
       
        $name = $this->argument('name');

        // Create the module
        $this->call('module:make', ['name' => [$name]]);

        // Create requests
        $this->call('module:make-request', ['name' => "Create{$name}Request", 'module' => $name]);
        $this->call('module:make-request', ['name' => "Update{$name}Request", 'module' => $name]);

        // Create migrations
        $this->call('module:make-migration', ['name' => "create_{$name}_table", 'module' => $name]);

        // Create policy
        $this->call('module:make-policy', ['name' => "{$name}Policy", 'module' => $name]);

        //Create Model
        $this->call('module:make-model', ['model' => $name, 'module' => $name]);

        // Create data class
        //! removed because it is not needed
        // $this->call('make:dto', ['name' => "{$name}Data", 'module' => $name]);

        // Create resources
        $this->call('module:make-resource', ['name' => "{$name}Resource", 'module' => $name]);
        $this->call('module:make-resource', ['name' => "{$name}Collection", 'module' => $name]);

        $this->info("Module {$name} created with model, migartion, requests, policies, and resources.");
    }
}
