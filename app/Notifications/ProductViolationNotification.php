<?php

namespace App\Notifications;

use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ProductViolationNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected Product $product)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Your Product Requires Review')
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('Your product "' . $this->product->title . '" requires additional review by our team.')
            ->line('Our automated system has flagged some content that may not comply with our guidelines.')
            ->line('Your product will remain in "pending" status until our team reviews it.')
            ->action('View Your Product', url('/products/' . $this->product->id))
            ->line('Thank you for your patience and understanding.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Product Requires Review',
            'body' => 'Your product "' . $this->product->title . '" requires additional review by our team.',
            'product_id' => $this->product->id,
            'type' => 'product_review_required'
        ];
    }
}
