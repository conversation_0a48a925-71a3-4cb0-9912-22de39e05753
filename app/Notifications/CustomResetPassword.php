<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class CustomResetPassword extends ResetPassword implements ShouldQueue
{
    use Queueable;

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = url(route('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ], false));

        $message = "<p style='text-align:right;'>مرحبًا، يُرجى اتّباع الرابط التالي لإعادة تعيين كلمة مرور حراجي لحسابك " . $notifiable->email . '</p>';
        $message .= "<p style='text-align:right;'>" . $url . '</p>';
        $message .= "<p style='text-align:right;'>ويمكنك تجاهل هذا البريد الإلكتروني إذا لم تطلب إعادة ضبط كلمة المرور.</p>";
        $message .= "<p style='text-align:right;'>شكراً</p>";
        $message .= "<p style='text-align:right;'>فريق حراجي</p>";

        $post = [
            'from' => [
                'email' => '<EMAIL>',
                'name' => 'حراجي',
            ],
            'to' => [
                [
                    'email' => $notifiable->email,
                ],
            ],
            'subject' => 'إعادة ضبط كلمة المرور للتطبيق حراجي',
            'text' => strip_tags($message),
            'html' => $message,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://send.api.mailtrap.io/api/send');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json', 'Authorization: Bearer 1be43f0e5fb70c4fa4e5481319bc44fd']);
        $result = curl_exec($ch);

        // Return empty mail message as we're handling the email sending manually
        return (new MailMessage)->subject('');
    }
} 