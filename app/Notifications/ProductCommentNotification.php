<?php

namespace App\Notifications;

use App\Models\Product;
use App\Models\ProductComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ProductCommentNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(protected Product $product, protected ProductComment $comment)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('تعليق جديد على منتج تتابعه'))
            ->greeting(__('مرحباً') . ' ' . $notifiable->name . '!')
            ->line(__('هناك تعليق جديد على المنتج') . ' "' . $this->product->title . '"')
            ->line(__('التعليق') . ': ' . $this->comment->text)
            ->line(__('من') . ': ' . $this->comment->user->name)
            ->action(__('عرض المنتج'), url('/products/' . $this->product->slug))
            ->line(__('شكراً لاستخدامك منصتنا!'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => __('تعليق جديد على منتج تتابعه'),
            'body' => __('هناك تعليق جديد على المنتج') . ' "' . $this->product->title . '"',
            'product_id' => $this->product->id,
            'comment_id' => $this->comment->id,
            'commenter_id' => $this->comment->user_id,
            'commenter_name' => $this->comment->user->name,
            'type' => 'product_comment'
        ];
    }
}
