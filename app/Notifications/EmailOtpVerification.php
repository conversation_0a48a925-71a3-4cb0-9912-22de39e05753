<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailOtpVerification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The OTP code.
     *
     * @var string
     */
    protected $otp;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $otp)
    {
        $this->otp = $otp;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->from('<EMAIL>', 'حراجي')
                    ->subject('رمز التحقق من حساب حراجي')
                    ->line('مرحبًا،')
                    ->line('رمز التحقق الخاص بك هو:')
                    ->line($this->otp)
                    ->line('هذا الرمز صالح لمدة 10 دقائق فقط.')
                    ->line('إذا لم تطلب هذا الرمز، يرجى تجاهل هذا البريد الإلكتروني.')
                    ->line('شكراً،')
                    ->line('فريق حراجي');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
