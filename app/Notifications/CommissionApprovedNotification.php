<?php

namespace App\Notifications;

use App\Models\Commission;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommissionApprovedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $commission;

    /**
     * Create a new notification instance.
     */
    public function __construct(Commission $commission)
    {
        $this->commission = $commission;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->from('<EMAIL>', 'حراجي')
            ->subject('تم قبول دفع العمولة')
            ->line('مرحباً ' . $notifiable->name . '،')
            ->line('تم قبول دفع العمولة الخاصة بك للمنتج: ' . $this->commission->product->title)
            ->line('مبلغ العمولة: ' . $this->commission->amount . ' ريال')
            ->line('طريقة الدفع: ' . $this->commission->payment_method_label)
            ->when($this->commission->review_notes, function ($message) {
                return $message->line('ملاحظات الإدارة: ' . $this->commission->review_notes);
            })
            ->line('شكراً لك على استخدام منصة حراجي!')
            ->line('فريق حراجي');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'تم قبول دفع العمولة',
            'message' => 'تم قبول دفع العمولة للمنتج: ' . $this->commission->product->title,
            'type' => 'success',
            'href' => '/profile/commissions/' . $this->commission->hashId,
            'commission_id' => $this->commission->id,
            'amount' => $this->commission->amount,
        ];
    }
}
