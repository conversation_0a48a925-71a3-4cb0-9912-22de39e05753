<?php

namespace App\Notifications;

use App\Models\Achievement;
use App\Models\AppNotification;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

/**
 * Achievement Awarded Notification
 * 
 * This notification is sent when a user receives an achievement.
 * It integrates with the existing AppNotification system.
 */
class AchievementAwardedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $achievementSlug;
    protected ?Achievement $achievement;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $achievementSlug)
    {
        $this->achievementSlug = $achievementSlug;
        $this->achievement = Achievement::where('slug', $achievementSlug)->first();
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $achievementName = $this->achievement?->name ?? 'إنجاز جديد';
        $achievementDescription = $this->achievement?->description ?? 'تم منحك إنجاز جديد';

        return (new MailMessage)
            ->from('<EMAIL>', 'حراجي')
            ->subject('🏆 تهانينا! حصلت على إنجاز جديد')
            ->greeting('مبروك ' . $notifiable->name . '!')
            ->line('تم منحك إنجاز جديد في منصة حراجي:')
            ->line('**' . $achievementName . '**')
            ->line($achievementDescription)
            ->line('هذا الإنجاز يظهر في ملفك الشخصي ويساعد في بناء سمعتك على المنصة.')
            ->action('عرض الملف الشخصي', route('web.profile.show'))
            ->line('استمر في التميز واحصل على المزيد من الإنجازات!')
            ->line('شكراً لك على استخدام منصة حراجي')
            ->salutation('فريق حراجي');
    }

    /**
     * Get the array representation of the notification for database storage.
     */
    public function toDatabase(object $notifiable): array
    {
        $achievementName = $this->achievement?->name ?? 'إنجاز جديد';
        $achievementIcon = $this->achievement?->icon ?? 'ti-trophy';

        // Create AppNotification for this achievement
        $this->createAppNotification($notifiable, $achievementName, $achievementIcon);

        return [
            'title' => '🏆 إنجاز جديد!',
            'message' => 'تم منحك إنجاز: ' . $achievementName,
            'type' => 'achievement',
            'achievement_slug' => $this->achievementSlug,
            'achievement_id' => $this->achievement?->id,
            'achievement_name' => $achievementName,
            'achievement_icon' => $achievementIcon,
        ];
    }

    /**
     * Create an AppNotification for this achievement
     */
    private function createAppNotification(User $user, string $achievementName, string $achievementIcon): void
    {
        try {
            $appNotification = AppNotification::create([
                'title' => '🏆 إنجاز جديد!',
                'body' => 'مبروك! تم منحك إنجاز: ' . $achievementName,
                'type' => 'achievement',
                'data' => json_encode([
                    'achievement_slug' => $this->achievementSlug,
                    'achievement_id' => $this->achievement?->id,
                    'achievement_name' => $achievementName,
                    'achievement_icon' => $achievementIcon,
                    'user_id' => $user->id,
                ]),
                'sent_at' => now(),
                'platform' => 'all',
            ]);

            // Link notification to user
            $appNotification->users()->attach($user->id);

            Log::info('AppNotification created for achievement', [
                'user_id' => $user->id,
                'achievement_slug' => $this->achievementSlug,
                'notification_id' => $appNotification->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to create AppNotification for achievement', [
                'user_id' => $user->id,
                'achievement_slug' => $this->achievementSlug,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'achievement_awarded';
    }
}
