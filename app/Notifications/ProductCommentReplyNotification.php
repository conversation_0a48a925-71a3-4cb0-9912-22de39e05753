<?php

namespace App\Notifications;

use App\Models\Product;
use App\Models\ProductComment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class ProductCommentReplyNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected Product $product, 
        protected ProductComment $parentComment, 
        protected ProductComment $reply
    ) {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject(__('رد جديد على تعليقك'))
            ->greeting(__('مرحباً') . ' ' . $notifiable->name . '!')
            ->line(__('هناك رد جديد على تعليقك في المنتج') . ' "' . $this->product->title . '"')
            ->line(__('تعليقك الأصلي') . ': ' . $this->parentComment->text)
            ->line(__('الرد') . ': ' . $this->reply->text)
            ->line(__('من') . ': ' . $this->reply->user->name)
            ->action(__('عرض المنتج'), url('/products/' . $this->product->slug))
            ->line(__('شكراً لاستخدامك منصتنا!'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => __('رد جديد على تعليقك'),
            'body' => __('هناك رد جديد على تعليقك في المنتج') . ' "' . $this->product->title . '"',
            'product_id' => $this->product->id,
            'parent_comment_id' => $this->parentComment->id,
            'reply_id' => $this->reply->id,
            'replier_id' => $this->reply->user_id,
            'replier_name' => $this->reply->user->name,
            'type' => 'product_comment_reply'
        ];
    }
}
