<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AppNotification extends Model
{
    protected $fillable = [
        'title',
        'body',
        'type',
        'data',
        'sent_at',
        'platform',
    ];

    public function appUserNotifications()
    {
        return $this->hasMany(AppUserNotification::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'app_user_notifications');
    }



    public function readCount()
    {
        return $this->appUserNotifications()->whereNotNull('read_at')->count();
    }
}
