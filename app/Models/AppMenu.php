<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class AppMenu extends Model
{
    use HasFactory;
    use HasSlug;

    protected $fillable = [
        'parent_id', 
        'title', 
        'title_ar',
        'slug', 
        'group', 
        'icon', 
        'order', 
        'is_active', 
        'type', 
        'data'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
        'parent_id' => 'integer',
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug')
            ->doNotGenerateSlugsOnUpdate();
    }

    public function parent()
    {
        return $this->belongsTo(AppMenu::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(AppMenu::class, 'parent_id')->orderBy('order');
    }

    // Get all menu items that have no parent (top level)
    public static function topLevel()
    {
        return static::whereNull('parent_id')->orderBy('order')->get();
    }

    // Get a nested array structure of the menu for frontend use
    public function getNestedStructure()
    {
        $result = [
            'id' => $this->id,
            'title' => $this->title,
            'title_ar' => $this->title_ar,
            'slug' => $this->slug,
            'group' => $this->group,
            'icon' => $this->icon,
            'order' => $this->order,
            'is_active' => $this->is_active,
            'type' => $this->type,
            'data' => $this->data,
        ];

        $children = $this->children()->where('is_active', true)->get();
        
        if ($children->isNotEmpty()) {
            $result['children'] = $children->map(function ($child) {
                return $child->getNestedStructure();
            });
        }

        return $result;
    }
} 