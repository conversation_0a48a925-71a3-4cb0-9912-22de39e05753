<?php

namespace App\Models;

use App\Events\NotificationsEvent;
use Illuminate\Support\Facades\Storage;

class GlobalNotification
{
    protected static $filePath = 'global_notification.json';

    /**
     * Retrieve the notification data.
     */
    public static function getNotification()
    {
        if (!Storage::disk('local')->exists(self::$filePath)) {
            return [
                'title' => '',
                'description' => '',
                'percentage' => 0,
                'date' => now()
            ];
        }

        return json_decode(Storage::disk('local')->get(self::$filePath), true);
    }

    /**
     * Update the notification data.
     */
    public static function updateNotification($title, $description, $type, $percentage,$queue_id = null)
    {
        $data = [
            'title' => $title,
            'description' => $description,
            'percentage' => $percentage,
            'type' => $type,
            'queue_id' => $queue_id,
            'date' => now()
        ];
        //remove file content if exists
        if (Storage::disk('local')->exists(self::$filePath)) {
            Storage::disk('local')->delete(self::$filePath);
        }
        Storage::disk('local')->put(self::$filePath, json_encode($data, JSON_PRETTY_PRINT));
        NotificationsEvent::dispatch('Notification Updated');
    }
} 