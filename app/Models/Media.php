<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Media extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'model_type',
        'model_id',
        'url',
        'type',
        'order',
        'is_primary',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $hidden = ['created_by', 'updated_by', 'deleted_by'];

    protected $casts = [
        'is_primary' => 'boolean',
        'order' => 'integer',
    ];

    public function model()
    {
        return $this->morphTo();
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function deletedBy()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }
} 