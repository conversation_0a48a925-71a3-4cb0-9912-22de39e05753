<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ContactMessage extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'message',
        'name',
        'email',
        'user_id',
        'parent_id',
        'status',
        'extra',
        'file',
        'is_read',
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'extra' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function parent()
    {
        return $this->belongsTo(ContactMessage::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(ContactMessage::class, 'parent_id');
    }

    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }
    
    public function markAsRead()
    {
        $this->is_read = true;
        $this->save();
        
        return $this;
    }
}
