<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShortUrl extends Model
{
    use HasFactory;

    protected $fillable = ['original_url', 'short_code', 'clicks'];

    public function scopeSearch($query, $search)
    {
        return $query->where('original_url', 'like', '%' . $search . '%')
            ->orWhere('short_code', 'like', '%' . $search . '%');
    }
}
