<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Report extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'model_id',
        'text',
        'reporter_id',
        'status'
    ];


    public function reporter()
    {
        return $this->belongsTo(User::class, 'reporter_id');
    }

    // Dynamic relationship based on type
    public function reportable()
    {
        switch($this->type) {
            case 'product':
                return $this->belongsTo(Product::class, 'model_id');
            case 'comment':
                return $this->belongsTo(ProductComment::class, 'model_id');
            case 'user':
                return $this->belongsTo(User::class, 'model_id');
            case 'chat_message':
                return $this->belongsTo(\Modules\Chat\app\Models\ChatMessage::class, 'model_id');
            default:
                return null;
        }
    }
}
