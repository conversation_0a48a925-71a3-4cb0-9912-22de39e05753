<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CategoryAttribute extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'category_id',
        'name',
        'type', // text, select, number, checkbox, radio, etc.
        'options', // JSON field for select/radio/checkbox options
        'is_required',
        'is_filterable', // Can be used for filtering in search
        'validation', // JSON field for validation rules
        'order', // Display order
        'placeholder',
        'help_text'
    ];

    protected $casts = [
        'options' => 'array',
        'validation' => 'array',
        'is_required' => 'boolean',
        'is_filterable' => 'boolean'
    ];

    /**
     * Get the category that owns the attribute
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the attribute values for this attribute
     */
    public function attributeValues()
    {
        return $this->hasMany(AttributeValue::class);
    }
} 