<?php

namespace App\Models;

use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Otp extends Model
{
    use HasFactory;
    use WithHashId;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'code',
        'type',
        'expires_at',
        'is_used',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    /**
     * Get the user that owns the OTP.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the OTP is expired.
     *
     * @return bool
     */
    public function isExpired()
    {
        return $this->expires_at->isPast();
    }

    /**
     * Generate a new OTP code.
     *
     * @param User $user
     * @param string $type
     * @param int $length
     * @param int $expiresInMinutes
     * @return Otp
     */
    public static function generate(User $user, string $type = 'email', int $length = 6, int $expiresInMinutes = 10)
    {
        // Invalidate any existing OTPs of the same type
        self::where('user_id', $user->id)
            ->where('type', $type)
            ->where('is_used', false)
            ->update(['is_used' => true]);

        // Generate a new OTP code
        $code = '';
        for ($i = 0; $i < $length; $i++) {
            $code .= mt_rand(0, 9);
        }

        // Create a new OTP record
        return self::create([
            'user_id' => $user->id,
            'code' => $code,
            'type' => $type,
            'expires_at' => now()->addMinutes($expiresInMinutes),
            'is_used' => false,
        ]);
    }

    /**
     * Verify an OTP code.
     *
     * @param User $user
     * @param string $code
     * @param string $type
     * @return bool
     */
    public static function verify(User $user, string $code, string $type = 'email')
    {
        $otp = self::where('user_id', $user->id)
            ->where('code', $code)
            ->where('type', $type)
            ->where('is_used', false)
            ->where('expires_at', '>', now())
            ->first();

        if (!$otp) {
            return false;
        }

        // Mark the OTP as used
        $otp->update(['is_used' => true]);

        return true;
    }

    //* On create save hashID
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
        });
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }
}
