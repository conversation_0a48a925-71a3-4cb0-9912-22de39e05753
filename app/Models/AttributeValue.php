<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AttributeValue extends Model
{
    use HasFactory,  SoftDeletes;

    protected $fillable = [
        'category_attribute_id',
        'product_id', // or listing_id, ad_id, etc. depending on your system
        'value', // The actual value (could be text, number, or JSON for multiple selections)
    ];

    protected $casts = [
        'value' => 'json', // Cast to array when the value is JSON (for multi-select)
    ];

    /**
     * Get the category attribute this value belongs to
     */
    public function categoryAttribute()
    {
        return $this->belongsTo(CategoryAttribute::class);
    }

    /**
     * Get the product this attribute value belongs to
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }
} 