<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserSession extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'device_id',
        'device_type',
        'device_model',
        'device_os',
        'device_os_version',
        'app_version',
        'ip_address',
        'user_agent',
        'last_activity',
        'login_at',
        'logout_at',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'last_activity' => 'datetime',
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include sessions for a specific device.
     */
    public function scopeForDevice($query, $deviceId)
    {
        return $query->where('device_id', $deviceId);
    }

    /**
     * Mark the session as inactive.
     */
    public function markAsInactive()
    {
        $this->is_active = false;
        $this->logout_at = now();
        $this->save();

        return $this;
    }

    protected static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            // Set login_at if not provided
            if (empty($model->login_at)) {
                $model->login_at = now();
            }

            // Set last_activity if not provided
            if (empty($model->last_activity)) {
                $model->last_activity = now();
            }
        });
        static::created(function ($model) {
            $model->save();
        });
    }
}
