<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductMedia extends Model
{
    protected $fillable = [
        'name',
        'type',
        'mime_type',
        'file_size',
        'extension',
        'path',
        'thumbnail_path',
        'small_path',
        'watermarked_path',
        'order',
        'is_primary',
        'product_id'
    ];

    protected $casts = [
        'is_primary' => 'boolean',
        'file_size' => 'integer',
        'order' => 'integer'
    ];
    //remove timestamps
    public $timestamps = false;

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}