<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BannerAdClick extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_ad_id',
        'user_id',
        'ip_address',
        'user_agent',
        'device_type',
        'platform',
        'referrer'
    ];

    /**
     * Get the banner ad that was clicked
     */
    public function bannerAd()
    {
        return $this->belongsTo(BannerAd::class);
    }

    /**
     * Get the user who clicked the ad
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}