<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BannerAdImpression extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_ad_id',
        'user_id',
        'ip_address',
        'user_agent',
        'device_type',
        'platform'
    ];

    /**
     * Get the banner ad that was impressed
     */
    public function bannerAd()
    {
        return $this->belongsTo(BannerAd::class);
    }

    /**
     * Get the user who viewed the ad
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}