<?php

namespace App\Models;

use App\Traits\WithHashId;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class UserRate extends Model
{
    use HasFactory, SoftDeletes, WithHashId;

    protected $guarded = [];

    /**
     * The attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'rate' => 'integer',
        ];
    }

    /**
     * Get the user who rated.
     */
    public function rater()
    {
        return $this->belongsTo(User::class, 'rater_id');
    }

    /**
     * Get the user who was rated.
     */
    public function ratedUser()
    {
        return $this->belongsTo(User::class, 'rated_user_id');
    }

    /**
     * Scope a query to only include pending rates.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved rates.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include rejected rates.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope a query to search by rater or rated user name.
     */
    public function scopeSearch($query, $search)
    {
        return $query->whereHas('rater', function ($q) use ($search) {
            $q->where('name', 'like', '%' . $search . '%');
        })->orWhereHas('ratedUser', function ($q) use ($search) {
            $q->where('name', 'like', '%' . $search . '%');
        })->orWhere('comment', 'like', '%' . $search . '%');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();
        static::created(function ($model) {
            $model->hashId = $model->hashId;
            $model->save();
        });
    }
}
