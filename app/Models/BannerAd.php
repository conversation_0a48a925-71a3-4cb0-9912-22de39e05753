<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BannerAd extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'image',
        'link',
        'screen',
        'platform',
        'start_date',
        'end_date',
        'is_active',
        'order',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $hidden = [
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean'
    ];

    /**
     * Scope to get active banners
     */
    public function scopeActive($query)
    {
        $now = now();
        return $query->where('is_active', true)
            ->where(function($q) use ($now) {
                $q->whereNull('start_date')
                    ->orWhere('start_date', '<=', $now);
            })
            ->where(function($q) use ($now) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', $now);
            });
    }

    /**
     * Scope to filter by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by platform
     */
    public function scopePlatform($query, $platform)
    {
        return $query->where(function($q) use ($platform) {
            $q->where('platform', $platform)
                ->orWhere('platform', 'both');
        });
    }

    /**
     * Scope to filter by screen
     */
    public function scopeScreen($query, $screen)
    {
        return $query->where('screen', $screen);
    }

    /**
     * Get the user who created the banner
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who updated the banner
     */
    public function updatedBy()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the user who deleted the banner
     */
    public function deletedBy()
    {
        return $this->belongsTo(User::class, 'deleted_by');
    }

    /**
     * Search scope
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('title', 'like', '%' . $search . '%')
            ->orWhere('description', 'like', '%' . $search . '%');
    }

    /**
     * Get banner impressions
     */
    public function impressions()
    {
        return $this->hasMany(BannerAdImpression::class, 'banner_ad_id');
    }

    /**
     * Get banner clicks
     */
    public function clicks()
    {
        return $this->hasMany(BannerAdClick::class, 'banner_ad_id');
    }
} 
