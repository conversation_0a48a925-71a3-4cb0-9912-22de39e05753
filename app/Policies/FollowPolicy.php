<?php

namespace App\Policies;

use App\Models\Follow;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class FollowPolicy
{

    public function before(User $user, string $ability): bool|null
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return null; // Continue to specific method check
    }
    /**
     * Determine whether the user can view any follows.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('follow_read');
    }

    /**
     * Determine whether the user can view the follow.
     */
    public function view(User $user, Follow $follow): bool
    {
        return $user->hasPermissionTo('follow_read');
    }

    /**
     * Determine whether the user can create follows.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('follow_create');
    }

    /**
     * Determine whether the user can toggle follows.
     */
    public function toggle(User $user): bool
    {
        return $user->hasPermissionTo('follow_create');
    }

    /**
     * Determine whether the user can delete the follow.
     */
    public function delete(User $user, Follow $follow): bool
    {
        return $user->hasPermissionTo('follow_delete');
    }
} 