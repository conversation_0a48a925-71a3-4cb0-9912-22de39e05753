<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\Response;

class AuthPolicy
{

    public function before(User $user, string $ability): bool|null
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return null; // Continue to specific method check
    }
    /**
     * Determine whether the user can login.
     */
    public function login(User $user): bool
    {
        return true; // Everyone can attempt to login
    }

    /**
     * Determine whether the user can register.
     */
    public function register(User $user): bool
    {
        return true; // Everyone can attempt to register
    }

    /**
     * Determine whether the user can request a password reset.
     */
    public function forgetPassword(User $user): bool
    {
        return true; // Everyone can request a password reset
    }

    /**
     * Determine whether the user can send email verification.
     */
    public function sendEmailVerification(User $user): bool
    {
        return true; // Everyone can request email verification
    }

    /**
     * Determine whether the user can reset password.
     */
    public function reset(User $user): bool
    {
        return true; // Everyone can reset their password
    }
} 