<?php

namespace App\Policies;

use App\Models\Settings;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SettingsPolicy
{

    public function before(User $user, string $ability): bool|null
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return null; // Continue to specific method check
    }
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('settings_read');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Settings $settings): bool
    {
        return $user->hasPermissionTo('settings_read');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('settings_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Settings $settings): bool
    {
        return $user->hasPermissionTo('settings_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Settings $settings): bool
    {
        return $user->hasPermissionTo('settings_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Settings $settings): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Settings $settings): bool
    {
        return false;
    }
}
