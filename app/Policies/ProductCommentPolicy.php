<?php

namespace App\Policies;

use App\Models\ProductComment;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProductCommentPolicy
{
    public function before(User $user, string $ability): bool|null
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return null; // Continue to specific method check
    }

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // Anyone can view comments
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ProductComment $productComment): bool
    {
        return true; // Anyone can view a specific comment
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // Any authenticated user can create comments
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ProductComment $productComment): bool
    {
        // Users can only update their own comments
        return $user->id === $productComment->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ProductComment $productComment): bool
    {
        // Users can only delete their own comments
        return $user->id === $productComment->user_id;
    }

    /**
     * Determine whether the user can reply to the model.
     */
    public function reply(User $user, ProductComment $productComment): bool
    {
        // Any authenticated user can reply to comments
        return true;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ProductComment $productComment): bool
    {
        return $user->id === $productComment->user_id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ProductComment $productComment): bool
    {
        return $user->id === $productComment->user_id;
    }
}
