<?php

namespace App\Policies;

use App\Models\User;
use App\Models\UserRate;
use App\RolesEnum;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserRatePolicy
{
    use HandlesAuthorization;
  public function before(User $user, string $ability): bool|null
    {
        if ($user->isSuperAdmin()) {
            return true;
        }

        return null; // Continue to specific method check
    }
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('user_rate_read');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserRate $userRate): bool
    {
        return $user->can('user_rate_read');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('user_rate_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserRate $userRate): bool
    {
        return $user->can('user_rate_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserRate $userRate): bool
    {
        return $user->can('user_rate_delete');
    }
}
