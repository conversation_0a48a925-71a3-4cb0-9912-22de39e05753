<?php

namespace App\Livewire;

use App\Models\GlobalNotification;
use Carbon\Carbon;
use Livewire\Component;

class PushNotificationUpdateLivewire extends Component
{

    protected $listeners = ['refreshNotificationComponent'];

    public function refreshNotificationComponent()
    {
        $this->render();
    }

    public function render()
    {

        $notification = GlobalNotification::getNotification();
        $date = isset($notification['date']) ? $notification['date'] : null;
        if ($date != null) {
            $date = Carbon::parse($date);
            if ($date->diffInMinutes(now()) > 30) {
                $notification = [];
            }
        } else {
            $notification = [];
        }
        return view('livewire.push-notification-update-livewire', compact('notification'));
    }
}
