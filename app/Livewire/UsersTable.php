<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\User;
use Carbon\Carbon;

class UsersTable extends DataTable
{
    public $verificationFilter = 'all';
    public $achievementFilter = null;

    protected $listeners = ['verificationUpdated' => '$refresh', 'refreshTable' => '$refresh', 'openVerificationModal' => 'openVerificationModal'];


    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن المستخدم بالاسم أو البريد الالكتروني';

        // Handle achievement filter from URL parameter
        if (request()->has('achievement')) {
            $this->achievementFilter = request()->get('achievement');
        }
        
        $this->columns = [
                ['title' => '#', 'type' => 'number', 'field' => 'id'],
                ['title' => 'الاسم', 'type' => 'string', 'field' => 'name'],
                ['title' => 'البريد الالكتروني', 'type' => 'string', 'field' => 'email'],
                ['title' => 'حالة التوثيق', 'type' => 'verification', 'field' => 'is_verified', 'renderType' => 'verification'],
                ['title' => 'عدد الإنجازات', 'type' => 'achievements_count', 'field' => 'achievements_count', 'renderType' => 'achievements_count'],
                ['title' => 'اخر نشاط', 'type' => 'date', 'field' => 'last_activity','renderType' => 'last_activity'],
                ['title' => 'FCM', 'type' => 'fcm', 'field' => 'fcm_token','renderType' => 'fcm'],
                ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions' ,'sortable' => false],
            ];
        
        $this->actions = ['delete', 'view', 'edit', 'verify', 'unverify', 'view_verification', 'add_achievement'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }
    

    public function render()
    {
        $users = User::with(['verifications' => function($query) {
                $query->latest();
            }])
            ->withCount('achievements')
            ->search($this->search)
            ->when($this->verificationFilter !== 'all', function ($query) {
                if ($this->verificationFilter === 'verified') {
                    return $query->where('is_verified', true);
                } elseif ($this->verificationFilter === 'unverified') {
                    return $query->where('is_verified', false);
                } elseif ($this->verificationFilter === 'pending') {
                    return $query->whereHas('pendingVerification');
                }
            })
            ->when($this->achievementFilter, function ($query) {
                return $query->whereHas('achievements', function ($q) {
                    $q->where('achievement_id', $this->achievementFilter);
                });
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);

            $this->resetPage();

        return view('modules.user.users-table', [
            'data' => $users,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }


    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        if ($column['renderType'] == 'last_activity') {
            return $item->last_activity==null ? 'N/A' :  Carbon::parse($item->last_activity)->diffForHumans() ;
        }

        if ($column['renderType'] == 'verification') {
            if ($item->is_verified) {
                return '<span class="badge bg-success"><i class="ti ti-shield-check me-1"></i>موثق</span>';
            } elseif ($item->hasPendingVerification()) {
                return '<span class="badge bg-warning"><i class="ti ti-clock me-1"></i>قيد المراجعة</span>';
            } else {
                return '<span class="badge bg-secondary"><i class="ti ti-shield-x me-1"></i>غير موثق</span>';
            }
        }

    //     <td>
    //     @if ($user->fcm_token != null && $user->fcm_token != '')
    //         <a href="javascript:void(0)" onclick="openNotificationModal({{ $user }})"
    //             class="btn btn-icon btn-text-secondary">
    //             <i class="ti ti-bell ti-md"></i>
    //         </a>
    //     @else
    //         N/A
    //     @endif
    // </td>
        if ($column['renderType'] == 'fcm') {
            return $item->fcm_token != null && $item->fcm_token != '' ? "<a href='javascript:void(0)' onclick='openNotificationModal(" . json_encode($item) . ")' class='btn btn-icon btn-text-secondary'><i class='ti ti-bell ti-md'></i></a>" : 'N/A';
        }

        if ($column['renderType'] == 'achievements_count') {
            $count = $item->achievements_count ?? 0;
            if ($count > 0) {
                return "<span class='badge bg-primary'><i class='ti ti-trophy me-1'></i>{$count}</span>";
            } else {
                return "<span class='badge bg-secondary'>0</span>";
            }
        }

    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'user';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف المستخدم '. $item->name . ' ؟';
        $item['delete'] = route('users.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'view' => [
                'icon' => 'ti-eye',
                'title' => 'View',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}",
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}/edit",
            ],
            'verify' => [
                'icon' => 'ti-shield-check',
                'title' => 'Verify User',
                'color' => 'success',
                'onclick' => "verifyUser({$item->id})",
                'url' => 'javascript:void(0)',
                'show_condition' => !$item->is_verified,
            ],
            'unverify' => [
                'icon' => 'ti-shield-x',
                'title' => 'Unverify User',
                'color' => 'warning',
                'onclick' => "unverifyUser({$item->id})",
                'url' => 'javascript:void(0)',
                'show_condition' => $item->is_verified,
            ],
            'view_verification' => [
                'icon' => 'ti-file-text',
                'title' => 'View Verification Details',
                'color' => 'info',
                'onclick' => "openVerificationModal({$item->id})",
                'url' => 'javascript:void(0)',
                'show_condition' => $item->verifications->isNotEmpty(),
            ],
            'add_achievement' => [
                'icon' => 'ti-trophy',
                'title' => 'Add Achievement',
                'color' => 'warning',
                'onclick' => "openAddAchievementModal({$item->id})",
                'url' => 'javascript:void(0)',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];

            // Check show condition if it exists
            if (isset($action['show_condition']) && !$action['show_condition']) {
                return '';
            }

            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';

            // Set default URL if not provided
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';

            // Determine button class based on action type
            $buttonClass = 'btn btn-icon waves-effect waves-light rounded-pill me-1';
            if($actionType == 'delete') {
                $buttonClass .= ' btn-text-secondary delete-record';
            } elseif(in_array($actionType, ['verify', 'unverify', 'view_verification'])) {
                $buttonClass .= ' btn-' . $action['color'];
            } else {
                $buttonClass .= ' btn-text-secondary';
            }

            return "<a href='{$action['url']}' class='{$buttonClass}' {$tooltip} {$target} {$onclick} title='{$action['title']}'><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }

    public function openVerificationModal($userId)
    {
        $this->dispatch('openVerificationModal', userId: $userId);
    }

}
