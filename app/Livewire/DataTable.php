<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Cache;

class DataTable extends Component
{
    use WithPagination; 

    public $search = '';
    public $sortField = 'id'; // Default sort field
    public $sortDirection = 'asc';
    public $filters = [];
    public $category = null;
    public $perPage = 10; // Items per page
    public $columns = [];
    public $searchPlaceholder = 'ابحث...';
    public $actions = [];
    public $visibleColumns = [];

    // List of listeners for events from parent components (optional)
    protected $listeners = ['refreshTable' => '$refresh'];

    public function mount()
    {
        $this->loadVisibleColumns();
    }

    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortDirection = 'asc';
        }

        $this->sortField = $field;
    }


    // New methods for column visibility and caching
    public function loadVisibleColumns()
    {
        $this->visibleColumns = Cache::get($this->getCacheKey(), array_column($this->columns, 'field'));
    }

    public function saveVisibleColumns()
    {
        Cache::put($this->getCacheKey(), $this->visibleColumns, now()->addDays(30));
    }

    public function toggleColumnVisibility($field)
    {
        if (($key = array_search($field, $this->visibleColumns)) !== false) {
            unset($this->visibleColumns[$key]);
        } else {
            $this->visibleColumns[] = $field;
        }
        $this->saveVisibleColumns();
    }

    protected function getCacheKey()
    {
        return 'datatable_visible_columns_' . static::class;
    }

    protected function getViewData()
    {
        return [
            'data' => [],
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'searchPlaceholder' => $this->searchPlaceholder,
            'actions' => $this->actions,
            'visibleColumns' => $this->visibleColumns,
        ];
    }

    public function renderView($view, $data = [])
    {
        return view($view, array_merge($this->getViewData(), $data));
    }
}