<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Category;
use App\Models\Location;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Products extends Component
{
    public $perPage = 10;
    public $page = 1;
    public $hasMorePages = false;
    public $products = [];
    public $viewMode = 'list'; // grid or list
    public $pageType = 'home'; // home, category, search, seller, profile
    public $query = null; // category_id, search_query, seller_id, etc.
    public $filters = [];
    public $showCategories = true; // Whether to show category filters
    public $showFilters = true; // Whether to show filter options

    // New properties for reactive filtering
    public $categories = [];
    public $locations = [];
    public $selectedCategoryId = null;
    public $selectedLocationId = null;
    public $userLatitude = null;
    public $userLongitude = null;
    public $isNearbyActive = false;
    public $subcategories = [];
    public $subsubcategories = [];
    public $categoryHierarchy = []; // To track the hierarchy of selected categories

    protected $listeners = ['loadMore', 'refreshProducts', 'filterByCategory', 'filterByLocation', 'getNearbyProducts'];

    public function mount($pageType = 'home', $query = null, $filters = [], $showCategories = true, $showFilters = true)
    {
        $this->pageType = $pageType;
        $this->query = $query;
        $this->filters = $filters;
        $this->showCategories = $showCategories;
        $this->showFilters = $showFilters;

        // Load parent categories and locations
        $this->loadParentCategories();
        $this->loadParentLocations();

        $this->loadProducts();
    }

    /**
     * Load parent categories only that have products
     */
    public function loadParentCategories()
    {
        // Get parent categories that have products (either directly or through children)
        $categoriesWithProducts = Category::whereNull('parent_id')
            ->where(function($query) {
                $query->whereHas('products', function($q) {
                    $q->where('status', 'approved');
                })
                ->orWhereHas('children.products', function($q) {
                    $q->where('status', 'approved');
                })
                ->orWhereHas('children.children.products', function($q) {
                    $q->where('status', 'approved');
                });
            })
            ->orderBy('order')
            ->get();

        $this->categories = $categoriesWithProducts;
    }

    /**
     * Load parent locations only that have products
     */
    public function loadParentLocations()
    {
        $this->locations = Location::whereNull('parent_id')
            ->whereHas('products', function($query) {
                $query->where('status', 'approved');
            })
            ->orWhereHas('children.products', function($query) {
                $query->where('status', 'approved');
            })
            ->orderBy('name')
            ->get();
    }

    public function loadProducts()
    {
        // Base query with common relations and conditions
        $productsQuery = Product::with(['location', 'user', 'media'])
            ->where('status', 'approved');

        // Apply filters based on page type
        switch ($this->pageType) {
            case 'category':
                // Get the category and its descendants
                $category = Category::find($this->query);
                if ($category) {
                    $categoryIds = [$category->id];
                    // Add child category IDs if any
                    $childCategories = $category->children;
                    if ($childCategories->count() > 0) {
                        $categoryIds = array_merge($categoryIds, $childCategories->pluck('id')->toArray());
                    }
                    $productsQuery->whereIn('category_id', $categoryIds);
                }
                break;

            case 'search':
                if ($this->query) {
                    $productsQuery->where('title', 'like', '%' . $this->query . '%');
                }
                break;

            case 'seller':
                // Filter products by seller ID
                if ($this->query) {
                    $productsQuery->where('user_id', $this->query);
                }
                break;

            case 'profile':
                // For profile page, show current user's products
                if (Auth::check()) {
                    $productsQuery->where('user_id', Auth::id());
                }
                break;
        }

        // Apply additional filters if provided
        if (!empty($this->filters)) {
            if (isset($this->filters['price_min']) && $this->filters['price_min'] > 0) {
                $productsQuery->where('price', '>=', $this->filters['price_min']);
            }

            if (isset($this->filters['price_max']) && $this->filters['price_max'] > 0) {
                $productsQuery->where('price', '<=', $this->filters['price_max']);
            }

            if (isset($this->filters['condition']) && !empty($this->filters['condition'])) {
                $productsQuery->where('condition', $this->filters['condition']);
            }

            // Handle location filtering
            if (isset($this->filters['location_id']) && !empty($this->filters['location_id'])) {
                $locationId = $this->filters['location_id'];
                $location = Location::find($locationId);

                if ($location) {
                    $locationIds = [$location->id];
                    // Add child location IDs if any
                    $childLocations = $location->children;
                    if ($childLocations->count() > 0) {
                        $locationIds = array_merge($locationIds, $childLocations->pluck('id')->toArray());
                    }
                    $productsQuery->whereIn('location_id', $locationIds);
                }
            }

            // Handle nearby products filtering
            if (isset($this->filters['latitude']) && isset($this->filters['longitude'])) {
                $latitude = $this->filters['latitude'];
                $longitude = $this->filters['longitude'];

                // Join with locations table to get coordinates
                $productsQuery->join('locations', 'products.location_id', '=', 'locations.id')
                    ->select('products.*')
                    ->orderByRaw('ABS(locations.latitude - ?) + ABS(locations.longitude - ?)', [$latitude, $longitude]);
            }

            // Apply category filter for any page type
            if (isset($this->filters['category_id']) && !empty($this->filters['category_id'])) {
                $categoryId = $this->filters['category_id'];
                $category = Category::find($categoryId);
                if ($category) {
                    $categoryIds = [$category->id];
                    // Add child category IDs if any
                    $childCategories = $category->children;
                    if ($childCategories->count() > 0) {
                        $categoryIds = array_merge($categoryIds, $childCategories->pluck('id')->toArray());
                    }
                    $productsQuery->whereIn('category_id', $categoryIds);
                }
            }

            // Apply subcategories filter for category page
            if ($this->pageType == 'category' && isset($this->filters['subcategories']) && !empty($this->filters['subcategories'])) {
                $productsQuery->whereIn('category_id', $this->filters['subcategories']);
            }

            // Apply sorting
            if (isset($this->filters['sort'])) {
                switch ($this->filters['sort']) {
                    case 'price_low':
                        $productsQuery->orderBy('price', 'asc');
                        break;
                    case 'price_high':
                        $productsQuery->orderBy('price', 'desc');
                        break;
                    case 'popular':
                        $productsQuery->orderBy('views_count', 'desc');
                        break;
                    default:
                        $productsQuery->orderBy('created_at', 'desc');
                }
            } else {
                // If we're not sorting by nearby, use default sorting
                if (!isset($this->filters['latitude']) && !isset($this->filters['longitude'])) {
                    $productsQuery->orderBy('created_at', 'desc');
                }
            }
        } else {
            // Default sorting by newest
            $productsQuery->orderBy('created_at', 'desc');
        }

        // Get products with pagination
        $products = $productsQuery->skip(($this->page - 1) * $this->perPage)
            ->take($this->perPage + 1) // Take one more to check if there are more pages
            ->get();

        $this->hasMorePages = $products->count() > $this->perPage;

        if ($this->hasMorePages) {
            $products = $products->take($this->perPage);
        }

        // If it's the first page, replace the products array
        if ($this->page === 1) {
            $this->products = $products->toArray();
        } else {
            // Otherwise, append to the existing array
            $this->products = array_merge($this->products, $products->toArray());
        }
    }

    public function loadMore()
    {
        $this->page++;
        $this->loadProducts();
    }

    public function toggleViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    public function refreshProducts()
    {
        $this->page = 1;
        $this->loadProducts();
    }

    /**
     * Filter products by category
     */
    public function filterByCategory($categoryId, $level = 0)
    {
        $this->selectedCategoryId = $categoryId;
        $this->page = 1;

        // Clear all subcategories if no category is selected
        if (!$categoryId) {
            $this->subcategories = [];
            $this->subsubcategories = [];
            $this->categoryHierarchy = [];
            unset($this->filters['category_id']);
        } else {
            $this->filters['category_id'] = $categoryId;

            // Load the selected category
            $category = Category::find($categoryId);

            if ($category) {
                // Update the category hierarchy based on the level
                if ($level === 0) { // Main category
                    $this->categoryHierarchy = [$category];
                    $this->subsubcategories = []; // Clear third level
                } elseif ($level === 1) { // Subcategory
                    // Keep the parent, add this as second level
                    if (count($this->categoryHierarchy) > 0) {
                        $this->categoryHierarchy = [$this->categoryHierarchy[0], $category];
                    } else {
                        // If somehow we don't have a parent, just add this
                        $this->categoryHierarchy = [$category];
                    }
                } elseif ($level === 2) { // Sub-subcategory
                    // Keep parent and subcategory, add this as third level
                    if (count($this->categoryHierarchy) > 1) {
                        $this->categoryHierarchy = [$this->categoryHierarchy[0], $this->categoryHierarchy[1], $category];
                    } elseif (count($this->categoryHierarchy) > 0) {
                        // If we only have one level, add this as second level
                        $this->categoryHierarchy = [$this->categoryHierarchy[0], $category];
                    } else {
                        // If somehow we don't have any parents, just add this
                        $this->categoryHierarchy = [$category];
                    }
                }

                // Load subcategories based on the selected level
                if ($level === 0 || $level === 1) {
                    // For main category or subcategory, load its children that have products
                    if ($category->children->count() > 0) {
                        if ($level === 0) {
                            // Get subcategories that have products
                            $this->subcategories = $category->children()
                                ->whereHas('products', function($query) {
                                    $query->where('status', 'approved');
                                })
                                ->orWhereHas('children.products', function($query) {
                                    $query->where('status', 'approved');
                                })
                                ->orderBy('title')
                                ->get();
                            $this->subsubcategories = []; // Clear third level when selecting a new parent
                        } else {
                            // Get sub-subcategories that have products
                            $this->subsubcategories = $category->children()
                                ->whereHas('products', function($query) {
                                    $query->where('status', 'approved');
                                })
                                ->orderBy('title')
                                ->get();
                        }
                    } else {
                        // If no children, clear the next level
                        if ($level === 0) {
                            $this->subcategories = [];
                        } else {
                            $this->subsubcategories = [];
                        }
                    }
                } else {
                    // For sub-subcategory, we don't need to load further children
                    // But we keep the existing subcategories
                }
            }
        }

        $this->loadProducts();
    }

    /**
     * Filter products by location
     */
    public function filterByLocation($locationId)
    {
        $this->selectedLocationId = $locationId;
        $this->page = 1;

        if ($locationId) {
            $this->filters['location_id'] = $locationId;

            // If we want to load child locations that have products
            // This would be used if we implement a hierarchical location selector
            // similar to the category selector
            /*
            $location = Location::find($locationId);
            if ($location) {
                $childLocations = $location->children()
                    ->whereHas('products', function($query) {
                        $query->where('status', 'approved');
                    })
                    ->orderBy('name')
                    ->get();
                // Store child locations if needed in the UI
            }
            */
        } else {
            unset($this->filters['location_id']);
        }

        $this->loadProducts();
    }

    /**
     * Get nearby products based on user location
     */
    public function getNearbyProducts($latitude = null, $longitude = null)
    {
        $this->page = 1;

        // Toggle nearby mode if no coordinates provided
        if ($latitude === null && $longitude === null) {
            $this->isNearbyActive = !$this->isNearbyActive;

            // If turning off nearby mode, remove coordinates from filters
            if (!$this->isNearbyActive) {
                $this->userLatitude = null;
                $this->userLongitude = null;
                unset($this->filters['latitude']);
                unset($this->filters['longitude']);
            }
            // If turning on nearby mode but we already have coordinates, use them
            elseif ($this->userLatitude && $this->userLongitude) {
                $this->filters['latitude'] = $this->userLatitude;
                $this->filters['longitude'] = $this->userLongitude;
            }
            // Otherwise, we need coordinates but don't have them yet
            else {
                // We'll handle this in the frontend by showing a message
                $this->isNearbyActive = false;
                return;
            }
        }
        // New coordinates provided
        else {
            $this->userLatitude = $latitude;
            $this->userLongitude = $longitude;
            $this->isNearbyActive = true;

            // Store coordinates in filters
            $this->filters['latitude'] = $latitude;
            $this->filters['longitude'] = $longitude;
        }

        $this->loadProducts();
    }

    public function render()
    {
        return view('livewire.products');
    }
}
