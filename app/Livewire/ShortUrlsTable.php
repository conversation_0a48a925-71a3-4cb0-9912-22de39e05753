<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\ShortUrl;

class ShortUrlsTable extends DataTable
{
    public $columns = [
        ['title' => '#', 'type' => 'number', 'field' => 'id'],
        ['title' => 'الرابط المختصر', 'type' => 'string', 'field' => 'short_url'],
        ['title' => 'الرابط الأصلي', 'type' => 'string', 'field' => 'original_url'],
        ['title' => 'تاريخ الإنشاء', 'type' => 'date', 'field' => 'created_at'],
        ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
    ];

    public function render()
    {
        $shortUrls = ShortUrl::search($this->search)
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);
            $this->resetPage();

        return view('modules.short-url.short-urls-table', compact('shortUrls'));
    }
}
