<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\Location;

class LocationsTable extends DataTable
{

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن الموقع بالاسم أو المدينة';

        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'الاسم', 'type' => 'string', 'field' => 'name'],
            ['title' => 'المدينة', 'type' => 'string', 'field' => 'city'],
            ['title' => 'الدولة', 'type' => 'string', 'field' => 'country'],
            ['title' => 'خط العرض', 'type' => 'number', 'field' => 'latitude'],
            ['title' => 'خط الطول', 'type' => 'number', 'field' => 'longitude'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false]
        ];

        $this->actions = ['delete', 'edit'];
        if ($this->visibleColumns == null) {
            $this->visibleColumns = array_column($this->columns, 'field');
        }
    }

    public function render()
    {
        $locations = Location::search($this->search)
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);

        $this->resetPage();

        return view('components.table.common-table', [
            'data' => $locations,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        return $item->{$column['field']};
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'location';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف الموقع '. $item->name . ' ؟';
        $item['delete'] = route('locations.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'view' => [
                'icon' => 'ti-eye',
                'title' => 'View',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}",
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/{$model}s/{$item->id}/edit",
            ],
            
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            // Undefined array key "url"
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';
             
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}