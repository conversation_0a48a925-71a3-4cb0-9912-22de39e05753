<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\UserVerification;
use App\Models\Settings;
use Illuminate\Support\Facades\Auth;

class VerificationModal extends Component
{
    public $verificationId;
    public $verification;
    public $requirements;
    public $adminNotes = '';
    public $showModal = false;

    protected $listeners = ['openVerificationModal'];

    public function openVerificationModal($verificationId)
    {
        $this->verificationId = $verificationId;
        $this->loadVerification();
        $this->showModal = true;
    }

    public function loadVerification()
    {
        $this->verification = UserVerification::with(['user', 'reviewer'])->find($this->verificationId);
        $requirementsSettings = Settings::where('key', 'verification_requirements')->first();
        $this->requirements = $requirementsSettings ? json_decode($requirementsSettings->value, true) : [];
    }

    public function approve()
    {
        if (!$this->verification) return;

        $this->verification->update([
            'status' => 'approved',
            'admin_notes' => $this->adminNotes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        // Mark user as verified
        $this->verification->user->markAsVerified(Auth::id(), $this->adminNotes);

        $this->closeModal();
        $this->dispatch('verificationUpdated');
        $this->dispatch('refreshTable');
        session()->flash('success', 'تم قبول طلب التوثيق بنجاح');
    }

    public function reject()
    {
        if (!$this->verification) return;

        if (empty($this->adminNotes)) {
            $this->addError('adminNotes', 'يجب إدخال سبب الرفض');
            return;
        }

        $this->verification->update([
            'status' => 'rejected',
            'admin_notes' => $this->adminNotes,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        $this->closeModal();
        $this->dispatch('verificationUpdated');
        $this->dispatch('refreshTable');
        session()->flash('success', 'تم رفض طلب التوثيق');
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['verificationId', 'verification', 'adminNotes']);
    }

    public function getFileUrl($filename)
    {
        return asset('storage/' . $filename);
    }

    public function render()
    {
        return view('livewire.verification-modal');
    }
}
