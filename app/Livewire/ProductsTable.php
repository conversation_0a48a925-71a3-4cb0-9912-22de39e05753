<?php

namespace App\Livewire;

use App\Models\Product;
use App\Models\Category;
use App\Enums\StatusEnum;
use Carbon\Carbon;

class ProductsTable extends DataTable
{
    // Properties for editing
    public $editingProductId = null;
    public $selectedCategory = null;
    public $selectedSubcategory = null;
    public $selectedSubSubcategory = null;
    public $selectedStatus = null;
    public $parentCategories = [];
    public $filteredParentCategories = [];
    public $subcategories = [];
    public $subSubcategories = [];
    public $statuses = [];
    public $categorySearchTerm = '';
    public $selectedCategoryPath = '';

    protected $listeners = [
        'refreshTable' => '$refresh',
        'categoryUpdated' => '$refresh',
        'statusUpdated' => '$refresh'
    ];

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن المنتج بالعنوان أو الوصف';

        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'العنوان', 'type' => 'string', 'field' => 'title'],
            ['title' => 'الصورة', 'type' => 'media', 'field' => 'media', 'renderType' => 'media'],
            ['title' => 'السعر', 'type' => 'price', 'field' => 'price'],
            ['title' => 'حالة المنتج', 'type' => 'status', 'field' => 'status', 'renderType' => 'status'],
            ['title' => 'حالة السلعة', 'type' => 'condition', 'field' => 'condition', 'renderType' => 'condition'],
            ['title' => 'التصنيف', 'type' => 'category', 'field' => 'category_id', 'renderType' => 'category'],
            ['title' => 'الموقع', 'type' => 'location', 'field' => 'location_id', 'renderType' => 'location'],
            ['title' => 'تاريخ الإضافة', 'type' => 'date', 'field' => 'created_at', 'renderType' => 'date'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];

        $this->actions = ['delete', 'view', 'edit'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');

        // Load parent categories for the dropdown
        $this->loadParentCategories();

        // Load statuses for the dropdown
        $this->statuses = StatusEnum::cases();
    }

    public function render()
    {
        $request = request();

        $query = Product::query()
            ->with(['user', 'category', 'location', 'media'])
            ->when($this->search, function ($query) {
                $query->where(function ($query) {
                    $query->where('title', 'like', '%' . $this->search . '%')
                        ->orWhere('description', 'like', '%' . $this->search . '%');
                });
            })->when($request->has('category_id'), function ($query) use ($request) {
                $categoryId = $request->has('category_id') ? $request->get('category_id') : null;
                $query->where('category_id', $categoryId);
            })
            ->when($this->category, function ($query) {
                $query->where('category_id', $this->category);
            })
            ->orderBy($this->sortField, $this->sortDirection);

        return view('components.table.products-table', [
            'data' => $query->paginate($this->perPage),
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
            'parentCategories' => $this->parentCategories,
            'subcategories' => $this->subcategories,
            'statuses' => $this->statuses,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        if ($column['renderType'] == 'date') {
            return Carbon::parse($item->{$column['field']})->format('Y-m-d H:i');
        }

        if ($column['renderType'] == 'media') {
            $primaryMedia = $item->primaryMediaUrl();
            if (!$primaryMedia) {
                return '<span class="text-muted">لا توجد صورة</span>';
            }

            $productUrl = route('products.show', $item->id);
            return "<a href='{$productUrl}' class='media-thumbnail'>
                    <img src='{$primaryMedia}' alt='{$item->title}' class='img-thumbnail' style='max-width: 50px; max-height: 50px;'>
                  </a>";
        }

        if ($column['renderType'] == 'status') {
            $statusClasses = [
                'active' => 'bg-success',
                'pending' => 'bg-warning',
                'sold' => 'bg-info',
                'rejected' => 'bg-danger',
            ];
            $statusText = [
                'active' => 'نشط',
                'pending' => 'قيد المراجعة',
                'sold' => 'تم البيع',
                'rejected' => 'مرفوض',
            ];
            $status = $item->{$column['field']}->name;
            $class = $statusClasses[$status] ?? 'bg-secondary';
            $text = $statusText[$status] ?? $status;
            return "<span class='badge {$class}'>{$text}</span>";
        }

        if ($column['renderType'] == 'condition') {
            $conditionClasses = [
                'new' => 'bg-success',
                'used' => 'bg-warning',
                'refurbished' => 'bg-info',
            ];
            $conditionText = [
                'new' => 'جديد',
                'used' => 'مستعمل',
                'refurbished' => 'مجدد',
            ];
            $condition = $item->{$column['field']}->name;
            $class = $conditionClasses[$condition] ?? 'bg-secondary';
            $text = $conditionText[$condition] ?? $condition;
            return "<span class='badge {$class}'>{$text}</span>";
        }

        if ($column['renderType'] == 'category') {
            return $item->category ? $item->category->title : 'N/A';
        }

        if ($column['renderType'] == 'location') {
            return $item->location ? $item->location->name : 'N/A';
        }

        if ($column['renderType'] == 'price') {
            return number_format($item->{$column['field']}, 2) . ' ريال';
        }

        return $item->{$column['field']};
    }

    /**
     * Load parent categories for the dropdown
     */
    public function loadParentCategories()
    {
        $this->parentCategories = Category::with('children')
            ->parents()
            ->orderBy('title')
            ->get();

        $this->filterParentCategories();
    }

    /**
     * Filter parent categories based on search term
     */
    public function filterParentCategories()
    {
        if (empty($this->categorySearchTerm)) {
            $this->filteredParentCategories = $this->parentCategories;
        } else {
            $searchTerm = strtolower($this->categorySearchTerm);
            $this->filteredParentCategories = $this->parentCategories->filter(function ($category) use ($searchTerm) {
                return str_contains(strtolower($category->title), $searchTerm);
            })->values();
        }
    }

    /**
     * Watch for changes in the category search term
     */
    public function updatedCategorySearchTerm()
    {
        $this->filterParentCategories();
    }

    /**
     * Load subcategories based on selected parent category
     */
    public function loadSubcategories()
    {
        if ($this->selectedCategory) {
            $this->subcategories = Category::with('children')
                ->where('parent_id', $this->selectedCategory)
                ->orderBy('title')
                ->get();
        } else {
            $this->subcategories = [];
        }

        $this->updateSelectedCategoryPath();
    }

    /**
     * Load sub-subcategories based on selected subcategory
     */
    public function loadSubSubcategories()
    {
        if ($this->selectedSubcategory) {
            $this->subSubcategories = Category::where('parent_id', $this->selectedSubcategory)
                ->orderBy('title')
                ->get();
        } else {
            $this->subSubcategories = [];
        }

        $this->updateSelectedCategoryPath();
    }

    /**
     * Select a parent category
     */
    public function selectCategory($categoryId)
    {
        $this->selectedCategory = $categoryId;
        $this->selectedSubcategory = null;
        $this->selectedSubSubcategory = null;
        $this->loadSubcategories();
        $this->subSubcategories = [];
    }

    /**
     * Select a subcategory
     */
    public function selectSubcategory($subcategoryId)
    {
        $this->selectedSubcategory = $subcategoryId;
        $this->selectedSubSubcategory = null;
        $this->loadSubSubcategories();

        // If there are no sub-subcategories, update the path to show the selected subcategory
        if (count($this->subSubcategories) === 0) {
            $this->updateSelectedCategoryPath();
        }
    }

    /**
     * Select a sub-subcategory
     */
    public function selectSubSubcategory($subSubcategoryId)
    {
        $this->selectedSubSubcategory = $subSubcategoryId;
        $this->updateSelectedCategoryPath();
    }

    /**
     * Update the selected category path display
     */
    public function updateSelectedCategoryPath()
    {
        $path = [];

        if ($this->selectedCategory) {
            $parentCategory = Category::find($this->selectedCategory);
            if ($parentCategory) {
                $path[] = $parentCategory->title;
            }

            if ($this->selectedSubcategory) {
                $subcategory = Category::find($this->selectedSubcategory);
                if ($subcategory) {
                    $path[] = $subcategory->title;
                }

                if ($this->selectedSubSubcategory) {
                    $subSubcategory = Category::find($this->selectedSubSubcategory);
                    if ($subSubcategory) {
                        $path[] = $subSubcategory->title;
                    }
                }
            }
        }

        $this->selectedCategoryPath = !empty($path) ? implode(' » ', $path) : 'لم يتم اختيار تصنيف';
    }

    /**
     * Open the edit category modal for a product
     */
    public function openEditCategoryModal($productId)
    {
        $this->reset(['selectedCategory', 'selectedSubcategory', 'selectedSubSubcategory', 'categorySearchTerm']);

        $this->editingProductId = $productId;
        $product = Product::findOrFail($productId);

        // Get the product's current category
        $category = $product->category;

        if ($category) {
            // Check if it's a sub-subcategory (has a parent that has a parent)
            if ($category->parent_id && $category->parent && $category->parent->parent_id) {
                $this->selectedSubSubcategory = $category->id;
                $this->selectedSubcategory = $category->parent_id;
                $this->selectedCategory = $category->parent->parent_id;
                $this->loadSubcategories();
                $this->loadSubSubcategories();
            }
            // If it's a subcategory (has a parent)
            else if ($category->parent_id) {
                $this->selectedSubcategory = $category->id;
                $this->selectedCategory = $category->parent_id;
                $this->loadSubcategories();
                $this->loadSubSubcategories();
            }
            // It's a parent category
            else {
                $this->selectedCategory = $category->id;
                $this->loadSubcategories();
            }
        }

        $this->loadParentCategories();
        $this->updateSelectedCategoryPath();
        $this->dispatch('openEditCategoryModal');
    }

    /**
     * Update the category for a product
     */
    public function updateCategory()
    {
        $product = Product::findOrFail($this->editingProductId);

        // Use the deepest selected category level
        $categoryId = $this->selectedSubSubcategory ?: $this->selectedSubcategory ?: $this->selectedCategory;

        if ($categoryId) {
            $product->category_id = $categoryId;
            $product->save();

            $this->dispatch('closeEditCategoryModal');
            session()->flash('success', 'تم تحديث التصنيف بنجاح');
        } else {
            session()->flash('error', 'يرجى اختيار تصنيف');
        }
    }

    /**
     * Open the edit status modal for a product
     */
    public function openEditStatusModal($productId)
    {
        $this->editingProductId = $productId;
        $product = Product::findOrFail($productId);
        $this->selectedStatus = $product->status->value;

        $this->dispatch('openEditStatusModal');
    }

    /**
     * Update the status for a product
     */
    public function updateStatus()
    {
        $product = Product::findOrFail($this->editingProductId);

        if ($this->selectedStatus) {
            $product->status = $this->selectedStatus;
            $product->save();

            $this->dispatch('closeEditStatusModal');
            session()->flash('success', 'تم تحديث حالة المنتج بنجاح');
        } else {
            session()->flash('error', 'يرجى اختيار حالة');
        }
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $item['deleteMessage'] = 'هل أنت متأكد من حذف المنتج '. $item->title . ' ؟';
        $item['delete'] = route('products.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'view' => [
                'icon' => 'ti-eye',
                'title' => 'View',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/products/{$item->id}",
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => "$baseUrl/admin/products/{$item->id}/edit",
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            // Undefined array key "url"
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';

            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}