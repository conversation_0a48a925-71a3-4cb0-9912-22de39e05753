<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\UserRate;
use Carbon\Carbon;

class UserRatesTable extends DataTable
{
    public $statusFilter = 'all';

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن التقييم بالاسم أو التعليق';
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'المقيم', 'type' => 'string', 'field' => 'rater', 'renderType' => 'rater'],
            ['title' => 'المستخدم المقيم', 'type' => 'string', 'field' => 'rated_user', 'renderType' => 'rated_user'],
            ['title' => 'التقييم', 'type' => 'number', 'field' => 'rate', 'renderType' => 'rate'],
            ['title' => 'التعليق', 'type' => 'string', 'field' => 'comment', 'renderType' => 'comment'],
            ['title' => 'الحالة', 'type' => 'string', 'field' => 'status', 'renderType' => 'status'],
            ['title' => 'تاريخ الإنشاء', 'type' => 'date', 'field' => 'created_at', 'renderType' => 'created_at'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['delete', 'approve', 'reject'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }

    public function render()
    {
        $query = UserRate::with(['rater', 'ratedUser'])
            ->when($this->statusFilter !== 'all', function ($query) {
                return $query->where('status', $this->statusFilter);
            })
            ->search($this->search)
            ->orderBy($this->sortField, $this->sortDirection);
            
        $userRates = $query->paginate(12);

        $this->resetPage();

        return view('components.table.common-table', [
            'data' => $userRates,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        if ($column['renderType'] == 'rater') {
            return $item->rater ? $item->rater->name : 'N/A';
        }

        if ($column['renderType'] == 'rated_user') {
            return $item->ratedUser ? $item->ratedUser->name : 'N/A';
        }

        if ($column['renderType'] == 'rate') {
            $stars = '';
            for ($i = 1; $i <= 5; $i++) {
                $starClass = $i <= $item->rate ? 'text-warning' : 'text-muted';
                $stars .= "<i class='ti ti-star $starClass'></i>";
            }
            return $stars;
        }

        if ($column['renderType'] == 'comment') {
            return $item->comment ? (strlen($item->comment) > 50 ? substr($item->comment, 0, 50) . '...' : $item->comment) : 'N/A';
        }

        if ($column['renderType'] == 'status') {
            $statusClasses = [
                'pending' => 'bg-label-warning',
                'approved' => 'bg-label-success',
                'rejected' => 'bg-label-danger',
            ];
            $statusLabels = [
                'pending' => 'معلق',
                'approved' => 'مقبول',
                'rejected' => 'مرفوض',
            ];
            $class = $statusClasses[$item->status] ?? 'bg-label-secondary';
            $label = $statusLabels[$item->status] ?? $item->status;
            return "<span class='badge $class'>$label</span>";
        }

        if ($column['renderType'] == 'created_at') {
            return Carbon::parse($item->created_at)->format('Y-m-d H:i');
        }
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'user-rate';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف هذا التقييم؟';
        $item['delete'] = route('user-rates.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'approve' => [
                'icon' => 'ti-check',
                'title' => 'Approve',
                'color' => 'text-success',
                'url' => 'javascript:void(0)',
                'onclick' => "updateRateStatus(" . $item->id . ", 'approved')",
                'disabled' => $item->status === 'approved',
            ],
            'reject' => [
                'icon' => 'ti-x',
                'title' => 'Reject',
                'color' => 'text-danger',
                'url' => 'javascript:void(0)',
                'onclick' => "updateRateStatus(" . $item->id . ", 'rejected')",
                'disabled' => $item->status === 'rejected',
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            
            // Skip rendering if action is disabled
            if (isset($action['disabled']) && $action['disabled']) {
                return '';
            }
            
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            
            if($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';
             
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
    
    public function setStatusFilter($status)
    {
        $this->statusFilter = $status;
    }
}
