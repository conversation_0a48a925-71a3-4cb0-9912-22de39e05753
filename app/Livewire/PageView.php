<?php

namespace App\Livewire;

use App\Helpers\BookContentStyleHelper;
use App\Http\Controllers\BookSearchController;
use Livewire\Component;
use App\Models\Book;
use App\Models\BookContent;
use App\Models\BookSearch;
use App\Models\BookTitle;
use Illuminate\Support\Facades\Cache;

class PageView extends Component
{
    public $book;
    public $currentPage = 1;
    public $currentPart = null;
    public $searchTerm;
    public $searchResults = [];

    public $showSearch = false;
    public $showRoot = false;
    public $showPdf = false;

    public $fontSize = 16;
    public $lineHeight = 1.5;

    public function mount(Book $book)
    {
        $this->book = $book;
        $this->currentPage = $book->firstPage();
        $this->currentPart = $book->firstPart();
        $this->loadOptionsFromCache();
    }

    public function render()
    {
        $this->saveOptionsToCache();
        $pageContent = $this->book->getPageContent($this->currentPage, $this->currentPart);
        $searchContent = BookSearchController::getPageContent($this->currentPage, 1, $this->book->id);
        $nass = $pageContent->nass ?? '';
        $titles = BookTitle::where('book_id', $this->book->id)->get('title');
        $nass = BookContentStyleHelper::applyStyle($pageContent,$titles);
        return view('livewire.page-view', compact('pageContent', 'searchContent','nass'));
    }

    public function nextPage()
    {
        $bookContent = $this->book->nextPageContent($this->currentPage, $this->currentPart);
        if ($bookContent != null) {
            $this->currentPage = $bookContent->page;
            $this->currentPart = $bookContent->part;
        }
    }

    public function previousPage()
    {
        if ($this->currentPage > 1) {
            $bookContent = $this->book->previousPageContent($this->currentPage, $this->currentPart);
            if ($bookContent != null) {
                $this->currentPage = $bookContent->page;
                $this->currentPart = $bookContent->part;
            }
        }
    }

    public function nextPart()
    {
        $bookContent = $this->book->nextPartContent($this->currentPage, $this->currentPart);
        if ($bookContent != null) {
            $this->currentPage = $bookContent->page;
            $this->currentPart = $bookContent->part;
        }
    }

    public function previousPart()
    {
       $bookContent = $this->book->previousPartContent($this->currentPage, $this->currentPart);
        if ($bookContent != null) {
            $this->currentPage = $bookContent->page;
            $this->currentPart = $bookContent->part;
        }
    }

    public function goToPage($page)
    {
        if ($page > 0 && $page <= $this->book->totalPages()) {
            $this->currentPage = $page;
            $this->currentPart = 1;
        }
    }

    public function search()
    {
        $this->searchResults = $this->book->searchContent($this->searchTerm);
    }

    public function toggleShowSearch()
    {
        $this->showSearch = !$this->showSearch;
        $this->saveOptionsToCache();
    }

    public function toggleShowRoot()
    {
        $this->showRoot = !$this->showRoot;
        $this->saveOptionsToCache();
    }

    public function toggleShowPdf()
    {
        $this->showPdf = !$this->showPdf;
        $this->saveOptionsToCache();
    }

    public function increaseFontSize()
    {
        $this->fontSize += 1;
        $this->saveOptionsToCache();
    }

    public function decreaseFontSize()
    {
        if ($this->fontSize > 1) {
            $this->fontSize -= 1;
            $this->saveOptionsToCache();
        }
    }

    public function increaseLineHeight()
    {
        $this->lineHeight += 0.1;
        $this->saveOptionsToCache();
    }

    public function decreaseLineHeight()
    {
        if ($this->lineHeight > 0.1) {
            $this->lineHeight -= 0.1;
            $this->saveOptionsToCache();
        }
    }

    protected function saveOptionsToCache()
    {
        Cache::put('db-fontSize', $this->fontSize);
        Cache::put('db-lineHeight', $this->lineHeight);
        Cache::put('db-showSearch', $this->showSearch);
        Cache::put('db-showRoot', $this->showRoot);
    }

    protected function loadOptionsFromCache()
    {
        $this->fontSize = Cache::get('db-fontSize', 16);
        $this->lineHeight = Cache::get('db-lineHeight', 1.5);
        $this->showSearch = Cache::get('db-showSearch', false);
        $this->showRoot = Cache::get('db-showRoot', false);
    }
}
