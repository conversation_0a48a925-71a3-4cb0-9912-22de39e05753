<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\Category;

class CategoriesTable extends DataTable
{
    public $updateModalView = 'modules.category.modal-update';
    
    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن التصنيف بالاسم';
        
        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'الاسم', 'type' => 'string', 'field' => 'title'],
            ['title' => 'الصورة', 'type' => 'string', 'field' => 'image','renderType' => 'image'],
            ['title' => 'عدد المنتجات', 'type' => 'number', 'field' => 'products_count', 'renderType' => 'products_count'],
            ['title' => 'التصنيفات الفرعية', 'type' => 'string', 'field' => 'children', 'sortable' => false,'renderType' => 'children'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false],
        ];
        
        $this->actions = ['delete', 'view', 'edit', 'attributes'];
        if($this->visibleColumns == null) $this->visibleColumns = array_column($this->columns, 'field');
    }

    public function render()
    {
        $categories = Category::search($this->search)
            ->orderBy($this->sortField, $this->sortDirection)
            ->with('children')
            ->paginate(12);
            $parentCategories = Category::whereNull('parent_id')->get();

            return view('components.table.common-table', [
                'data' => $categories,
                'columns' => $this->columns,
                'sortField' => $this->sortField,
                'sortDirection' => $this->sortDirection,
                'visibleColumns' => $this->visibleColumns,
                'updateModalView' => $this->updateModalView,
                'parentCategories' => $parentCategories,
            ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        switch ($column['renderType']) {
            case 'children':
                return $this->renderChildren($item);
            case 'image':
                if ($item->image == null) {
                    return "<p class='text-danger'>لا توجد صورة</p>";
                }
                return "<img src='" . asset($item->image) . "' alt='{$item->title}' class='img-fluid' style='width: 50px; height: 50px;'>";
            case 'products_count':
                return "<a href='" . route('products.index', ['category_id' => $item->id]) . "' class='btn btn-primary btn-sm'>{$item->products()->count()}</a>";
            case 'date':
                return $item->{$column['field']} ? $item->{$column['field']}->format('Y-m-d') : '';
            default:
                return $item->{$column['field']};
        }
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'category';
        $models = 'categories';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف التصنيف '. $item->title . '؟';
        $item['delete'] = route('categories.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => "openDeleteModal(" . json_encode($item) . ")",
                'url' => 'javascript:void(0)',
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => 'javascript:void(0)',
                'onclick' => "openEditModal(" . json_encode($item) . ")",
            ],
        ];
        if($item->parent_id == null) {
            $actions['attributes'] = [
                'icon' => 'ti-list-details',
                'title' => 'Manage Attributes',
                'color' => 'text-info',
                'url' => route('categories.attributes.index', $item->id),
            ];
        }

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            if(!isset($action['url']) || $action['url'] == null) $action['url'] = 'javascript:void(0)';
             
            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        //* childeren

    }

    protected function renderChildren($category)
    {
        $html = '';
        foreach ($category->children as $child) {
            $html .= '<span class="badge bg-primary mt-1">' . $child->title . '</span> ';
        }
        return $html;
    }
}
