<?php

namespace App\Livewire;

use App\Livewire\DataTable;
use App\Models\Brand;

class BrandsTable extends DataTable
{
    public $updateModalView = 'modules.brand.modal-update';

    public function mount()
    {
        parent::mount();
        $this->searchPlaceholder = 'ابحث عن العلامة التجارية بالاسم أو الوصف';

        $this->columns = [
            ['title' => '#', 'type' => 'number', 'field' => 'id'],
            ['title' => 'الاسم', 'type' => 'string', 'field' => 'name'],
            ['title' => 'الرابط', 'type' => 'string', 'field' => 'slug'],
            ['title' => 'الوصف', 'type' => 'string', 'field' => 'description'],
            ['title' => 'المنتجات', 'type' => 'number', 'field' => 'products_count'],
            ['title' => 'الفئات', 'type' => 'number', 'field' => 'categories_count'],
            ['title' => 'أوامر', 'type' => 'action', 'field' => 'actions', 'sortable' => false]
        ];

        $this->actions = ['delete', 'edit'];
        if ($this->visibleColumns == null) {
            $this->visibleColumns = array_column($this->columns, 'field');
        }
    }

    public function render()
    {
        $brands = Brand::withCount(['product', 'categories'])
            ->search($this->search)
            ->orderBy($this->sortField, $this->sortDirection)
            ->paginate(12);

        $this->resetPage();

        return view('components.table.common-table', [
            'data' => $brands,
            'columns' => $this->columns,
            'sortField' => $this->sortField,
            'sortDirection' => $this->sortDirection,
            'visibleColumns' => $this->visibleColumns,
        ]);
    }

    public function renderColumn($column, $item)
    {
        if (!isset($column['renderType'])) {
            return $item->{$column['field']};
        }

        return $item->{$column['field']};
    }

    public function renderAction($actionType, $item)
    {
        $baseUrl = env('APP_URL');
        $model = 'brand';
        $item['deleteMessage'] = 'هل أنت متأكد من حذف العلامة التجارية ' . $item->name . ' ؟';
        $item['delete'] = route('brands.destroy', $item->id);

        $actions = [
            'delete' => [
                'icon' => 'ti-trash',
                'title' => 'Delete',
                'color' => 'text-secondary',
                'onclick' => 'openDeleteModal(' . json_encode($item) . ')',
                'url' => 'javascript:void(0)',
            ],
            'edit' => [
                'icon' => 'ti-edit',
                'title' => 'Edit',
                'color' => 'text-secondary',
                'url' => 'javascript:void(0)',
                'onclick' => "openEditModal(" . json_encode($item) . ")",
            ],
        ];

        if (isset($actions[$actionType])) {
            $action = $actions[$actionType];
            $tooltip = isset($action['tooltip']) ? "data-bs-toggle='tooltip' data-bs-placement='right' title='{$action['tooltip']}'" : '';
            $target = isset($action['target']) ? "target='{$action['target']}'" : '';
            $onclick = isset($action['onclick']) ? "onclick='{$action['onclick']}'" : '';
            if ($actionType == 'delete') {
                return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill delete-record' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
            }
            if (!isset($action['url']) || $action['url'] == null) {
                $action['url'] = 'javascript:void(0)';
            }

            return "<a href='{$action['url']}' class='btn btn-icon btn-{$action['color']} waves-effect waves-light rounded-pill' {$tooltip} {$target} {$onclick}><i class='ti {$action['icon']} ti-md'></i></a>";
        }

        return '';
    }
}