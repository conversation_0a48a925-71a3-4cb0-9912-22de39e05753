<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Settings;

class VerificationRequirementsForm extends Component
{
    public $requirements = [];

    public function mount()
    {
        $settings = Settings::where('key', 'verification_requirements')->first();
        $this->requirements = $settings ? json_decode($settings->value, true) : [];
        
        // Add empty requirement if none exist
        if (empty($this->requirements)) {
            $this->requirements = [
                [
                    'type' => 'text',
                    'title' => '',
                    'description' => '',
                    'is_required' => true
                ]
            ];
        }
    }

    public function addRequirement()
    {
        $this->requirements[] = [
            'type' => 'text',
            'title' => '',
            'description' => '',
            'is_required' => true
        ];
    }

    public function removeRequirement($index)
    {
        unset($this->requirements[$index]);
        $this->requirements = array_values($this->requirements);
    }

    public function save()
    {
        $this->validate([
            'requirements.*.type' => 'required|string|in:text,textarea,file,select',
            'requirements.*.title' => 'required|string|max:255',
            'requirements.*.description' => 'nullable|string',
            'requirements.*.is_required' => 'boolean',
        ]);

        Settings::updateOrCreate(
            ['key' => 'verification_requirements'],
            [
                'value' => json_encode($this->requirements),
                'type' => 'json',
                'group' => 'verification',
                'description' => 'User verification requirements configuration'
            ]
        );

        session()->flash('success', 'تم حفظ متطلبات التوثيق بنجاح');
    }

    public function render()
    {
        return view('livewire.verification-requirements-form');
    }
}
