<?php
namespace App\Traits;
use Vin<PERSON>\Hashids\Facades\Hashids;

trait WithHashId
{
    public function getHashIdAttribute(): string
    {
        return Hashids::encode($this->id);
    }

    public static function findByHashId(string $hashId)
    {
        return static::find(Hashids::decode($hashId)[0] ?? null);
    }


    public static function getId(string $hashId): int
    {
        return Hashids::decode($hashId)[0] ?? null;
    }

    //* This scope will allow you to query a model using the hashId 
    public function scopeHashId($query, $hashId)
    {
        $id = Hashids::decode($hashId)[0] ?? null;
        return $query->where('id', $id);
    }

    //this will make routes linke user/{id} work for  HashId
    public function getRouteKeyName()
    {
        return 'id';
    }
}