<?php

namespace App\Providers;

use App\Models\AppMenu;
use App\Models\ProductComment;
use App\Policies\AppMenuPolicy;
use App\Policies\ProductCommentPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        AppMenu::class => AppMenuPolicy::class,
        ProductComment::class => ProductCommentPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();
    }
}