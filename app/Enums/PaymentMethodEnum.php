<?php

namespace App\Enums;

enum PaymentMethodEnum: string
{
    case WALLET = 'wallet';
    case BANK_TRANSFER = 'bank_transfer';
    case ONLINE_PAYMENT = 'online_payment';

    static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    static function getLabelsAr(): array
    {
        return [
            self::WALLET->value => 'محفظة إلكترونية',
            self::BANK_TRANSFER->value => 'تحويل بنكي',
            self::ONLINE_PAYMENT->value => 'دفع إلكتروني',
        ];
    }

    static function getKeys(): array
    {
        return array_column(self::cases(), 'name');
    }

    static function getKey(string $value): ?string
    {
        return array_search($value, self::getValues(), true);
    }

    static function getValue(string $key): ?string
    {
        return array_search($key, self::getKeys(), true);
    }

    static function getLabel(string $value): string
    {
        return match ($value) {
            self::WALLET->value => 'محفظة إلكترونية',
            self::BANK_TRANSFER->value => 'تحويل بنكي',
            self::ONLINE_PAYMENT->value => 'دفع إلكتروني',
            default => 'غير معروف',
        };
    }

    static function requiresProof(string $value): bool
    {
        return match ($value) {
            self::WALLET->value => true,
            self::BANK_TRANSFER->value => true,
            self::ONLINE_PAYMENT->value => false,
            default => true,
        };
    }

    static function isAutoVerified(string $value): bool
    {
        return match ($value) {
            self::ONLINE_PAYMENT->value => true,
            default => false,
        };
    }
}
