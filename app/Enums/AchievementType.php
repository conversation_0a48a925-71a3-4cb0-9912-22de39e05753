<?php

namespace App\Enums;

enum AchievementType: string
{
    case ACHIEVEMENT = 'achievement';
    case BADGE = 'badge';
    case REWARD = 'reward';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function getLabelsAr(): array
    {
        return [
            self::ACHIEVEMENT->value => 'إنجاز',
            self::BADGE->value => 'شارة',
            self::REWARD->value => 'مكافأة',
        ];
    }
}
