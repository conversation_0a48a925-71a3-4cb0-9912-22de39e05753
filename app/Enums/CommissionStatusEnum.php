<?php

namespace App\Enums;

enum CommissionStatusEnum: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }

    static function getLabelsAr(): array
    {
        return [
            self::PENDING->value => 'قيد المراجعة',
            self::APPROVED->value => 'مقبول',
            self::REJECTED->value => 'مرفوض',
        ];
    }

    static function getKeys(): array
    {
        return array_column(self::cases(), 'name');
    }

    static function getKey(string $value): ?string
    {
        return array_search($value, self::getValues(), true);
    }

    static function getValue(string $key): ?string
    {
        return array_search($key, self::getKeys(), true);
    }

    static function getLabel(string $value): string
    {
        return match ($value) {
            self::PENDING->value => 'قيد المراجعة',
            self::APPROVED->value => 'مقبول',
            self::REJECTED->value => 'مرفوض',
            default => 'غير معروف',
    };
    }

    static function getBadgeClass(string $value): string
    {
        return match ($value) {
            self::PENDING->value => 'badge-warning',
            self::APPROVED->value => 'badge-success',
            self::REJECTED->value => 'badge-danger',
            default => 'badge-secondary',
        };
    }
}
