<?php

namespace App\Enums;

enum StatusEnum: string
{
    case PENDING = 'pending';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';
    case SOLD = 'sold';
    case DELETED = 'deleted';

    static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
    static function getLabelsAr(): array
    {
        return [
            self::PENDING->value => 'قيد المراجعة',
            self::APPROVED->value => 'مقبول',
            self::REJECTED->value => 'مرفوض',
            self::SOLD->value => 'تم البيع',
            self::DELETED->value => 'محذوف',
        ];
    }
    static function getKeys(): array
    {
        return array_column(self::cases(), 'name');
    }
    static function getKey(string $value): ?string
    {
        return array_search($value, self::getValues(), true);
    }
    static function getValue(string $key): ?string
    {
        return array_search($key, self::getKeys(), true);
    }
    static function getLabel(string $value): string
    {
        return match ($value) {
            self::PENDING->value => 'قيد المراجعة',
            self::APPROVED->value => 'مقبول',
            self::REJECTED->value => 'مرفوض',
            self::SOLD->value => 'تم البيع',
            self::DELETED->value => 'محذوف',
            default => 'غير معروف',
        };
    }
}
