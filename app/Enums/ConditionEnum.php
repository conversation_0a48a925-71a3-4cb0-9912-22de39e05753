<?php

namespace App\Enums;

enum ConditionEnum: string
{
    case NEW = 'new';
    case USED = 'used';
    case REFURBISHED = 'refurbished';

    static function getValues(): array
    {
        return array_column(self::cases(), 'value');
    }
    static function getLablesAr(): array
    {
        return [
            self::NEW->value => 'جديد',
            self::USED->value => 'مستعمل',
            self::REFURBISHED->value => 'مجدد',
        ];
    }

    static function getKeys(): array
    {
        return array_column(self::cases(), 'name');
    }
    static function getKey(string $value): ?string
    {
        return array_search($value, self::getValues(), true);
    }
    static function getValue(string $key): ?string
    {
        return array_search($key, self::getKeys(), true);
    }
    static function getLabel(string $value): string
    {
        return match ($value) {
            self::NEW->value => 'جديد',
            self::USED->value => 'مستعمل',
            self::REFURBISHED->value => 'مجدد',
            default => 'غير معروف',
        };
    }
    static function getLabelByKey(string $key): string
    {
        return match ($key) {
            self::NEW->name => 'جديد',
            self::USED->name => 'مستعمل',
            self::REFURBISHED->name => 'مجدد',
            default => 'غير معروف',
        };
    }
    static function getLabelByValue(string $value): string
    {
        return match ($value) {
            self::NEW->value => 'جديد',
            self::USED->value => 'مستعمل',
            self::REFURBISHED->value => 'مجدد',
            default => 'غير معروف',
        };
    }

    static function getKeyByLabel(string $label): ?string
    {
        return array_search($label, self::getLablesAr(), true);
    }
}
