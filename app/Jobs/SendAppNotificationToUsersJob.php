<?php

namespace App\Jobs;

use App\Models\AppNotification;
use App\Models\User;
use App\Http\Controllers\FirebaseController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class SendAppNotificationToUsersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $notification;
    protected $batchSize = 50; 

    public function __construct(AppNotification $notification)
    {
        $this->notification = $notification;
    }

    public function handle(): void 
    {
        // Process users in chunks to avoid memory issues
        User::chunk($this->batchSize, function ($users) {
            $userNotifications = [];
            $timestamp = now();

            foreach ($users as $user) {
                $userNotifications[] = [
                    'app_notification_id' => $this->notification->id,
                    'user_id' => $user->id,
                ];
            }

            // Bulk insert notifications for this batch
            if (!empty($userNotifications)) {
                DB::table('app_user_notifications')->insert($userNotifications);
            }
        });

    }
}