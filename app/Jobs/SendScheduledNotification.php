<?php

namespace App\Jobs;

use App\Http\Controllers\FirebaseController;
use App\Models\AppNotification;
use App\Models\AppUserNotification;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendScheduledNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userId;
    protected $title;
    protected $body;
    protected $type;
    protected $data;
    protected $notificationType;

    public function __construct(
        $userId,
        $title,
        $body,
        $type = 'info',
        $data = null,
        $notificationType = 'general'
    ) {
        $this->userId = $userId;
        $this->title = $title;
        $this->body = $body;
        $this->type = $type;
        $this->data = $data;
        $this->notificationType = $notificationType;
    }

    public function handle(FirebaseController $firebaseController)
    {
        $user = User::find($this->userId);

        if ($user && $user->fcm_token) {
            $firebaseController->send(
                $this->title,
                $this->body,
                $this->data ? json_decode($this->data, true)['id'] : null,
                $this->notificationType,
                [$user->fcm_token]
            );
        }

        $appNotification = AppNotification::create([
            'title' => $this->title,
            'body' => $this->body,
            'type' => $this->type,
            'data' => $this->data,
        ]);

        AppUserNotification::create([
            'user_id' => $user->id,
            'app_notification_id' => $appNotification->id,
        ]);
    }
}