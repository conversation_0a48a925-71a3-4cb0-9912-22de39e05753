<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\AchievementConditionsService;
use App\Notifications\AchievementAwardedNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * Process Achievement Queue Job
 * 
 * This job is responsible for checking and awarding achievements to users.
 * It can be triggered for:
 * 1. A specific user
 * 2. All users (for periodic checks)
 * 
 * When achievements are awarded, notifications are sent to users.
 */
class ProcessAchievementQueueJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user to check achievements for (null for all users)
     */
    protected ?User $user;

    /**
     * Whether to check all users
     */
    protected bool $checkAllUsers;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 300; // 5 minutes

    /**
     * Create a new job instance for a specific user.
     */
    public function __construct(?User $user = null, bool $checkAllUsers = false)
    {
        $this->user = $user;
        $this->checkAllUsers = $checkAllUsers;
        
        // Set queue name based on scope
        if ($checkAllUsers) {
            $this->onQueue('achievements-bulk');
        } else {
            $this->onQueue('achievements');
        }
    }

    /**
     * Execute the job.
     */
    public function handle(AchievementConditionsService $achievementService): void
    {
        try {
            if ($this->checkAllUsers) {
                $this->processAllUsers($achievementService);
            } elseif ($this->user) {
                $this->processSpecificUser($achievementService);
            } else {
                Log::warning('ProcessAchievementQueueJob called without user or checkAllUsers flag');
            }
        } catch (\Exception $e) {
            Log::error('Error in ProcessAchievementQueueJob', [
                'user_id' => $this->user?->id,
                'check_all_users' => $this->checkAllUsers,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Process achievements for a specific user
     */
    private function processSpecificUser(AchievementConditionsService $achievementService): void
    {
        Log::info('Processing achievements for specific user', ['user_id' => $this->user->id]);

        $awardedAchievements = $achievementService->checkAllAchievementsForUser($this->user);

        if (!empty($awardedAchievements)) {
            $this->sendAchievementNotifications($this->user, $awardedAchievements);
            
            Log::info('Achievements awarded to user', [
                'user_id' => $this->user->id,
                'achievements' => $awardedAchievements,
                'count' => count($awardedAchievements)
            ]);
        }
    }

    /**
     * Process achievements for all users
     */
    private function processAllUsers(AchievementConditionsService $achievementService): void
    {
        Log::info('Processing achievements for all users');

        $results = $achievementService->checkAchievementsForAllUsers();

        Log::info('Bulk achievement processing completed', [
            'processed_users' => $results['processed_users'],
            'total_achievements_awarded' => $results['total_achievements_awarded'],
            'errors_count' => count($results['errors'])
        ]);

        if (!empty($results['errors'])) {
            Log::warning('Errors occurred during bulk achievement processing', [
                'errors' => $results['errors']
            ]);
        }
    }

    /**
     * Send achievement notifications to user
     */
    private function sendAchievementNotifications(User $user, array $achievementSlugs): void
    {
        foreach ($achievementSlugs as $achievementSlug) {
            try {
                // Send notification for each achievement
                $user->notify(new AchievementAwardedNotification($achievementSlug));
                
                Log::info('Achievement notification sent', [
                    'user_id' => $user->id,
                    'achievement_slug' => $achievementSlug
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send achievement notification', [
                    'user_id' => $user->id,
                    'achievement_slug' => $achievementSlug,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessAchievementQueueJob failed', [
            'user_id' => $this->user?->id,
            'check_all_users' => $this->checkAllUsers,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }

    /**
     * Static method to dispatch job for specific user
     */
    public static function dispatchForUser(User $user): void
    {
        static::dispatch($user, false);
    }

    /**
     * Static method to dispatch job for all users
     */
    public static function dispatchForAllUsers(): void
    {
        static::dispatch(null, true);
    }
}
