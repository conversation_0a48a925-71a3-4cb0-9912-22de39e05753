<?php

namespace App\Jobs;

use App\Models\ProductMedia;
use FFMpeg\FFMpeg;
use FFMpeg\Coordinate\TimeCode;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Illuminate\Support\Facades\Storage;

class ProcessProductMedia implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected ProductMedia $media, protected string $originalPath) {}

    public function handle(): void
    {
        if ($this->media->type === 'image') {
            $this->processImage();
        } elseif ($this->media->type === 'video') {
            $this->processVideo();
        }
    }

    protected function processImage(): void
    {
        $manager = new ImageManager(new Driver());
        $image = $manager->read(Storage::disk('products')->get($this->originalPath));

        // Create watermarked original image
        $originalWithWatermark = clone $image;
        $this->addWatermark($originalWithWatermark, 20); // 20px padding for original
        $originalWatermarkedPath = $this->getPathWithSuffix('watermarked');
        Storage::disk('products')->put($originalWatermarkedPath, $originalWithWatermark->toJpeg()->toString());

        // Create thumbnail (150x150)
        $thumbnail = $image->scale(width: 150, height: 150);
        // Add watermark to thumbnail
        $this->addWatermark($thumbnail, 10); // smaller padding for thumbnail
        $thumbnailPath = $this->getPathWithSuffix('thumb');
        Storage::disk('products')->put($thumbnailPath, $thumbnail->toJpeg()->toString());

        // Create small version (800x800 max)
        $small = $image->scale(width: 800, height: 800);
        // Add watermark to small version
        $this->addWatermark($small, 20); // 20px padding as requested
        $smallPath = $this->getPathWithSuffix('small');
        Storage::disk('products')->put($smallPath, $small->toJpeg()->toString());

        // Update media record with new paths
        $this->media->update([
            'thumbnail_path' => $thumbnailPath,
            'small_path' => $smallPath,
            'watermarked_path' => $originalWatermarkedPath,
        ]);
    }

    /**
     * Add watermark to an image
     *
     * @param \Intervention\Image\Interfaces\ImageInterface $image The image to add watermark to
     * @param int $padding Padding from the edges in pixels
     * @return void
     */
    protected function addWatermark($image, int $padding = 20): void
    {
        // Load watermark logo
        $logoPath = public_path('assets/img/logo.png');
        $manager = new ImageManager(new Driver());
        $watermark = $manager->read(file_get_contents($logoPath));

        // Calculate watermark size based on image dimensions
        // Watermark should be proportional to the image size (10% of image width)
        $targetWidth = $image->width() * 0.10;
        $ratio = $targetWidth / $watermark->width();

        // Resize watermark
        $watermark->scale(
            width: intval($watermark->width() * $ratio),
            height: intval($watermark->height() * $ratio)
        );

        // Save resized watermark to a temporary file
        $tempWatermarkPath = sys_get_temp_dir() . '/temp_watermark_' . uniqid() . '.png';
        file_put_contents($tempWatermarkPath, $watermark->toPng()->toString());

        // Apply watermark with 50% opacity to bottom right with padding
        $image->place(
            $tempWatermarkPath,
            'bottom-right',
            $padding,
            $padding,
            50 // 50% opacity (0-100)
        );

        // Clean up temporary file
        @unlink($tempWatermarkPath);
    }

    protected function processVideo(): void
    {
        try {
            $ffmpeg = FFMpeg::create();

            $video = $ffmpeg->open(Storage::disk('products')->path($this->originalPath));

            // Extract frame for thumbnail
            $frame = $video->frame(TimeCode::fromSeconds(1));

            $thumbnailPath = $this->getPathWithSuffix('thumb', 'jpg');

            $frame->save(Storage::disk('products')->path($thumbnailPath));

            // Create smaller version of video
            $smallPath = $this->getPathWithSuffix('small', 'mp4');

            //*check if video is small dimensions
            $currentDimensions = $video->getStreams()->first()->getDimensions();
            // *check if No need to resize
            if ($currentDimensions->getWidth() <= 854 && $currentDimensions->getHeight() <= 480) {
                $smallPath = $this->originalPath;
            } else {
                $video->filters()->resize(new \FFMpeg\Coordinate\Dimension(854, 480))->synchronize();
                $video->save(new \FFMpeg\Format\Video\X264(), Storage::disk('products')->path($smallPath));
            }

            // Update media record
            $this->media->update([
                'thumbnail_path' => $thumbnailPath,
                'small_path' => $smallPath,
                // No watermarked_path for videos
            ]);
        } catch (\FFMpeg\Exception\ExecutableNotFoundException $e) {
            Log::error('FFMpeg executable not found: ' . $e->getMessage());
        }
    }

    protected function getPathWithSuffix(string $suffix, ?string $extension = null): string
    {
        $pathInfo = pathinfo($this->originalPath);
        $extension = $extension ?? $pathInfo['extension'];
        return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_' . $suffix . '.' . $extension;
    }
}
