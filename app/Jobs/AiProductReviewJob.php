<?php

namespace App\Jobs;

use App\Enums\StatusEnum;
use App\Helpers\Helpers;
use App\Models\Product;
use App\Models\ProductMedia;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Notification;
use App\Notifications\ProductApprovedNotification;
use App\Notifications\ProductViolationNotification;

class AiProductReviewJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Product $product)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting AI review for product: ' . $this->product->id);
            
            // Check if product is already approved or rejected
            if ($this->product->status !== StatusEnum::PENDING) {
                Log::info('Product already reviewed. Status: ' . $this->product->status->value);
                return;
            }
            
            $hasViolation = false;
            $violationDetails = [];
            
            // Review product text content
            $textViolation = $this->reviewTextContent();
            if ($textViolation['hasViolation']) {
                $hasViolation = true;
                $violationDetails[] = $textViolation['details'];
            }
            
            // Review product images
            $imageViolation = $this->reviewImages();
            if ($imageViolation['hasViolation']) {
                $hasViolation = true;
                $violationDetails[] = $imageViolation['details'];
            }
            
            // Update product status based on review results
            if ($hasViolation) {
                // Notify admins about violation
                $notificationData = [
                    'title' => 'Product Violation Detected',
                    'body' => 'Product ID: ' . $this->product->id . ' has content violations',
                    'details' => $violationDetails,
                    'product_id' => $this->product->id
                ];
                
                Helpers::notifyAdmins($notificationData);
                
                // Notify user about pending review
                $this->product->user->notify(new ProductViolationNotification($this->product));
                
                Log::info('Product has violations. Notified admins for review.', [
                    'product_id' => $this->product->id,
                    'violations' => $violationDetails
                ]);
            } else {
                // Approve the product
                $this->product->status = StatusEnum::APPROVED;
                $this->product->save();
                
                // Notify user about approval
                $this->product->user->notify(new ProductApprovedNotification($this->product));
                
                Log::info('Product approved automatically by AI review', [
                    'product_id' => $this->product->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Error in AI product review: ' . $e->getMessage(), [
                'product_id' => $this->product->id,
                'exception' => $e
            ]);
            
            // Notify admins about error
            $notificationData = [
                'title' => 'AI Review Error',
                'body' => 'Error reviewing product ID: ' . $this->product->id,
                'details' => $e->getMessage(),
                'product_id' => $this->product->id
            ];
            
            Helpers::notifyAdmins($notificationData);
        }
    }
    
    /**
     * Review product text content using Gemini AI
     * 
     * @return array
     */
    private function reviewTextContent(): array
    {
        $textContent = $this->product->title . "\n" . $this->product->desc;
        
        // Call Gemini API to analyze text
        $response = $this->callGeminiApi([
            'text' => $textContent,
            'type' => 'text'
        ]);
        
        return $this->processGeminiResponse($response, 'text');
    }
    
    /**
     * Review product images using Gemini AI
     * 
     * @return array
     */
    private function reviewImages(): array
    {
        $media = $this->product->media()->where('type', 'image')->get();
        
        if ($media->isEmpty()) {
            return ['hasViolation' => false, 'details' => 'No images to review'];
        }
        
        $violationFound = false;
        $violationDetails = [];
        
        foreach ($media as $image) {
            $imagePath = $image->path;
            $imageUrl = Storage::disk('products')->url($imagePath);
            
            // Call Gemini API to analyze image
            $response = $this->callGeminiApi([
                'image_url' => $imageUrl,
                'type' => 'image'
            ]);
            
            $result = $this->processGeminiResponse($response, 'image');
            
            if ($result['hasViolation']) {
                $violationFound = true;
                $violationDetails[] = "Image {$image->id}: " . $result['details'];
            }
        }
        
        return [
            'hasViolation' => $violationFound,
            'details' => $violationDetails
        ];
    }
    
    /**
     * Call Gemini API for content analysis
     * 
     * @param array $data
     * @return array
     */
    private function callGeminiApi(array $data): array
    {
        $apiKey = config('gemini.api_key');
        
        // If no API key is configured, use mock responses
        if (empty($apiKey)) {
            Log::warning('Gemini API key not configured. Using mock responses.');
            return $this->getMockResponse($data);
        }
        
        try {
            $apiUrl = config('gemini.api_url');
            $model = $data['type'] === 'text' ? config('gemini.text_model') : config('gemini.model');
            
            // Prepare request based on content type
            if ($data['type'] === 'text') {
                $endpoint = $apiUrl . $model . ':generateContent?key=' . $apiKey;
                $payload = [
                    'contents' => [
                        'parts' => [
                            [
                                'text' => "Review this product description for any violations or prohibited content: \n\n" . $data['text']
                            ]
                        ]
                    ],
                    'generationConfig' => [
                        'temperature' => config('gemini.temperature'),
                        'topP' => config('gemini.top_p'),
                        'topK' => config('gemini.top_k'),
                        'maxOutputTokens' => config('gemini.max_output_tokens'),
                    ],
                    'safetySettings' => [
                        [
                            'category' => 'HARM_CATEGORY_HARASSMENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_HATE_SPEECH',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ],
                        [
                            'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                            'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                        ]
                    ]
                ];
            } else {
                // For image analysis, we would need to implement multipart request with image data
                // This is a placeholder for actual implementation
                return $this->getMockResponse($data);
            }
            
            // For now, return mock response until full implementation
            return $this->getMockResponse($data);
            
            // Uncomment below for actual API call implementation
            /*
            $response = Http::post($endpoint, $payload);
            
            if ($response->successful()) {
                $result = $response->json();
                // Process the response to determine if there are violations
                // This would need to be implemented based on the actual Gemini API response format
                return [
                    'success' => true,
                    'violation_detected' => false, // Logic to determine this from response
                    'details' => 'Response from Gemini API'
                ];
            } else {
                Log::error('Gemini API error', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                
                return [
                    'success' => false,
                    'violation_detected' => false,
                    'details' => 'Error calling Gemini API: ' . $response->status()
                ];
            }
            */
        } catch (\Exception $e) {
            Log::error('Exception calling Gemini API: ' . $e->getMessage());
            
            return [
                'success' => false,
                'violation_detected' => false,
                'details' => 'Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get mock response for development
     * 
     * @param array $data
     * @return array
     */
    private function getMockResponse(array $data): array
    {
        if ($data['type'] === 'text') {
            // Simulate text analysis
            $containsViolation = $this->mockTextViolationCheck($data['text']);
            return [
                'success' => true,
                'violation_detected' => $containsViolation,
                'details' => $containsViolation ? 'Text contains prohibited content' : 'No violations found'
            ];
        } else {
            // Simulate image analysis
            return [
                'success' => true,
                'violation_detected' => false,
                'details' => 'No violations found in image'
            ];
        }
    }
    
    /**
     * Process Gemini API response
     * 
     * @param array $response
     * @param string $contentType
     * @return array
     */
    private function processGeminiResponse(array $response, string $contentType): array
    {
        if (!$response['success']) {
            Log::warning('Gemini API error for ' . $contentType . ' analysis', [
                'product_id' => $this->product->id,
                'response' => $response
            ]);
            
            return [
                'hasViolation' => false,
                'details' => 'Error analyzing ' . $contentType . ' content'
            ];
        }
        
        return [
            'hasViolation' => $response['violation_detected'],
            'details' => $response['details']
        ];
    }
    
    /**
     * Mock text violation check (for development)
     * 
     * @param string $text
     * @return bool
     */
    private function mockTextViolationCheck(string $text): bool
    {
        $prohibitedWords = config('gemini.prohibited_words', ['scam', 'illegal', 'fake', 'counterfeit', 'replica']);
        
        foreach ($prohibitedWords as $word) {
            if (stripos($text, $word) !== false) {
                return true;
            }
        }
        
        return false;
    }
}
