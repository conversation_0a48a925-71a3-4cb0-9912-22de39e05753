<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Jen<PERSON>gers\Agent\Agent;

class UserSessionService
{
    /**
     * Create a new user session.
     *
     * @param User $user
     * @param Request $request
     * @param string|null $deviceId
     * @param string|null $appVersion
     * @return UserSession
     */
    public function createSession(User $user, Request $request, ?string $deviceId = null, ?string $deviceModel = null, ?string $deviceBrand = null, ?string $appVersion = null): UserSession
    {
        // Parse user agent
        $agent = new Agent();
        $agent->setUserAgent($request->userAgent());

        // Create session
        $session = UserSession::create([
            'user_id' => $user->id,
            'device_id' => $deviceId,
            'device_type' => $this->getDeviceType($agent),
            'device_model' => $deviceModel,
            'device_os' => $deviceBrand,
            'device_os_version' => null, // This would need to be provided by the client app
            'app_version' => $appVersion,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'login_at' => now(),
            'last_activity' => now(),
            'is_active' => true,
        ]);

        // Check if user has multiple devices or shares device with others
        $user->checkMultipleDevices();

        return $session;
    }

    /**
     * Update the last activity timestamp for a user session.
     *
     * @param UserSession $session
     * @return UserSession
     */
    public function updateLastActivity(UserSession $session): UserSession
    {
        $session->update([
            'last_activity' => now(),
        ]);

        return $session;
    }

    /**
     * End a user session.
     *
     * @param UserSession $session
     * @return UserSession
     */
    public function endSession(UserSession $session): UserSession
    {
        $session->update([
            'is_active' => false,
            'logout_at' => now(),
        ]);

        return $session;
    }

    /**
     * End all active sessions for a user.
     *
     * @param User $user
     * @param UserSession|null $exceptSession
     * @return void
     */
    public function endAllSessions(User $user, ?UserSession $exceptSession = null): void
    {
        $query = $user->sessions()->where('is_active', true);

        if ($exceptSession) {
            $query->where('id', '!=', $exceptSession->id);
        }

        $query->update([
            'is_active' => false,
            'logout_at' => now(),
        ]);
    }

    /**
     * Get the current user session from the request.
     *
     * @param Request $request
     * @param string|null $deviceId
     * @return UserSession|null
     */
    public function getCurrentSession(Request $request, ?string $deviceId = null): ?UserSession
    {
        if (!Auth::check()) {
            return null;
        }

        $user = Auth::user();
        
        // Try to find by device ID first if provided
        if ($deviceId) {
            $session = $user->sessions()
                ->where('device_id', $deviceId)
                ->where('is_active', true)
                ->latest('last_activity')
                ->first();
                
            if ($session) {
                return $session;
            }
        }
        
        // Otherwise try to find by IP and user agent
        return $user->sessions()
            ->where('ip_address', $request->ip())
            ->where('user_agent', $request->userAgent())
            ->where('is_active', true)
            ->latest('last_activity')
            ->first();
    }

    /**
     * Get the device type from the user agent.
     *
     * @param Agent $agent
     * @return string
     */
    protected function getDeviceType(Agent $agent): string
    {
        if ($agent->isPhone()) {
            return 'mobile';
        } elseif ($agent->isTablet()) {
            return 'tablet';
        } elseif ($agent->isDesktop()) {
            return 'desktop';
        } else {
            return 'other';
        }
    }
}
