<?php

namespace App\Services;

use App\Models\User;
use App\Models\Commission;
use App\Models\UserRate;
use App\Models\UserVerification;
use App\Models\Achievement;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Achievement Conditions Service
 *
 * This service contains all the logic for checking and awarding different types of achievements.
 * It's organized to handle multiple achievement categories:
 *
 * 1. Commission-related achievements (دفع العموله) - Year-based achievements
 * 2. Annual excellence achievements (تميز السنة المثالي)
 * 3. Brand verification achievements (موثق كعلامه تجارية)
 * 4. Premium membership achievements (عضوية تميز - 20 ratings in a year)
 */
class AchievementConditionsService
{
    /**
     * Check all achievements for a specific user
     */
    public function checkAllAchievementsForUser(User $user): array
    {
        $awardedAchievements = [];

        try {
            // Check commission-related achievements
            $commissionAchievements = $this->checkCommissionAchievements($user);
            $awardedAchievements = array_merge($awardedAchievements, $commissionAchievements);

            // Check annual excellence achievements
            $excellenceAchievements = $this->checkAnnualExcellenceAchievements($user);
            $awardedAchievements = array_merge($awardedAchievements, $excellenceAchievements);

            // Check brand verification achievements
            $verificationAchievements = $this->checkBrandVerificationAchievements($user);
            $awardedAchievements = array_merge($awardedAchievements, $verificationAchievements);

            // Check premium membership achievements
            $premiumAchievements = $this->checkPremiumMembershipAchievements($user);
            $awardedAchievements = array_merge($awardedAchievements, $premiumAchievements);

        } catch (\Exception $e) {
            Log::error('Error checking achievements for user ' . $user->id, [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $awardedAchievements;
    }

    /**
     * Check commission-related achievements (دفع العموله)
     * Each achievement is year-based - user gets achievement for each year they pay commissions
     */
    private function checkCommissionAchievements(User $user): array
    {
        $awardedAchievements = [];
        $currentYear = now()->year;

        // Get user's approved commissions for current year
        $approvedCommissions = Commission::where('user_id', $user->id)
            ->approved()
            ->byYear($currentYear)
            ->count();

        // Commission achievements based on number of commissions paid in a year
        $commissionAchievements = [
            'commission_1_paid' => 1,      // First commission paid in year
            'commission_2_paid' => 2,      // Second commission paid in year
            'commission_3_paid' => 3,      // Third commission paid in year
            'commission_5_paid' => 5,      // Fifth commission paid in year
            'commission_10_paid' => 10,    // Tenth commission paid in year
            'commission_20_paid' => 20,    // Twentieth commission paid in year
        ];

        foreach ($commissionAchievements as $slug => $requiredCount) {
            if ($approvedCommissions >= $requiredCount) {
                $awarded = $this->awardAchievementForYear($user, $slug, $currentYear);
                if ($awarded) {
                    $awardedAchievements[] = $slug;
                }
            }
        }

        return $awardedAchievements;
    }

    /**
     * Check annual excellence achievements (تميز السنة المثالي)
     */
    private function checkAnnualExcellenceAchievements(User $user): array
    {
        $awardedAchievements = [];
        $currentYear = now()->year;

        // Check if user has multiple achievements in the same year
        $yearlyAchievements = $user->achievements()
            ->whereYear('user_achievements.achieved_at', $currentYear)
            ->count();

        // Check user's overall performance metrics
        $approvedCommissions = Commission::where('user_id', $user->id)
            ->approved()
            ->byYear($currentYear)
            ->count();

        $averageRating = UserRate::where('rated_user_id', $user->id)
            ->approved()
            ->whereYear('created_at', $currentYear)
            ->avg('rate');

        // Award excellence achievement if user meets multiple criteria
        if ($yearlyAchievements >= 3 && $approvedCommissions >= 5 && $averageRating >= 4.5) {
            $awarded = $this->awardAchievement($user, 'annual_excellence');
            if ($awarded) {
                $awardedAchievements[] = 'annual_excellence';
            }
        }

        return $awardedAchievements;
    }

    /**
     * Check brand verification achievements (موثق كعلامه تجارية)
     */
    private function checkBrandVerificationAchievements(User $user): array
    {
        $awardedAchievements = [];

        // Check if user has approved verification
        $hasApprovedVerification = UserVerification::where('user_id', $user->id)
            ->where('status', 'approved')
            ->exists();

        if ($hasApprovedVerification) {
            $awarded = $this->awardAchievement($user, 'brand_verified');
            if ($awarded) {
                $awardedAchievements[] = 'brand_verified';
            }
        }

        return $awardedAchievements;
    }

    /**
     * Check premium membership achievements (عضوية تميز - 20 ratings in a year)
     */
    private function checkPremiumMembershipAchievements(User $user): array
    {
        $awardedAchievements = [];
        $currentYear = now()->year;

        // Get user's approved ratings for current year
        $approvedRatings = UserRate::where('rated_user_id', $user->id)
            ->approved()
            ->whereYear('created_at', $currentYear)
            ->count();

        if ($approvedRatings >= 20) {
            $awarded = $this->awardAchievement($user, 'premium_membership');
            if ($awarded) {
                $awardedAchievements[] = 'premium_membership';
            }
        }

        return $awardedAchievements;
    }

    /**
     * Award achievement to user if not already awarded
     */
    private function awardAchievement(User $user, string $achievementSlug): bool
    {
        $achievement = Achievement::where('slug', $achievementSlug)->first();
        
        if (!$achievement) {
            Log::warning("Achievement not found: {$achievementSlug}");
            return false;
        }

        // Check if user already has this achievement for current year
        $currentYear = now()->year;
        $hasAchievement = $user->achievements()
            ->where('achievement_id', $achievement->id)
            ->whereYear('user_achievements.achieved_at', $currentYear)
            ->exists();

        if (!$hasAchievement) {
            $user->achievements()->attach($achievement->id, [
                'achieved_at' => now(),
                'achieved_year' => now()->year,
            ]);

            Log::info("Achievement awarded", [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'achievement_id' => $achievement->id
            ]);

            return true;
        }

        return false;
    }

    /**
     * Award achievement to user for a specific year if not already awarded for that year
     */
    private function awardAchievementForYear(User $user, string $achievementSlug, int $year): bool
    {
        $achievement = Achievement::where('slug', $achievementSlug)->first();

        if (!$achievement) {
            Log::warning("Achievement not found: {$achievementSlug}");
            return false;
        }

        // Check if user already has this achievement for the specific year
        $hasAchievement = $user->achievements()
            ->where('achievement_id', $achievement->id)
            ->whereYear('user_achievements.achieved_at', $year)
            ->exists();

        if (!$hasAchievement) {
            $user->achievements()->attach($achievement->id, [
                'achieved_at' => now()->setYear($year),
                'achieved_year' => $year,
            ]);

            Log::info("Achievement awarded for year", [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'achievement_id' => $achievement->id,
                'year' => $year
            ]);

            return true;
        }

        return false;
    }

    /**
     * Check achievements for all users (used by scheduled command)
     */
    public function checkAchievementsForAllUsers(): array
    {
        $results = [
            'processed_users' => 0,
            'total_achievements_awarded' => 0,
            'errors' => []
        ];

        User::chunk(100, function ($users) use (&$results) {
            foreach ($users as $user) {
                try {
                    $awardedAchievements = $this->checkAllAchievementsForUser($user);
                    $results['processed_users']++;
                    $results['total_achievements_awarded'] += count($awardedAchievements);
                } catch (\Exception $e) {
                    $results['errors'][] = [
                        'user_id' => $user->id,
                        'error' => $e->getMessage()
                    ];
                }
            }
        });

        return $results;
    }

    /**
     * Manually award achievement to user (for admin use)
     */
    public function manuallyAwardAchievement(User $user, string $achievementSlug, User $admin): bool
    {
        $achievement = Achievement::where('slug', $achievementSlug)->first();
        
        if (!$achievement) {
            return false;
        }

        // Check if user already has this achievement
        $hasAchievement = $user->achievements()
            ->where('achievement_id', $achievement->id)
            ->exists();

        if (!$hasAchievement) {
            $user->achievements()->attach($achievement->id, [
                'achieved_at' => now(),
            ]);

            Log::info("Achievement manually awarded", [
                'user_id' => $user->id,
                'achievement_slug' => $achievementSlug,
                'achievement_id' => $achievement->id,
                'awarded_by' => $admin->id
            ]);

            return true;
        }

        return false;
    }
}
