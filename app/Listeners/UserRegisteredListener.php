<?php

namespace App\Listeners;

use App\Events\UserRegisteredEvent;
use App\Models\Achievement;
use App\Notifications\UserRegisteredNotification;
use App\RolesEnum;
use App\Http\Controllers\FirebaseController;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Role;

class UserRegisteredListener
{
    protected $firebaseController;

    public function __construct(FirebaseController $firebaseController)
    {
        $this->firebaseController = $firebaseController;
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegisteredEvent $event): void
    {
        // Send notification to admins
        $admins = [];
        Role::where('name', RolesEnum::SUPER_ADMIN)
            ->first()
            ->users->each(function ($user) use (&$admins) {
                $admins[] = $user;
            });
        Notification::send($admins, new UserRegisteredNotification($event->user));

        // Add registration achievement to user
        $currentYear = date('Y');
        $registrationAchievement = Achievement::firstOrCreate(
            [
                'role' => 'role_register',
                'name' => $currentYear,
            ],
            [
                'name' => $currentYear,
                'description' => 'Achieved for registering in ' . $currentYear,
                'icon' => 'https://picsum.photos/64/64?random=1',
                'role' => 'role_register',
            ]
        );

        // Attach achievement to user
        $event->user->achievements()->syncWithoutDetaching([
            $registrationAchievement->id => ['achieved_at' => now()]
        ]);

        // Schedule Firebase notification after 5 minutes
        \Illuminate\Support\Facades\Queue::later(
            now()->addMinutes(5),
            new \App\Jobs\SendScheduledNotification(
                $event->user->id,
                'هدف محقق',
                'مبروك عليك لقد حققت الهدف الاول لك في السنة الحالية',
                'info',
                json_encode(['id' => $registrationAchievement->id]),
                'achievement'
            )
        );
    }
}
