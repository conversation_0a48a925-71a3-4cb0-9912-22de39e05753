<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255'],
            'password' => ['nullable', 'string', 'min:8'],
            'role' => ['required', 'exists:roles,id'],
            'phone' => ['required', 'string', 'max:255', 'unique:users,phone,' . $this->user->id],
            'username' => ['nullable', 'string', 'max:255', 'unique:users,username,' . $this->user->id],
        ];
    }

    public function messages(): array
    {
        return [
            'phone.unique' => 'رقم الهاتف مستخدم من قبل مستخدم آخر',
            'username.unique' => 'اسم المستخدم مستخدم من قبل مستخدم آخر',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل مستخدم آخر',
            'phone.required' => 'رقم الهاتف مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'name.required' => 'الاسم مطلوب',
            'role.required' => 'الدور مطلوب',
            'password.min' => 'كلمة المرور يجب أن تكون على الأقل 8 أحرف',
        ];
    }
}
