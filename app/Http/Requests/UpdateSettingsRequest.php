<?php

namespace App\Http\Requests;

use App\RolesEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::user()?->hasRole(RolesEnum::SUPER_ADMIN);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'key' => 'required|string|unique:settings,key',
            'value' => 'nullable',
            'type' => 'required|in:string,integer,boolean,json,file',
            'group' => 'nullable|string',
            'description' => 'nullable|string',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'key.required' => 'المفتاح مطلوب',
            'key.string' => 'المفتاح يجب أن يكون نصاً',
            'key.unique' => 'المفتاح موجود بالفعل',
            'type.required' => 'النوع مطلوب',
            'type.in' => 'النوع يجب أن يكون أحد القيم التالية: string, integer, boolean, json, file',
            'group.string' => 'المجموعة يجب أن تكون نصاً',
            'description.string' => 'الوصف يجب أن يكون نصاً',
        ];
    }
}
