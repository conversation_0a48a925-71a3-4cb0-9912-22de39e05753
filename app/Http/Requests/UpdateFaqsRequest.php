<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFaqsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return  $this->user()->hasRole('admin') || $this->user()->hasRole('super_admin');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'question' => 'required|string|max:255',
            'answer' => 'required|string',
            'status' => 'required|string|in:active,inactive',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'question.required' => 'السؤال مطلوب',
            'question.string' => 'السؤال يجب أن يكون نصاً',
            'question.max' => 'السؤال يجب ألا يتجاوز 255 حرفاً',
            'answer.required' => 'الإجابة مطلوبة',
            'answer.string' => 'الإجابة يجب أن تكون نصاً',
            'status.required' => 'الحالة مطلوبة',
            'status.string' => 'الحالة يجب أن تكون نصاً',
            'status.in' => 'الحالة يجب أن تكون إما نشط أو غير نشط',
        ];
    }
}
