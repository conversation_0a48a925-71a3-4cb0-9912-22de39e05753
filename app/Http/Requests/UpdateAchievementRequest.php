<?php

namespace App\Http\Requests;

use App\Enums\AchievementType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAchievementRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => ['required', 'string', Rule::in(AchievementType::values())],
            'role' => 'nullable|string|max:255',
            'icon' => 'nullable|string|max:50',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'الاسم مطلوب',
            'name.string' => 'الاسم يجب أن يكون نصاً',
            'name.max' => 'الاسم يجب ألا يتجاوز 255 حرفاً',
            'description.string' => 'الوصف يجب أن يكون نصاً',
            'type.required' => 'النوع مطلوب',
            'type.string' => 'النوع يجب أن يكون نصاً',
            'type.in' => 'النوع غير صالح',
            'role.string' => 'الدور يجب أن يكون نصاً',
            'role.max' => 'الدور يجب ألا يتجاوز 255 حرفاً',
            'icon.string' => 'الأيقونة يجب أن تكون نصاً',
            'icon.max' => 'الأيقونة يجب ألا تتجاوز 50 حرفاً',
        ];
    }
}