<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAppNotificationRequest extends FormRequest
{
    public function authorize()
    {
        return auth()->user()->hasPermissionTo('app_notification_create');
    }

    public function rules()
    {
        return [
            'title'   => 'required|string|max:255',
            'body' => 'required|string',
            'data' => 'nullable|json',
            'sent_at' => 'nullable|date',
            'platform' => 'sometimes|in:all,android,ios',
            'type'    => 'sometimes|in:info,warning,error',
            'image' => 'sometimes|nullable|file|image|max:10000',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages()
    {
        return [
            'title.required' => 'العنوان مطلوب',
            'title.string' => 'العنوان يجب أن يكون نصاً',
            'title.max' => 'العنوان يجب ألا يتجاوز 255 حرفاً',
            'body.required' => 'المحتوى مطلوب',
            'body.string' => 'المحتوى يجب أن يكون نصاً',
            'data.json' => 'البيانات يجب أن تكون بصيغة JSON صالحة',
            'sent_at.date' => 'تاريخ الإرسال يجب أن يكون تاريخاً صالحاً',
            'platform.in' => 'المنصة يجب أن تكون إحدى القيم التالية: all, android, ios',
            'type.in' => 'النوع يجب أن يكون أحد القيم التالية: info, warning, error',
            'image.file' => 'الصورة يجب أن تكون ملفاً',
            'image.image' => 'الملف يجب أن يكون صورة',
            'image.max' => 'حجم الصورة يجب ألا يتجاوز 10 ميجابايت',
        ];
    }
}
