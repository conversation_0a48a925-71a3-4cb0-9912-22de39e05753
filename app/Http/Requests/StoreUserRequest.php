<?php

namespace App\Http\Requests;

use App\RolesEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::user()?->hasRole(RolesEnum::SUPER_ADMIN);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8'],
            'phone' => ['required', 'string', 'max:255', 'unique:users'],
            'role' => ['required', 'string', 'exists:roles,id'],
        ];
    }


    public function messages(): array
    {
        return [
            'phone.unique' => 'رقم الهاتف مستخدم من قبل مستخدم آخر',
            'username.unique' => 'اسم المستخدم مستخدم من قبل مستخدم آخر',
            'email.unique' => 'البريد الإلكتروني مستخدم من قبل مستخدم آخر',
            'phone.required' => 'رقم الهاتف مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'name.required' => 'الاسم مطلوب',
            'role.required' => 'الدور مطلوب',
            'password.min' => 'كلمة المرور يجب أن تكون على الأقل 8 أحرف',
        ];
    }
}
