<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return  $this->user()->hasPermissionTo('category_update');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|string',
            'order' => 'nullable|integer',
            'parent_id' => 'nullable|exists:categories,id',
            'app_item_size' => 'required|integer|min:1|max:3',
        ];
    }

    /**
     * Get custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'title.required' => 'العنوان مطلوب',
            'title.string' => 'العنوان يجب أن يكون نصاً',
            'title.max' => 'العنوان يجب ألا يتجاوز 255 حرفاً',
            'image.image' => 'الملف يجب أن يكون صورة',
            'image.mimes' => 'الصورة يجب أن تكون من نوع: jpeg, png, jpg, gif',
            'image.max' => 'حجم الصورة يجب ألا يتجاوز 2 ميجابايت',
            'icon.string' => 'الأيقونة يجب أن تكون نصاً',
            'order.integer' => 'الترتيب يجب أن يكون رقماً صحيحاً',
            'parent_id.exists' => 'القسم الأب غير موجود',
            'app_item_size.required' => 'حجم العنصر في التطبيق مطلوب',
            'app_item_size.integer' => 'حجم العنصر في التطبيق يجب أن يكون رقماً صحيحاً',
            'app_item_size.min' => 'حجم العنصر في التطبيق يجب أن يكون على الأقل 1',
            'app_item_size.max' => 'حجم العنصر في التطبيق يجب ألا يتجاوز 3',
        ];
    }
}
