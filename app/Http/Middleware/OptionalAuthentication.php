<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class OptionalAuthentication
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->bearerToken()) {
            // Try to authenticate with token if present
            try {
                Auth::shouldUse('sanctum');
                Auth::authenticate();
            } catch (\Exception $e) {
                // Token is invalid, continue as unauthenticated
            }
        }
        
        return $next($request);
    }
}