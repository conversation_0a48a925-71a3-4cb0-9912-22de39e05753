<?php

namespace App\Http\Middleware;

use App\Services\UserSessionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class TrackUserSession
{
    /**
     * The user session service instance.
     *
     * @var UserSessionService
     */
    protected $sessionService;

    /**
     * Create a new middleware instance.
     *
     * @param UserSessionService $sessionService
     * @return void
     */
    public function __construct(UserSessionService $sessionService)
    {
        $this->sessionService = $sessionService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        if (Auth::check()) {
            $user = Auth::user();

            // Get device ID from request header if available
            $deviceId = $request->header('X-Device-ID');
            $deviceModel = $request->header('X-Device-Model');
            $deviceBrand = $request->header('X-Device-Brand');
            $appVersion = $request->header('X-App-Version');

            // Try to get current session
            $session = $this->sessionService->getCurrentSession($request, $deviceId);

            if ($session) {
                // Update last activity
                $this->sessionService->updateLastActivity($session);
            } else {
                // Create new session
                $this->sessionService->createSession($user, $request, $deviceId, $deviceModel, $deviceBrand, $appVersion);
            }
        }

        return $response;
    }
}
