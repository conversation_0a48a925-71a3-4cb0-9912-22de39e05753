<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\BannerAdResource;
use App\Models\BannerAd;
use Illuminate\Http\Request;

class BannerAdApiController extends Controller
{
    /**
     * Get banner ads for a specific screen
     */
    public function getForScreen(Request $request, $screen)
    {
        $platform = $request->get('platform', 'app');
        
        $bannerAds = BannerAd::active()
            ->platform($platform)
            ->screen($screen)
            ->orderBy('order')
            ->get();
            
        return BannerAdResource::collection($bannerAds);
    }
} 