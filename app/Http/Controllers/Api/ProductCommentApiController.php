<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CommentResource;
use App\Models\ProductComment;
use App\Models\Product;
use App\Models\User;
use App\Models\Follow;
use App\Notifications\ProductCommentNotification;
use App\Notifications\ProductCommentReplyNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ProductCommentApiController extends Controller
{
    use AuthorizesRequests;

    public function index(Request $request)
    {
        $query = ProductComment::query();
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // Only get parent comments (not replies) for the main listing
        $query->whereNull('parent_id');

        $comments = $query->with([
            'user', 'product', 'replies.user'])->latest()->paginate($request->get('per_page', 15));
        return response()->json([
            'success' => true,
            'data' => $comments
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize('create', ProductComment::class);

        $validated = $request->validate([
            'id' => 'required|exists:products,id',
            'comment' => 'required|string',
        ]);

        $comment = ProductComment::create([
            'user_id' => Auth::id(),
            'product_id' => $validated['id'],
            'text' => $validated['comment'],
            'commented_at' => now(),
        ]);

        // Send notifications to product followers
        $this->notifyProductFollowers($comment);

        return response()->json([
            'success' => true,
            'data' => CommentResource::make($comment->load(['user', 'product']))
        ], 201);
    }

    public function show(ProductComment $productComment)
    {
        return response()->json([
            'success' => true,
            'data' => $productComment->load(['user', 'product'])
        ]);
    }

    public function update(Request $request, ProductComment $productComment)
    {
        $this->authorize('update', $productComment);
        $validated = $request->validate([
            'text' => 'required|string',
        ]);
        $productComment->update($validated);
        return response()->json([
            'success' => true,
            'data' => $productComment->load(['user', 'product'])
        ]);
    }

    public function destroy(ProductComment $productComment)
    {
        $this->authorize('delete', $productComment);
        $productComment->delete();
        return response()->json([
            'success' => true,
            'message' => 'Comment deleted'
        ]);
    }

    public function reply(Request $request, ProductComment $parentComment)
    {
        $this->authorize('reply', $parentComment);

        $validated = $request->validate([
            'comment' => 'required|string',
        ]);

        $reply = ProductComment::create([
            'user_id' => Auth::id(),
            'product_id' => $parentComment->product_id,
            'parent_id' => $parentComment->id,
            'text' => $validated['comment'],
            'commented_at' => now(),
        ]);

        // Send notification to the parent comment author
        $this->notifyCommentReply($parentComment, $reply);

        return response()->json([
            'success' => true,
            'data' => CommentResource::make($reply->load(['user', 'product', 'parent']))
        ], 201);
    }

    /**
     * Notify users who follow a product when a new comment is added
     */
    private function notifyProductFollowers(ProductComment $comment)
    {
        $product = $comment->product;

        // Skip notification if the commenter is the product owner
        if ($comment->user_id == $product->user_id) {
            return;
        }

        // Get all users who follow this product
        $followers = Follow::where('followable_type', Product::class)
            ->where('followable_id', $product->id)
            ->get();

        // Notify each follower
        foreach ($followers as $follower) {
            // Skip notification to the commenter
            if ($follower->user_id == $comment->user_id) {
                continue;
            }

            $user = User::find($follower->user_id);
            if ($user) {
                $user->notify(new ProductCommentNotification($product, $comment));
            }
        }

        // Also notify the product owner if they're not the commenter
        if ($product->user_id != $comment->user_id) {
            $product->user->notify(new ProductCommentNotification($product, $comment));
        }
    }

    /**
     * Notify the parent comment author when someone replies to their comment
     */
    private function notifyCommentReply(ProductComment $parentComment, ProductComment $reply)
    {
        // Don't notify if the replier is the same as the parent comment author
        if ($parentComment->user_id == $reply->user_id) {
            return;
        }

        $parentComment->user->notify(
            new ProductCommentReplyNotification(
                $parentComment->product,
                $parentComment,
                $reply
            )
        );
    }
}