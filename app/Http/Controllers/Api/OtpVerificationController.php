<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Models\Otp;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OtpVerificationController extends Controller
{
    /**
     * Send OTP verification code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendOtp(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return response()->json([
                'success' => true,
                'message' => 'البريد الإلكتروني مفعل مسبقاً'
            ]);
        }

        $otp = $request->user()->sendEmailOtpVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'تم إرسال رمز التحقق بنجاح',
            'expires_at' => $otp->expires_at
        ]);
    }

    /**
     * Verify OTP code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ], [
            'code.required' => 'رمز التحقق مطلوب',
            'code.size' => 'يجب أن يتكون رمز التحقق من 6 أرقام',
        ]);

        if ($validator->fails()) {
            return Helpers::responseError($validator->errors()->first());
        }

        $user = $request->user();

        if ($user->hasVerifiedEmail()) {
            return response()->json([
                'success' => true,
                'message' => 'البريد الإلكتروني مفعل مسبقاً'
            ]);
        }

        if (Otp::verify($user, $request->code, 'email')) {
            // Mark email as verified
            if ($user->markEmailAsVerified()) {
                event(new Verified($user));
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تفعيل البريد الإلكتروني بنجاح'
            ]);
        }

        return Helpers::responseError('رمز التحقق غير صحيح أو منتهي الصلاحية');
    }
}
