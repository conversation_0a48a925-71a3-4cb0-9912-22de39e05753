<?php

namespace App\Http\Controllers\Api;

use App\Enums\ConditionEnum;
use App\Enums\StatusEnum;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\ProductResource;
use App\Models\Product;
use App\Models\ProductMedia;
use App\Jobs\ProcessProductMedia;
use App\Jobs\AiProductReviewJob;
use App\Models\Location;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ProductAnalytics;
use App\Models\User;
use App\Models\UserFavorite;

class ProductApiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Product::query();

        if ($request->has('search')) {
            $query->search($request->search);
            $tags = Tag::where('name', 'like', '%' . $request->search . '%')
                ->limit(3)
                ->get();
            foreach ($tags as $tag) {
                $query->orWhereHas('tags', function ($q) use ($tag) {
                    $q->where('tags.id', $tag->id);
                });
            }
        }

        if ($request->has('condition')) {
            $query->byCondition($request->condition);
        }

        if ($request->has('category_id')) {
            $query->byCategory($request->category_id);
        }
        if ($request->has('brand_id')) {
            $query->byBrand($request->brand_id);
        }

        if ($request->has('location_id')) {
            $query->byLocation($request->location_id);
        }

        if ($request->has('user_id')) {
            $query->byUser($request->user_id);
        }

        if ($request->has('min_price') && $request->has('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }
        if ($request->has('sellerHashId')) {
            $user = User::where('hashId', $request->sellerHashId)->first();
            if ($user) {
                $query->byUser($user->id);
            }
        }

        if ($request->has('tag_id')) {
            $query->whereHas('tags', function ($q) use ($request) {
                $q->where('tags.id', $request->tag_id);
            });
        }
        if ($request->has('sort_by')) {
            //lowercase the sort_by value
            $sortBy = strtolower($request->sort_by);
            if ($sortBy === 'pricelowtohigh') {
                $query->orderBy('price', 'asc');
            } elseif ($sortBy === 'pricehightolow') {
                $query->orderBy('price', 'desc');
            } elseif ($sortBy === 'most_viewed') {
                $query->orderBy('views_count', 'desc');
            } elseif ($sortBy === 'created_at') {
                $query->orderBy('created_at', $request->sort_order ?? 'desc');
            } elseif ($sortBy === 'newest') {
                $query->orderBy('created_at', 'desc');
            } elseif ($sortBy === 'oldest') {
                $query->orderBy('created_at', 'asc');
            }
            //  elseif ($sortBy === 'nearest' && $request->has('latitude') && $request->has('longitude')) {
            //     $latitude = $request->latitude;
            //     $longitude = $request->longitude;
            //     $locations = Location::nearest($latitude, $longitude)
            //         ->with('products')
            //         ->get();
            //     $query->whereIn('location_id', $locations->pluck('id'));
            // }
            else{
                $query->orderBy('created_at', 'desc');
            }
        }

        if ($request->has('status')) {
            $query->byStatus($request->status);
        } else {
            $query->byStatus('approved');
        }
        $perPage = $request->has('per_page') ? $request->per_page : 12;
        $products = $query
            ->with(['user', 'category', 'location', 'tags', 'media', 'comments'])
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'Products retrieved successfully',
            'data' => ProductResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'last_page' => $products->lastPage(),
            ],
        ]);
    }

    /**
     * Get the authenticated user's products or a specific user's products if user_id is provided.
     */
    public function userProducts(Request $request)
    {
        $query = Product::query();

        // If user is authenticated, get their products
        if ($request->user()) {
            $query->where('user_id', $request->user()->id);
        }
        // Otherwise check if user_id was provided in the request
        elseif ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        } else {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Either authenticate or provide a user_id parameter',
                ],
                400,
            );
        }

        if ($request->has('status')) {
            $query->byStatus($request->status);
        }

        if ($request->has('condition')) {
            $query->byCondition($request->condition);
        }

        if ($request->has('category_id')) {
            $query->byCategory($request->category_id);
        }

        if ($request->has('location_id')) {
            $query->byLocation($request->location_id);
        }

        if ($request->has('min_price') && $request->has('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }

        if ($request->has('tag_id')) {
            $query->whereHas('tags', function ($q) use ($request) {
                $q->where('tags.id', $request->tag_id);
            });
        }

        $perPage = $request->has('per_page') ? $request->per_page : 12;
        $products = $query
            ->with(['user', 'category', 'location', 'tags', 'media'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'message' => 'User products retrieved successfully',
            'data' => ProductResource::collection($products),
            'pagination' => [
                'current_page' => $products->currentPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
                'last_page' => $products->lastPage(),
            ],
        ]);
    }

    //add , remove favorite
    public function favorite(Request $request, Product $product)
    {
        $user = $request->user();
        $isAdd = UserFavorite::where('user_id', $user->id)->where('product_id', $product->id)->exists();
        if ($isAdd) {
            $user->favorites()->detach($product->id);
        } else {
            $user->favorites()->attach($product->id);
        }
        return response()->json([
            'success' => true,
            'data' => !$isAdd,
            'message' => 'Favorite toggled successfully',
        ]);
    }

    public function favorites(Request $request)
    {
        $user = $request->user();
        $favorites = $user->favorites()->paginate(12);
        return response()->json([
            'success' => true,
            'message' => 'Favorites retrieved successfully',
            'data' => ProductResource::collection($favorites),
            'pagination' => [
                'current_page' => $favorites->currentPage(),
                'per_page' => $favorites->perPage(),
                'total' => $favorites->total(),
                'last_page' => $favorites->lastPage(),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $conditions = ConditionEnum::getLablesAr();

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'location_id' => 'required|exists:locations,id',
            'condition' => 'required|in:' . implode(',', array_values($conditions)),
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
            'allow_phone_call' => 'required|boolean',
            'allow_chat' => 'required|boolean',
            'alternative_phone' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        $condition = ConditionEnum::getKeyByLabel($request->condition);
        DB::beginTransaction();
        try {
            $product = new Product();
            $product->title = $request->title;
            $product->desc = $request->description;
            $product->price = $request->price;
            $product->category_id = $request->category_id;
            $product->location_id = $request->location_id;
            $product->condition = $condition;
            $product->user_id = $request->user()->id;
            $product->allow_phone_call = $request->allow_phone_call;
            $product->allow_chat = $request->allow_chat;
            $product->alternative_phone = $request->alternative_phone;
            $product->save();

            if (isset($request->tag_ids)) {
                $product->tags()->attach($request->tag_ids);
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $fileOriginalName = $image->getClientOriginalName();
                    $fileSize = $image->getSize();
                    $fileMimeType = $image->getClientMimeType();
                    $fileExtension = $image->getClientOriginalExtension();
                    $fileName = time() . '_' . $index . '.' . $fileExtension;

                    // Store in product-specific directory
                    $path = "products/{$product->id}/images/" . $fileName;
                    Storage::disk('products')->put($path, file_get_contents($image));

                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = $fileMimeType;
                    $media->file_size = $fileSize;
                    $media->extension = $fileExtension;
                    $media->path = $path;
                    $media->order = $index;
                    $media->is_primary = $index === 0;
                    $media->product_id = $product->id;
                    $media->save();

                    // Dispatch job to process the media
                    ProcessProductMedia::dispatch($media, $path);

                }
            }

            // Handle video upload
            if ($request->hasFile('video')) {
                $video = $request->file('video');
                $fileName = time() . '.' . $video->getClientOriginalExtension();
                $path = "products/{$product->id}/videos/" . $fileName;

                Storage::disk('products')->put($path, file_get_contents($video));

                $media = new ProductMedia();
                $media->name = $fileName;
                $media->type = 'video';
                $media->mime_type = $video->getClientMimeType();
                $media->file_size = $video->getSize();
                $media->extension = $video->getClientOriginalExtension();
                $media->path = $path;
                $media->product_id = $product->id;
                $media->save();

                // Dispatch job to process the video
                ProcessProductMedia::dispatch($media, $path);
            }

            DB::commit();

            // Dispatch AI product review job after transaction is committed
            AiProductReviewJob::dispatch($product);

            return response()->json(
                [
                    'success' => true,
                    'message' => 'Product created successfully',
                    'data' => new ProductResource($product->load(['user', 'category', 'location', 'tags', 'media'])),
                ],
                201,
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Failed to create product',
                    'error' => $e->getMessage(),
                ],
                500,
            );
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, Product $product)
    {
        $product->incrementViews();

        // Record the product view in product_reports table
        $report = new ProductAnalytics();
        $report->product_id = $product->id;
        $report->user_id = request()->user() ? request()->user()->id : null;
        $report->ip_address = request()->ip();
        $report->user_agent = request()->userAgent();
        $report->device = $this->getDeviceInfo(request()->userAgent());
        $report->viewed_at = now();
        $report->save();

        return response()->json([
            'success' => true,
            'message' => 'Product retrieved successfully',
            'data' => new ProductResource($product->load(['user', 'category', 'location', 'tags', 'media'])),
        ]);
    }

    /**
     * Get device information from user agent
     */
    private function getDeviceInfo($userAgent)
    {
        $device = 'Unknown';

        if (preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i', $userAgent)) {
            $device = 'Mobile';
        } elseif (preg_match('/android|ipad|playbook|silk/i', $userAgent)) {
            $device = 'Tablet';
        } elseif (preg_match('/macintosh|mac os x/i', $userAgent)) {
            $device = 'Mac';
        } elseif (preg_match('/windows|win32/i', $userAgent)) {
            $device = 'Windows';
        } elseif (preg_match('/linux/i', $userAgent)) {
            $device = 'Linux';
        }

        return $device;
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $conditions = ConditionEnum::getLablesAr();
        $statuses = StatusEnum::getLabelsAr();

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'sometimes|string',
            'price' => 'sometimes|numeric|min:0',
            'category_id' => 'sometimes|exists:categories,id',
            'location_id' => 'sometimes|exists:locations,id',
            'condition' => 'sometimes|in:' . implode(',', array_keys($conditions)),
            'status' => 'sometimes|in:' . implode(',', array_keys($statuses)),
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
            'delete_images' => 'nullable|array',
            'delete_images.*' => 'exists:product_media,id',
            'delete_video' => 'nullable|exists:product_media,id',
            'allow_phone_call' => 'sometimes|boolean',
            'allow_chat' => 'sometimes|boolean',
            'alternative_phone' => 'sometimes|string',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        DB::beginTransaction();

        try {
            $product->title = $request->title ?? $product->title;
            $product->desc = $request->description ?? $product->desc;
            $product->price = $request->price ?? $product->price;
            $product->category_id = $request->category_id ?? $product->category_id;
            $product->location_id = $request->location_id ?? $product->location_id;
            $product->condition = $request->condition ?? $product->condition;
            $product->status = $request->status ?? $product->status;
            $product->allow_phone_call = $request->has('allow_phone_call') ? $request->allow_phone_call : $product->allow_phone_call;
            $product->allow_chat = $request->has('allow_chat') ? $request->allow_chat : $product->allow_chat;
            $product->alternative_phone = $request->has('alternative_phone') ? $request->alternative_phone : $product->alternative_phone;
            $product->save();

            if (isset($request->tag_ids)) {
                $product->tags()->sync($request->tag_ids);
            } else {
                $product->tags()->detach();
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $fileName = time() . '_' . $index . '.' . $image->getClientOriginalExtension();
                    $path = "products/{$product->id}/images/" . $fileName;

                    Storage::disk('products')->put($path, file_get_contents($image));

                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = $image->getClientMimeType();
                    $media->file_size = $image->getSize();
                    $media->extension = $image->getClientOriginalExtension();
                    $media->path = $path;
                    $media->order = ProductMedia::where('product_id', $product->id)->where('type', 'image')->count();
                    $media->product_id = $product->id;
                    $media->save();

                    ProcessProductMedia::dispatch($media, $path);
                }
            }

            // Handle video upload
            if ($request->hasFile('video')) {
                // Delete existing video if any
                $existingVideo = $product->media()->where('type', 'video')->first();
                if ($existingVideo) {
                    Storage::disk('products')->deleteDirectory("products/{$product->id}/videos");
                    $existingVideo->delete();
                }

                $video = $request->file('video');
                $fileName = time() . '.' . $video->getClientOriginalExtension();
                $path = "products/{$product->id}/videos/" . $fileName;

                Storage::disk('products')->put($path, file_get_contents($video));

                $media = new ProductMedia();
                $media->name = $fileName;
                $media->type = 'video';
                $media->mime_type = $video->getClientMimeType();
                $media->file_size = $video->getSize();
                $media->extension = $video->getClientOriginalExtension();
                $media->path = $path;
                $media->product_id = $product->id;
                $media->save();

                ProcessProductMedia::dispatch($media, $path);
            }

            // Delete selected images
            if (isset($request->delete_images)) {
                $mediaToDelete = ProductMedia::whereIn('id', $request->delete_images)->where('product_id', $product->id)->get();

                foreach ($mediaToDelete as $media) {
                    Storage::disk('products')->delete([$media->path, $media->thumbnail_path, $media->small_path]);
                    $media->delete();
                }
            }

            // Delete selected video
            if (isset($request->delete_video)) {
                $videoMedia = ProductMedia::where('id', $request->delete_video)->where('product_id', $product->id)->first();

                if ($videoMedia) {
                    Storage::disk('products')->deleteDirectory("products/{$product->id}/videos");
                    $videoMedia->delete();
                }
            }

            DB::commit();

            // Dispatch AI product review job after transaction is committed
            AiProductReviewJob::dispatch($product);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully',
                'data' => new ProductResource($product->load(['user', 'category', 'location', 'tags', 'media'])),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Failed to update product',
                    'error' => $e->getMessage(),
                ],
                500,
            );
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        $product->tags()->detach();
        $product->deleted_by = request()->user()->id;
        $product->save();
        $product->delete();

        return response()->json([
            'success' => true,
            'message' => 'Product deleted successfully',
        ]);
    }

    /**
     * Handle product image upload.
     */
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('products', $filename, 'public');

            return response()->json([
                'success' => true,
                'url' => asset('storage/' . $path),
            ]);
        }

        return response()->json(
            [
                'success' => false,
                'message' => 'No file uploaded',
            ],
            400,
        );
    }

    public function similarProducts(Product $product)
    {
        $productTags = $product->tags->pluck('id');
        $productCategory = $product->category_id;
        $productLocation = $product->location_id;
        //* get products with same tags, category, location
        $productsWithSameTags = Product::whereHas('tags', function ($query) use ($productTags) {
            $query->whereIn('tags.id', $productTags);
        })->get();
        $productsWithSameCategory = Product::where('category_id', $productCategory)->get();
        $productsWithSameLocation = Product::where('location_id', $productLocation)->get();

        $similarProducts = $productsWithSameTags->merge($productsWithSameCategory)->merge($productsWithSameLocation);

        $similarProducts = $similarProducts->unique('id');

        $similarProducts = $similarProducts->take(10);

        return response()->json([
            'success' => true,
            'message' => 'Similar products retrieved successfully',
            'data' => ProductResource::collection($similarProducts),
        ]);
    }
}
