<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AppNotificationResource;
use App\Models\AppNotification;
use App\Models\AppUserNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AppNotificationApiController extends Controller
{
    /**
     * Get all notifications for authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();

        // Get user notifications or all system notifications
        $notifications = $user->userNotifications()->orderBy('created_at', 'desc')->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Notifications retrieved successfully',
            'data' => AppNotificationResource::collection($notifications),
            'meta' => [
                'total' => $notifications->total(),
                'current_page' => $notifications->currentPage(),
                'last_page' => $notifications->lastPage(),
                'per_page' => $notifications->perPage(),
            ],
        ]);
    }

    /**
     * Mark notification as read
     *
     * @param Request $request
     * @param AppNotification $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead(Request $request, AppNotification $notification)
    {
        $user = $request->user();

        // Check if user already has a record for this notification
        $userNotification = AppUserNotification::where('user_id', $user->id)->where('app_notification_id', $notification->id)->first();

        if (!$userNotification) {
            // Create a new record if it doesn't exist
            $userNotification = AppUserNotification::create([
                'user_id' => $user->id,
                'app_notification_id' => $notification->id,
            ]);
        }

        // Mark as read if not already
        if (!$userNotification->read_at) {
            $userNotification->read_at = now();
            $userNotification->save();
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
        ]);
    }

    /**
     * Mark all notifications as read
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAllAsRead(Request $request)
    {
        $user = $request->user();

        // Get all notifications for the user
        $notifications = AppNotification::whereHas('users', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
            ->orWhere('type', 'system')
            ->get();

        // Mark all as read
        foreach ($notifications as $notification) {
            $userNotification = AppUserNotification::firstOrCreate([
                'user_id' => $user->id,
                'app_notification_id' => $notification->id,
            ]);

            if (!$userNotification->read_at) {
                $userNotification->read_at = now();
                $userNotification->save();
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read',
        ]);
    }

    /**
     * Get count of unread notifications
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCount(Request $request)
    {
        $user = $request->user();

        // Get all notification IDs
        $allNotificationIds = AppNotification::where('type', 'system')
            ->orWhereHas('users', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->pluck('id');

        // Get IDs of read notifications
        $readNotificationIds = AppUserNotification::where('user_id', $user->id)->whereNotNull('read_at')->pluck('app_notification_id');

        // Calculate unread count
        $unreadCount = count($allNotificationIds) - $readNotificationIds->intersect($allNotificationIds)->count();

        return response()->json([
            'success' => true,
            'message' => 'Unread notification count retrieved',
            'data' => [
                'count' => $unreadCount,
            ],
        ]);
    }

    /**
     * Save FCM token for the user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveFcmToken(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'fcm_token' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Validation error',
                    'errors' => $validator->errors(),
                ],
                422,
            );
        }

        // Save FCM token to user
        $user->fcm_token = $request->fcm_token;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'FCM token saved successfully',
        ]);
    }
    /**
     * Delete FCM token for the user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteFcmToken(Request $request)
    {
        $user = $request->user();

        // Delete FCM token from user
        $user->fcm_token = null;
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'FCM token deleted successfully',
        ]);
    }

    /**
     * Delete notification
     *
     * @param Request $request
     * @param AppNotification $notification
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, AppNotification $notification)
    {
        $user = $request->user();

        // Check if user has a record for this notification
        $userNotification = AppUserNotification::where('user_id', $user->id)->where('app_notification_id', $notification->id)->first();

        if ($userNotification) {
            // Delete the user notification record
            $userNotification->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted successfully',
        ]);
    }
}
