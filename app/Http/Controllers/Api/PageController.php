<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{
    public function index()
    {
        $pages = Page::where('status', true)
            ->whereIn('page_type', ['app', 'all'])
            ->select('id', 'title', 'slug')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $pages,
        ]);
    }

    public function show(string $slug)
    {
        $page = Page::where('slug', $slug)
            ->where('status', true)
            ->whereIn('page_type', ['app', 'all'])
            ->first();

        if (!$page) {
            return response()->json(
                [
                    'status' => 'error',
                    'message' => 'Page not found',
                ],
                404,
            );
        }

        return response()->json([
            'status' => 'success',
            'data' => $page,
        ]);
    }

}
