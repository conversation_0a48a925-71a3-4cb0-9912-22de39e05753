<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AppMenuResource;
use App\Models\AppMenu;
use Illuminate\Http\Request;

class AppMenuApiController extends Controller
{
    /**
     * Get all app menus or filter by group
     */
    public function index(Request $request)
    {
        $query = AppMenu::query()->where('is_active', true);
        
      
        // Get the menu items ordered by order
        $appMenus = $query->orderBy('order')->whereNull('parent_id')->get();
        
       
        
        return response()->json([
            'data' => AppMenuResource::collection($appMenus),
            'success' => true,
        ]);
    }

    /**
     * Get a specific app menu item
     */
    public function show(string $id)
    {
        $appMenu = AppMenu::findOrFail($id);
        
        // Return nested structure if requested
        if (request()->has('nested') && request()->nested) {
            return response()->json([
                'data' => $appMenu->getNestedStructure(),
                'success' => true,
            ]);
        }
        
        return response()->json([
            'data' => $appMenu,
            'success' => true,
        ]);
    }
} 