<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserRateResource;
use App\Models\User;
use App\Models\UserRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserRateApiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Validate request parameters
        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,id',
            'status' => 'nullable|in:pending,approved,rejected',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $perPage = $request->input('per_page', 15);

        $query = UserRate::with(['rater:id,name,email,username,photo', 'ratedUser:id,name,email,username,photo']);

        // Filter by user_id if provided
        if ($request->has('user_id')) {
            $query->where('rated_user_id', $request->user_id);
        }

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('status', $request->status);
        } else {
            // By default, only show approved ratings
            $query->where('status', 'approved');
        }

        // Order by created_at descending
        $query->orderBy('created_at', 'desc');

        $ratings = $query->paginate($perPage);

        return response()->json([
            'status' => true,
            'message' => 'Ratings retrieved successfully',
            'data' => UserRateResource::collection($ratings),
            'meta' => [
                'current_page' => $ratings->currentPage(),
                'last_page' => $ratings->lastPage(),
                'per_page' => $ratings->perPage(),
                'total' => $ratings->total()
            ]
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'userId' => 'required|exists:users,hashId',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('hashId', $request->userId)->first();
        if (!$user) {
            return response()->json([
                'status' => false,
                'message' => 'User not found'
            ], 404);
        }

        // Check if user is trying to rate themselves
        if ($user->id == Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => 'You cannot rate yourself'
            ], 400);
        }

        // Check if user has already rated this user
        $existingRate = UserRate::where('rater_id', Auth::id())
            ->where('rated_user_id', $request->rated_user_id)
            ->first();

        if ($existingRate) {
            return response()->json([
                'status' => false,
                'message' => 'You have already rated this user'
            ], 400);
        }
        // Create new rating
        $userRate = UserRate::create([
            'rater_id' => Auth::id(),
            'rated_user_id' => $user->id,
            'rate' => $request->rating,
            'comment' => $request->comment,
            'status' => 'pending',
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Rating submitted successfully and pending review',
            'data' => new UserRateResource($userRate)
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($hashId)
    {
        $user = User::where('hash_id',$hashId)->firstOrFail();
        $userRate = UserRate::where('user_id',$user->id)->where('status','approved')->with(['rater:id,name,email,username,photo', 'ratedUser:id,name,email,username,photo'])
            ->get();

        if (!$userRate) {
            return response()->json([
                'status' => false,
                'message' => 'Rating not found'
            ], 404);
        }


        return response()->json([
            'status' => true,
            'message' => 'Rating retrieved successfully',
            'data' => UserRateResource::collection($userRate),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $userRate = UserRate::find($id);

        if (!$userRate) {
            return response()->json([
                'status' => false,
                'message' => 'Rating not found'
            ], 404);
        }

        // Only the rater can update their rating
        if ($userRate->rater_id !== Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => 'You are not authorized to update this rating'
            ], 403);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'rate' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Update rating
        $userRate->update([
            'rate' => $request->rate,
            'comment' => $request->comment,
            'status' => 'pending', // Reset to pending for review
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Rating updated successfully and pending review',
            'data' => new UserRateResource($userRate)
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $userRate = UserRate::find($id);

        if (!$userRate) {
            return response()->json([
                'status' => false,
                'message' => 'Rating not found'
            ], 404);
        }

        // Only the rater can delete their rating
        if ($userRate->rater_id !== Auth::id()) {
            return response()->json([
                'status' => false,
                'message' => 'You are not authorized to delete this rating'
            ], 403);
        }

        $userRate->delete();

        return response()->json([
            'status' => true,
            'message' => 'Rating deleted successfully'
        ]);
    }

    /**
     * Get user ratings.
     *
     * @param  int  $userId
     * @return \Illuminate\Http\Response
     */
    public function getUserRatings($userHashId)
    {
        $user = User::where('hashId', $userHashId)->first();

        if (!$user) {
            return response()->json([
                'status' => false,
                'message' => 'User not found'
            ], 404);
        }

        $ratings = UserRate::with('rater:id,name,email,username,photo')
            ->where('rated_user_id', $user->id)
            ->where('status', 'approved')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $averageRating = $user->average_rating;
        $ratingsCount = $user->ratings_count;

        return response()->json([
            'status' => true,
            'message' => 'User ratings retrieved successfully',
            'data' => [
                'average_rating' => $averageRating,
                'ratings_count' => $ratingsCount,
                'ratings' => UserRateResource::collection($ratings),
                'meta' => [
                    'current_page' => $ratings->currentPage(),
                    'last_page' => $ratings->lastPage(),
                    'per_page' => $ratings->perPage(),
                    'total' => $ratings->total()
                ]
            ]
        ]);
    }

    /**
     * Get ratings given by the authenticated user.
     *
     * @return \Illuminate\Http\Response
     */
    public function getMyRatings()
    {
        $ratings = UserRate::with('ratedUser:id,name,email,username,photo')
            ->where('rater_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'status' => true,
            'message' => 'Your ratings retrieved successfully',
            'data' => UserRateResource::collection($ratings),
            'meta' => [
                'current_page' => $ratings->currentPage(),
                'last_page' => $ratings->lastPage(),
                'per_page' => $ratings->perPage(),
                'total' => $ratings->total()
            ]
        ]);
    }
}
