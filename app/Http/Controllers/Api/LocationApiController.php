<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\LocationResource;
use App\Models\Location;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LocationApiController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Location::query();

        if ($request->has('search')) {
            $query->search($request->search);
        }

        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        } else {
            $query->whereNull('parent_id');
        }

        $locations = $query->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'message' => 'Locations retrieved successfully',
            'data' => LocationResource::collection($locations),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:locations,id',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        $location = Location::create([
            'name' => $request->name,
            'parent_id' => $request->parent_id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'created_by' => $request->user()->id,
        ]);

        return response()->json([
            'success' => true,

            'message' => 'Location created successfully',
            'data' => new LocationResource($location),
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Location $location)
    {
        return response()->json([
            'success' => true,

            'message' => 'Location retrieved successfully',
            'data' => new LocationResource($location),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Location $location)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'parent_id' => 'nullable|exists:locations,id',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        $location->update([
            'name' => $request->name ?? $location->name,
            'parent_id' => $request->parent_id ?? $location->parent_id,
            'latitude' => $request->latitude ?? $location->latitude,
            'longitude' => $request->longitude ?? $location->longitude,
            'updated_by' => $request->user()->id,
        ]);

        return response()->json([
            'success' => true,

            'message' => 'Location updated successfully',
            'data' => new LocationResource($location),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Location $location)
    {
        $location->deleted_by = request()->user()->id;
        $location->save();
        $location->delete();

        return response()->json([
            'message' => 'Location deleted successfully',
        ]);
    }

    /**
     * Get children of a specific location
     *
     * @param Location $location
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChildren(Location $location)
    {
        $children = $location->children;
        return response()->json($children);
    }
}