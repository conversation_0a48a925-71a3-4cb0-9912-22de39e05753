<?php

namespace App\Http\Controllers\Api;

use App\Events\UserRegisteredEvent;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\UserResource;
use App\Models\User;
use App\RolesEnum;
use App\Services\AchievementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class UserApiController extends Controller
{
    /**
     * Register a new user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8',
                'phone' => 'required|string|unique:users',
            ],
            [
                'name.required' => 'الاسم مطلوب',
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
                'email.unique' => 'البريد الإلكتروني مستخدم بالفعل',
                'password.required' => 'كلمة المرور مطلوبة',
                'password.min' => 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
                'phone.required' => 'رقم الهاتف مطلوب',
                'phone.unique' => 'رقم الهاتف مستخدم بالفعل',
            ],
        );

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
        ]);

        // Assign default role if needed
        $user->assignRole(RolesEnum::USER);

        $token = $user->createToken('auth_token')->plainTextToken;

        // Dispatch user registered event
        UserRegisteredEvent::dispatch($user);

        // Send verification based on client type
        if ($request->has('client_type') && $request->client_type === 'app') {
            // For app users, send OTP verification
            $otp = $user->sendEmailOtpVerificationNotification();
        } else {
            // For web users, send email verification link
            $user->sendEmailVerificationNotification();
        }


        try{
            $deviceId = isset($request->device_id) ? $request->device_id : null;
            $deviceModel = isset($request->device_id) ? $request->device_id : null;
            $deviceBrand = isset($request->device_id) ? $request->device_id : null;

        }catch(e){}

        $response = [
            'success' => true,
            'data' => UserResource::make($user),
            'token' => $token,
        ];

        if ($request->has('client_type') && $request->client_type === 'app') {
            $response['message'] = 'تم تسجيل المستخدم بنجاح، يرجى إدخال رمز التحقق المرسل إلى بريدك الإلكتروني';
            $response['verification_type'] = 'otp';
        } else {
            $response['message'] = 'تم تسجيل المستخدم بنجاح، يرجى تفعيل بريدك الإلكتروني';
            $response['verification_type'] = 'email_link';
        }

        return response()->json($response, 201);
    }

    /**
     * Login user and create token
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|string|email',
                'password' => 'required|string',
            ],
            [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
                'password.required' => 'كلمة المرور مطلوبة',
            ],
        );

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json(
                [
                    'message' => 'بيانات تسجيل الدخول غير صحيحة',
                ],
                401,
            );
        }

        $user = User::where('email', $request->email)->firstOrFail();
        $token = $user->createToken('auth_token')->plainTextToken;

        // Update last activity timestamp
        $user->last_activity = now();
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => UserResource::make($user),
            'token' => $token,
        ]);
    }

    /**
     * Login user with social media credentials
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function social(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|string|email',
                'name' => 'required|string|max:255',
                'phone' => 'sometimes|string',
                'provider' => 'required|string|in:google,facebook,apple',
                'photoUrl' => 'sometimes',
            ],
            [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
                'name.required' => 'الاسم مطلوب',
                'provider.required' => 'مزود الخدمة مطلوب',
                'provider.in' => 'مزود الخدمة غير صالح',
            ],
        );

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }
        // Check if user already exists
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            // Create new user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make(uniqid()), // Generate a random password
                'phone' => $request->phone ?? null,
            ]);
            UserRegisteredEvent::dispatch($user);
        }
        // Assign default role if needed
        if (!$user->hasRole(RolesEnum::USER)) {
            $user->assignRole(RolesEnum::USER);
        }
        // Update user photo URL if provided
        if ($request->has('photoUrl')) {
            $user->update(['photo' => $request->photoUrl]);
        }
        // Check if the user is already logged in

        $token = $user->createToken('auth_token')->plainTextToken;

        // Update last activity timestamp
        $user->last_activity = now();
        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'social login successful',
            'data' => UserResource::make($user),
            'token' => $token,
        ]);
    }

    /**
     * Get authenticated user profile
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function profile(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Profile retrieved successfully',
            'data' => UserResource::make($request->user()),
        ]);
    }
    /**
     * Get authenticated user profile
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function userProfile(Request $request,$hashId)
    {
        $userModel = User::where('hashId',$hashId)->firstOrFail();
        return response()->json([
            'success' => true,
            'message' => $hashId,
            'data' => UserResource::make($userModel),
        ]);
    }

    /**
     * Update user profile
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'sometimes|string|max:255',
                'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
                'phone' => 'sometimes|string|unique:users,phone,' . $user->id,
                'currentPassword' => 'sometimes|string|min:8',
                'newPassword' => 'sometimes|string|min:8',
            ],
            [
                'name.string' => 'يجب أن يكون الاسم نصًا',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
                'email.unique' => 'البريد الإلكتروني مستخدم بالفعل',
                'phone.unique' => 'رقم الهاتف مستخدم بالفعل',
                'password.min' => 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
            ],
        );

        if ($validator->fails()) {
            return Helpers::responseError($validator->errors()->first());
        }

        //* Change Password
        if ($request->filled('currentPassword')) {
            if (!Hash::check($request->currentPassword, $user->password)) {
                return Helpers::responseError('كلمة المرور الحالية غير صحيحة');
            } else {
                $user->password = Hash::make($request->newPassword);
                $user->save();
                return Helpers::responseData([
                    'message' => 'تم تعيين كلمة المرور الجديدة بنجاح',
                    'data' => UserResource::make($user),
                ]);
            }
        }

        //* Change Profile image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            Storage::disk('users')->put($imageName, file_get_contents($image));
            $data['photo'] = 'users/' . $imageName;
            $user->update(['photo' => $data['photo']]);
            $user->save();
            return Helpers::responseData([
                'success' => true,
                'message' => 'تم تعيين صورة الملف الشخصي بنجاح',
                'data' => UserResource::make($user),
            ]);
        }

        //* Change profile cover
        if ($request->hasFile('cover')) {
            $cover = $request->file('cover');
            $coverName = time() . '.' . $cover->getClientOriginalExtension();
            Storage::disk('users')->put($coverName, file_get_contents($cover));
            $data['cover'] = 'users/' . $coverName;
            $user->update(['cover' => $data['cover']]);
            $user->save();
            return Helpers::responseData([
                'success' => true,
                'message' => 'تم تعيين صورة الغلاف الشخصي بنجاح',
                'data' => UserResource::make($user),
            ]);
        }

        $data = $request->only(['name', 'email', 'phone']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }

        $user->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => UserResource::make($user),
        ]);
    }

    /**
     * Logout user (revoke token)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully',
        ]);
    }

    /**
     * Delete user account
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteAccount(Request $request)
    {
        $user = $request->user();

        // Revoke all tokens
        $user->tokens()->delete();

        // Delete user
        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الحساب بنجاح',
        ]);
    }

    public function resetPassword(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|string|email',
                'password' => 'required|string|min:8',
                'token' => 'required|string',
            ],
            [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
                'password.required' => 'كلمة المرور مطلوبة',
                'password.min' => 'يجب أن تتكون كلمة المرور من 8 أحرف على الأقل',
                'token.required' => 'الرمز مطلوب',
            ],
        );

        if ($validator->fails()) {
            return Helpers::responseError($validator->errors()->first());
        }

        $status = Password::reset($request->only('email', 'password', 'token'), function ($user, $password) {
            $user->forceFill([
                'password' => Hash::make($password),
            ]);

            $user->save();
        });

        if ($status === Password::PASSWORD_RESET) {
            return response()->json([
                'success' => true,
                'message' => 'تم إعادة تعيين كلمة المرور بنجاح',
            ]);
        }

        return Helpers::responseError('حدث خطأ أثناء إعادة تعيين كلمة المرور');
    }

    public function forgetPassword(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'email' => 'required|string|email',
            ],
            [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يجب أن يكون البريد الإلكتروني عنوان بريد إلكتروني صالح',
            ],
        );

        if ($validator->fails()) {
            return Helpers::responseError($validator->errors()->first());
        }

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return Helpers::responseError('المستخدم غير موجود');
        }

        $status = Password::sendResetLink($request->only('email'));

        if ($status === Password::RESET_LINK_SENT) {
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. يرجى التحقق من بريدك واتباع التعليمات لإعادة تعيين كلمة المرور.',
            ]);
        }

        return Helpers::responseError('حدث خطأ أثناء إرسال رابط إعادة تعيين كلمة المرور');
    }

    /**
     * Get user's achievements
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserAchievements(Request $request)
    {
        $user = $request->user();
        $achievementService = app(AchievementService::class);

        $achievements = $achievementService->getUserAchievements($user);

        return response()->json([
            'success' => true,
            'message' => 'User achievements retrieved successfully',
            'data' => $achievements,
        ]);
    }
}
