<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Follow;
use App\Models\Location;
use App\Models\Product;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Request;

class FollowApiController extends Controller
{

    public function getFollows(Request $request)
    {
        $user = $request->user();

        $followingUsers = $this->mapFollows(
            Follow::where('user_id', $user->id)
                ->where('followable_type', 'App\\Models\\User')
                ->with(['followable'])
                ->get()
        );

        $followingProductsAndTags = $this->mapFollows(
            Follow::where('user_id', $user->id)
                ->whereIn('followable_type', ['App\\Models\\Product', 'App\\Models\\Tag'])
                ->with(['followable'])
                ->get()
        );

        $followers = $this->mapFollows(
            Follow::where('followable_id', $user->id)
                ->where('followable_type', 'App\\Models\\User')
                ->with(['followable'])
                ->get()
        );
        

        $data = [
            'followingUsers' => $followingUsers,
            'followers' => $followers,
            'followingOthers' => $followingProductsAndTags,
            
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    public function toggleFollow(Request $request)
    {
        $user = $request->user();
        $type = $request->input('type');
        $id = $request->input('id');

        switch ($type) {
            case 'product':
                $followable = Product::findOrFail($id);
                break;
            case 'user':
                $followable = User::where('hashId', $id)->firstOrFail();
                break;
            case 'tag':
                $followable = Tag::findOrFail($id);
                break;
            case 'location':
                $followable = Location::findOrFail($id);
                break;
            case 'category':
                $followable = Category::findOrFail($id);
                break;
            case 'brand':
                $followable = Brand::findOrFail($id);
                break;
            default:
                return response()->json([
                    'success' => false,
                    'data' => 'Invalid followable type',
                ], 400);
        }

        $existing = Follow::where('user_id', $user->id)
            ->where('followable_type', get_class($followable))
            ->where('followable_id', $followable->id)
            ->first();

        if ($existing) {
            $existing->delete();
            return response()->json([
                'success' => true,
                'data' => 'unfollowed'
            ]);
        } else {
            Follow::create([
                'followable_type' => get_class($followable),
                'followable_id' => $followable->id,
                'user_id' => $user->id
            ]);
            return response()->json([
                'success' => true,
                'data' => 'followed',
            ]);
        }
    }

    /**
     * Map follows to a simple array structure
     *
     * @param \Illuminate\Support\Collection $follows
     * @return array
     */
    private function mapFollows($follows)
    {
        return $follows->map(function ($follow) {
            $followable = $follow->followable;
            return [
                'id' => $followable->id ?? null,
                'hashId' => $followable->hashId ?? null,
                'name' => $followable->name ?? null,
                'image' => $followable->image ?? null,
            ];
        })->all();
    }
}

