<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\URL;

class EmailVerificationController extends Controller
{
    /**
     * Send email verification link
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function sendVerificationEmail(Request $request)
    {
        if ($request->user()->hasVerifiedEmail()) {
            return response()->json([
                'success' => true,
                'message' => 'البريد الإلكتروني مفعل مسبقاً'
            ]);
        }

        $request->user()->sendEmailVerificationNotification();

        return response()->json([
            'success' => true,
            'message' => 'تم إرسال رابط التفعيل بنجاح'
        ]);
    }

    /**
     * Verify email
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request, $id, $hash)
    {
        $user = User::findOrFail($id);

        if (!hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
            return $request->wantsJson()
                ? response()->json([
                    'success' => false,
                    'message' => 'رابط التفعيل غير صالح'
                ], 400)
                : redirect()->route('verification.verify')->with('error', 'رابط التفعيل غير صالح');
        }

        if ($user->hasVerifiedEmail()) {
            return $request->wantsJson()
                ? response()->json([
                    'success' => true,
                    'message' => 'البريد الإلكتروني مفعل مسبقاً'
                ])
                : redirect()->route('verification.success');
        }

        if ($user->markEmailAsVerified()) {
            event(new Verified($user));
        }

        return $request->wantsJson()
            ? response()->json([
                'success' => true,
                'message' => 'تم تفعيل البريد الإلكتروني بنجاح'
            ])
            : redirect()->route('verification.success');
    }

    /**
     * Generate verification URL for API
     *
     * @param User $user
     * @return string
     */
    public static function generateVerificationUrl(User $user)
    {
        $verificationUrl = URL::temporarySignedRoute(
            'verification.verify',
            now()->addMinutes(60),
            [
                'id' => $user->id,
                'hash' => sha1($user->getEmailForVerification()),
            ]
        );

        return $verificationUrl;
    }
} 