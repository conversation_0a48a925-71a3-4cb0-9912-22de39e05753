<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\AppUpdateResource;
use App\Models\AppUpdate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SettingsApiController extends Controller
{
    public function getSettings(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|in:android,ios',
            'version' => 'required|string',
        ]);

        $user = $request->user();

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Find latest update for the platform
        $latestUpdate = AppUpdate::where('platform', $request->platform)->orderBy('release_date', 'desc')->first();

        if (!$latestUpdate) {
            return response()->json([
                'success' => true,

                'message' => 'No updates available',
                'data' => [
                    'needs_update' => false,
                    'force_update' => false,
                    'app_in_maintenance_mode' => false,
                    'maintenanceMessage' => 'The app is currently under maintenance. Please check back later.',
                    'is_authenticated' => $user ? true : false,
                ],
            ]);
        }
        // Compare versions
        $currentVersion = $request->version;
        $latestVersion = $latestUpdate->version;
        // Simple version comparison (can be enhanced for semantic versioning)
        $needsUpdate = version_compare($currentVersion, $latestVersion, '<');
        $forceUpdate = $needsUpdate && $latestUpdate->force_update;
        $updateInfo = $needsUpdate
            ? [
                'version' => $latestVersion,
                'release_notes' => $latestUpdate->release_notes,
                'force_update' => $forceUpdate,
            ]
            : null;
        return response()->json([
            'message' => $needsUpdate ? 'Update available' : 'App is up to date',
            'success' => true,
            'data' => [
                'needs_update' => $needsUpdate,
                'is_authenticated' => $user ? true : false,
                'update_info' => $needsUpdate ? new AppUpdateResource($latestUpdate) : null,
                'app_in_maintenance_mode' => false,
                'maintenanceMessage' => 'The app is currently under maintenance. Please check back later.',
            ],
        ]);

        return response()->json($settings);
    }
}
