<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\BrandResource;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BrandApiController extends Controller
{
    public function index(Request $request)
    {
        $query = Brand::query();

        if ($request->has('search')) {
            $query->search($request->search);
        }

        $brands = $query->orderBy('name')->get();

        return response()->json([
            'message' => 'Brands retrieved successfully',
            'data' => BrandResource::collection($brands),
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $brand = Brand::create([
            'name' => $request->name,
            'description' => $request->description,
            'created_by' => $request->user()->id,
        ]);

        return response()->json([
            'message' => 'Brand created successfully',
            'data' => new BrandResource($brand),
        ], 201);
    }

    public function show(Brand $brand)
    {
        return response()->json([
            'message' => 'Brand retrieved successfully',
            'data' => new BrandResource($brand),
        ]);
    }

    public function update(Request $request, Brand $brand)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $brand->update([
            'name' => $request->name ?? $brand->name,
            'description' => $request->description ?? $brand->description,
            'updated_by' => $request->user()->id,
        ]);

        return response()->json([
            'message' => 'Brand updated successfully',
            'data' => new BrandResource($brand),
        ]);
    }

    public function destroy(Brand $brand)
    {
        $brand->deleted_by = request()->user()->id;
        $brand->save();
        $brand->delete();

        return response()->json([
            'message' => 'Brand deleted successfully',
        ]);
    }
}