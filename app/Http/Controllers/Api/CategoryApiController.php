<?php

namespace App\Http\Controllers\Api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\CategoryResource;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoryApiController extends Controller
{
    /**
     * Get all categories
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = Category::query();

        if ($request->has('search')) {
            $query->search($request->search);
        }

        $categories = $query->orderBy('order', 'asc')->get();
        return response()->json([
            'success' => true,
            'message' => 'Categories retrieved successfully',
            'data' => CategoryResource::collection($categories),
        ]);
    }

    /**
     * Get parent categories only
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getParentCategories()
    {
        $parentCategories = Category::whereNull('parent_id')->get();
        return response()->json([
            'success' => true,
            'message' => 'Parent categories retrieved successfully',
            'data' => CategoryResource::collection($parentCategories),
        ]);
    }

    /**
     * Get a specific category with its children
     *
     * @param Category $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Category $category)
    {
        $category->load('children');
        return response()->json([
            'success' => true,
            'message' => 'Category retrieved successfully',
            'data' => new CategoryResource($category),
        ]);
    }

    /**
     * Get children of a specific category
     *
     * @param Category $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function getChildren(Category $category)
    {
        $children = $category->children;
        return response()->json([
            'success' => true,
            'message' => 'Category children retrieved successfully',
            'data' => CategoryResource::collection($children),
        ]);
    }

    /**
     * Search categories by title
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'search' => 'required|string',
        ]);

        if ($validator->fails()) {
            $firstError = $validator->errors()->first();
            return Helpers::responseError($firstError);
        }

        $categories = Category::search($request->search)->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Search results retrieved successfully',
            'data' => CategoryResource::collection($categories),
        ]);
    }
}