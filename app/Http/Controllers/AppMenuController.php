<?php

namespace App\Http\Controllers;

use App\Models\AppMenu;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AppMenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('viewAny', AppMenu::class);

        $appMenus = AppMenu::orderBy('order')->get();
        return view('modules.app-menus.index', compact('appMenus'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', AppMenu::class);

        $parentMenus = AppMenu::orderBy('title')->get();
        return view('modules.app-menus.create', compact('parentMenus'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', AppMenu::class);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'group' => 'nullable|string|max:255',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'order' => 'nullable',
            'is_active' => 'required|string',
            'type' => 'required|in:url,text,html',
            'data_url' => 'nullable|string',
            'data_text' => 'nullable|string',
            'data_html' => 'nullable|string',
            'parent_id' => 'nullable',
        ]);

        $isActive = $request->is_active == '1' ? true : false;
        $parentId = $request->parent_id == '0' ? null : $request->parent_id;

        $data['is_active'] = $isActive;
        
        if ($request->parent_id === '0') {
            $data['parent_id'] = null;
        }

        if ($request->type == 'url') {
            $data['data'] = $request->data_url;
        } elseif ($request->type == 'text') {
            $data['data'] = $request->data_text;
        } elseif ($request->type == 'html') {
            $data['data'] = $request->data_html;
        }

        $data['title'] = $request->title;
        $data['title_ar'] = $request->title_ar;
        $data['group'] = $request->group;
        $data['order'] = $request->order;

        // Handle icon upload
        if ($request->hasFile('icon')) {
            $icon = $request->file('icon');
            $iconName = time() . '.' . $icon->getClientOriginalExtension();
            Storage::disk('public')->put('menu-icons/' . $iconName, file_get_contents($icon));
            $data['icon'] = 'menu-icons/' . $iconName;
        }

        AppMenu::create($data);

        return redirect()->route('app-menus.index')->with('success', 'تم إنشاء عنصر القائمة بنجاح.');
    }

    /**
     * Display the specified resource.
     */
    public function show(AppMenu $appMenu)
    {
        // Not needed for this resource
        return redirect()->route('app-menus.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AppMenu $appMenu)
    {
        $this->authorize('update', $appMenu);

        $parentMenus = AppMenu::where('id', '!=', $appMenu->id)
            ->orderBy('title')
            ->get();
            
        return view('modules.app-menus.update', compact('appMenu', 'parentMenus'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AppMenu $appMenu)
    {
        $this->authorize('update', $appMenu);

        $request->validate([
            'title' => 'required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'group' => 'nullable|string|max:255',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'order' => 'nullable|integer',
            'is_active' => 'required|string',
            'type' => 'required|in:url,text,html',
            'data_url' => 'nullable|string',
            'data_text' => 'nullable|string',
            'data_html' => 'nullable|string',
            'parent_id' => 'nullable',
        ]);

        $isActive = $request->is_active == '1' ? true : false;

        $data = $request->except(['is_active', 'data_url', 'data_text', 'data_html', 'icon']);
        $data['is_active'] = $isActive;
        
        if ($request->parent_id === '0') {
            $data['parent_id'] = null;
        }

        // Prevent menu item from being its own parent
        if ($request->parent_id == $appMenu->id) {
            return back()->withErrors(['parent_id' => 'لا يمكن أن يكون العنصر أبًا لنفسه.'])->withInput();
        }

        if ($request->type == 'url') {
            $data['data'] = $request->data_url;
        } elseif ($request->type == 'text') {
            $data['data'] = $request->data_text;
        } elseif ($request->type == 'html') {
            $data['data'] = $request->data_html;
        }

        // Handle icon upload
        if ($request->hasFile('icon')) {
            // Delete old icon if exists
            if ($appMenu->icon && Storage::disk('public')->exists($appMenu->icon)) {
                Storage::disk('public')->delete($appMenu->icon);
            }

            $icon = $request->file('icon');
            $iconName = time() . '.' . $icon->getClientOriginalExtension();
            Storage::disk('public')->put('menu-icons/' . $iconName, file_get_contents($icon));
            $data['icon'] = 'menu-icons/' . $iconName;
        }

        $appMenu->update($data);

        return redirect()->route('app-menus.index')->with('success', 'تم تحديث عنصر القائمة بنجاح.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AppMenu $appMenu)
    {
        $this->authorize('delete', $appMenu);

        // Delete icon file if exists
        if ($appMenu->icon && Storage::disk('public')->exists($appMenu->icon)) {
            Storage::disk('public')->delete($appMenu->icon);
        }

        // Make child items top-level items
        AppMenu::where('parent_id', $appMenu->id)
            ->update(['parent_id' => null]);

        $appMenu->delete();

        return redirect()->route('app-menus.index')->with('success', 'تم حذف عنصر القائمة بنجاح.');
    }
}
