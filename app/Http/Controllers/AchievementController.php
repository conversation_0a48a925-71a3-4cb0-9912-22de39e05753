<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreAchievementRequest;
use App\Http\Requests\UpdateAchievementRequest;
use App\Models\Achievement;
use App\Enums\AchievementType;
use Illuminate\Http\Request;

class AchievementController extends Controller
{

    public function index()
    {
        $this->authorize('viewAny', Achievement::class);

        $achievements = Achievement::withCount('users')->latest()->get();
        $analyticsData = [
            [
                'title' => 'كل الإنجازات',
                'description' => 'إجمالي عدد الإنجازات',
                'value' => Achievement::count(),
                'percent' => 100,
                'icon' => 'trophy',
                'color' => 'primary',
            ],
            [
                'title' => 'إنجازات النظام',
                'description' => 'عدد إنجازات النظام',
                'value' => Achievement::system()->count(),
                'percent' => 0,
                'icon' => 'shield-check',
                'color' => 'success',
            ],
            [
                'title' => 'الإنجازات المخصصة',
                'description' => 'عدد الإنجازات المخصصة',
                'value' => Achievement::custom()->count(),
                'percent' => 0,
                'icon' => 'edit',
                'color' => 'warning',
            ],
        ];
        $types = AchievementType::getLabelsAr();
        return view('modules.achievements.index', compact('achievements', 'analyticsData', 'types'));
    }

    public function create()
    {
        $this->authorize('create', Achievement::class);

        $types = AchievementType::getLabelsAr();
        return view('modules.achievements.create', compact('types'));
    }

    public function store(StoreAchievementRequest $request)
    {
        $this->authorize('create', Achievement::class);

        $data = $request->validated();
        if ($request->hasFile('icon')) {
            $data['icon'] = $request->file('icon')->store('achievements', 'public');
        }
        Achievement::create($data);

        return redirect()->route('achievements.index')->with('success', 'تم إنشاء الإنجاز بنجاح.');
    }

    public function show(Achievement $achievement)
    {
        $this->authorize('view', $achievement);

        return view('modules.achievements.show', compact('achievement'));
    }

    public function edit(Achievement $achievement)
    {
        $this->authorize('update', $achievement);

        $types = AchievementType::getLabelsAr();
        return view('modules.achievements.edit', compact('achievement', 'types'));
    }

    public function update(UpdateAchievementRequest $request, Achievement $achievement)
    {
        $this->authorize('update', $achievement);

        $data = $request->validated();
        if ($request->hasFile('icon')) {
            $data['icon'] = $request->file('icon')->store('achievements', 'public');
        }
        $achievement->update($data);

        return redirect()->route('achievements.index')->with('success', 'تم تحديث الإنجاز بنجاح.');
    }

    public function destroy(Achievement $achievement)
    {
        $this->authorize('delete', $achievement);

        //*check if the achievement is used in any user achievement
        if ($achievement->users()->exists()) {
            return redirect()->route('achievements.index')->with('error', 'لا يمكن حذف الإنجاز لأنه مستخدم في إنجازات المستخدمين.');
        }
        $achievement->delete();

        return redirect()->route('achievements.index')->with('success', 'تم حذف الإنجاز بنجاح.');
    }
}
