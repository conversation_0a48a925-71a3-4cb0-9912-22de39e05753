<?php

namespace App\Http\Controllers;

use App\Enums\CommissionStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Commission;
use App\Services\CommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CommissionController extends Controller
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * Display a listing of commissions.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Commission::class);

        $query = Commission::with(['user', 'product', 'admin'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        if ($request->filled('year')) {
            $query->byYear($request->year);
        }

        if ($request->filled('user_search')) {
            $query->whereHas('user', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->user_search . '%')
                  ->orWhere('email', 'like', '%' . $request->user_search . '%');
            });
        }

        if ($request->filled('product_search')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->product_search . '%');
            });
        }

        $commissions = $query->paginate(15);

        // Get filter options
        $statusOptions = CommissionStatusEnum::getLabelsAr();
        $paymentMethodOptions = PaymentMethodEnum::getLabelsAr();
        $years = Commission::selectRaw("strftime('%Y', created_at) as year")
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        return view('content.commissions.index', compact(
            'commissions',
            'statusOptions',
            'paymentMethodOptions',
            'years'
        ));
    }

    /**
     * Show the form for creating a new commission.
     */
    public function create()
    {
        $this->authorize('create', Commission::class);
        
        return redirect()->route('commissions.index');
    }

    /**
     * Store a newly created commission in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Commission::class);
        
        // This would typically be handled by user interface
        return redirect()->route('commissions.index');
    }

    /**
     * Display the specified commission.
     */
    public function show(Commission $commission)
    {
        $this->authorize('view', $commission);

        $commission->load(['user', 'product', 'admin']);

        return view('content.commissions.show', compact('commission'));
    }

    /**
     * Show the form for editing the specified commission.
     */
    public function edit(Commission $commission)
    {
        $this->authorize('update', $commission);

        return redirect()->route('commissions.show', $commission);
    }

    /**
     * Update the specified commission in storage.
     */
    public function update(Request $request, Commission $commission)
    {
        $this->authorize('update', $commission);

        $request->validate([
            'review_notes' => 'nullable|string|max:1000',
        ]);

        $commission->update([
            'review_notes' => $request->review_notes,
        ]);

        return redirect()->route('commissions.show', $commission)
            ->with('success', 'تم تحديث ملاحظات المراجعة بنجاح');
    }

    /**
     * Remove the specified commission from storage.
     */
    public function destroy(Commission $commission)
    {
        $this->authorize('delete', $commission);

        // Only allow deletion of pending commissions
        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return redirect()->route('commissions.index')
                ->with('error', 'لا يمكن حذف العمولات المراجعة');
        }

        $commission->delete();

        return redirect()->route('commissions.index')
            ->with('success', 'تم حذف العمولة بنجاح');
    }

    /**
     * Approve a commission.
     */
    public function approve(Request $request, Commission $commission)
    {
        $this->authorize('update', $commission);

        $request->validate([
            'review_notes' => 'nullable|string|max:1000',
        ]);

        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return response()->json([
                'success' => false,
                'message' => 'يمكن قبول العمولات المعلقة فقط'
            ], 400);
        }

        $this->commissionService->approveCommission(
            $commission,
            Auth::user(),
            $request->review_notes
        );

        return response()->json([
            'success' => true,
            'message' => 'تم قبول العمولة بنجاح'
        ]);
    }

    /**
     * Reject a commission.
     */
    public function reject(Request $request, Commission $commission)
    {
        $this->authorize('update', $commission);

        $request->validate([
            'review_notes' => 'required|string|max:1000',
        ]);

        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return response()->json([
                'success' => false,
                'message' => 'يمكن رفض العمولات المعلقة فقط'
            ], 400);
        }

        $this->commissionService->rejectCommission(
            $commission,
            Auth::user(),
            $request->review_notes
        );

        return response()->json([
            'success' => true,
            'message' => 'تم رفض العمولة بنجاح'
        ]);
    }
}
