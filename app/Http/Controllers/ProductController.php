<?php

namespace App\Http\Controllers;

use App\Enums\ConditionEnum;
use App\Enums\StatusEnum;
use App\Models\Product;
use App\Models\Category;
use App\Models\Location;
use App\Models\ProductMedia;
use App\Models\Tag;
use App\Jobs\ProcessProductMedia;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProductController extends Controller
{

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Product::class);

        $productsCount = Product::count();
        $productsCountLastMonth = Product::where('created_at', '>=', Carbon::now()->subMonth())->count();
        $productsCountLastMonthPercentage = $productsCountLastMonth > 0 ? number_format((100 * $productsCountLastMonth) / $productsCount) : 0;

        $activeProductsCount = Product::where('status', 'active')->count();
        $activeProductsPercentage = $activeProductsCount > 0 ? number_format((100 * $activeProductsCount) / $productsCount, 2) : 0;

        $soldProductsCount = Product::where('status', 'sold')->count();
        $soldProductsPercentage = $soldProductsCount > 0 ? number_format((100 * $soldProductsCount) / $productsCount, 2) : 0;

        $analyticsData = [
            [
                'title' => 'عدد المنتجات',
                'description' => 'عدد المنتجات الكلي',
                'value' => $productsCount,
                'percent' => 0,
                'icon' => 'shopping-cart',
                'color' => 'primary',
            ],
            [
                'title' => 'المنتجات الجديدة',
                'description' => 'عدد المنتجات الجديدة خلال الشهر الماضي',
                'value' => $productsCountLastMonth,
                'percent' => $productsCountLastMonthPercentage,
                'icon' => 'shopping-cart',
                'color' => 'success',
            ],
            [
                'title' => 'المنتجات النشطة',
                'description' => 'عدد المنتجات النشطة',
                'value' => $activeProductsCount,
                'percent' => $activeProductsPercentage,
                'icon' => 'shopping-cart',
                'color' => 'warning',
            ],
            [
                'title' => 'المنتجات المباعة',
                'description' => 'عدد المنتجات المباعة',
                'value' => $soldProductsCount,
                'percent' => $soldProductsPercentage,
                'icon' => 'shopping-cart',
                'color' => 'danger',
            ],
        ];

        return view('modules.product.index', compact('analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Product::class);

        $categories = Category::all();
        $locations = Location::all();
        $tags = Tag::all();

        $statuses = StatusEnum::getLabelsAr();
        $conditions = ConditionEnum::getLablesAr();

        return view('modules.product.create', compact('categories', 'locations', 'tags', 'statuses', 'conditions'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Product::class);

        $conditions = ConditionEnum::getLablesAr();
        $statuses = StatusEnum::getLabelsAr();
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'location_id' => 'required|exists:locations,id',
            'condition' => 'required|in:' . implode(',', array_keys($conditions)),
            'status' => 'required|in:' . implode(',', array_keys($statuses)),
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
        ]);

        DB::beginTransaction();

        try {
            $product = new Product();
            $product->title = $validated['title'];
            $product->desc = $validated['description'];
            $product->price = $validated['price'];
            $product->category_id = $validated['category_id'];
            $product->location_id = $validated['location_id'];
            $product->condition = $validated['condition'];
            $product->status = $validated['status'];
            $product->user_id = Auth::id();
            $product->save();

            if (isset($validated['tag_ids'])) {
                $product->tags()->attach($validated['tag_ids']);
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $fileOriginalName = $image->getClientOriginalName();
                    $fileSize = $image->getSize();
                    $fileMimeType = $image->getClientMimeType();
                    $fileExtension = $image->getClientOriginalExtension();
                    $fileName = time() . '_' . $index . '.' . $fileExtension;
                    
                    // Store in product-specific directory
                    $path = "products/{$product->id}/images/" . $fileName;
                    Storage::disk('products')->put($path, file_get_contents($image));
                    
                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = $fileMimeType;
                    $media->file_size = $fileSize;
                    $media->extension = $fileExtension;
                    $media->path = $path;
                    $media->order = $index;
                    $media->is_primary = $index === 0;
                    $media->product_id = $product->id;
                    $media->save();

                    // Dispatch job to process the media
                    ProcessProductMedia::dispatch($media, $path);
                }
            }

            // Handle video upload
            if ($request->hasFile('video')) {
                $video = $request->file('video');
                $fileName = time() . '.' . $video->getClientOriginalExtension();
                $path = "products/{$product->id}/videos/" . $fileName;
                
                Storage::disk('products')->put($path, file_get_contents($video));
                
                $media = new ProductMedia();
                $media->name = $fileName;
                $media->type = 'video';
                $media->mime_type = $video->getClientMimeType();
                $media->file_size = $video->getSize();
                $media->extension = $video->getClientOriginalExtension();
                $media->path = $path;
                $media->product_id = $product->id;
                $media->save();

                // Dispatch job to process the video
                ProcessProductMedia::dispatch($media, $path);
            }

            DB::commit();

            return redirect()->route('products.index')->with('success', 'Product created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to create product: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Product $product)
    {
        $this->authorize('view', $product);

        // Paginate comments and reports
        $comments = $product->comments()->with('user')->paginate(10, ['*'], 'comments_page');
        $reports = $product->reports()->with('user')->orderByDesc('viewed_at')->paginate(15, ['*'], 'reports_page');
        
        // Load other relationships
        $product->load(['user', 'category', 'location', 'tags', 'media']);
        $product->registerAnalytics();

      

        return view('modules.product.show', compact('product', 'comments', 'reports'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Product $product)
    {
        $this->authorize('update', $product);

        $categories = Category::all();
        $locations = Location::all();
        $tags = Tag::all();
        $statuses = StatusEnum::getLabelsAr();
        $conditions = ConditionEnum::getLablesAr();

        return view('modules.product.update', compact('product', 'categories', 'locations', 'tags', 'statuses', 'conditions'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Product $product)
    {
        $this->authorize('update', $product);

        $conditions = ConditionEnum::getLablesAr();
        $statuses = StatusEnum::getLabelsAr();
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'location_id' => 'required|exists:locations,id',
            'condition' => 'required|in:' . implode(',', array_keys($conditions)),
            'status' => 'required|in:' . implode(',', array_keys($statuses)),
            'tag_ids' => 'nullable|array',
            'tag_ids.*' => 'exists:tags,id',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'video' => 'nullable|file|mimes:mp4,mov,avi|max:10240',
            'delete_images' => 'nullable|array',
            'delete_images.*' => 'exists:product_media,id',
            'delete_video' => 'nullable|exists:product_media,id',
        ]);

        DB::beginTransaction();

        try {
            $product->title = $validated['title'];
            $product->desc = $validated['description'];
            $product->price = $validated['price'];
            $product->category_id = $validated['category_id'];
            $product->location_id = $validated['location_id'];
            $product->condition = $validated['condition'];
            $product->status = $validated['status'];
            $product->save();

            if (isset($validated['tag_ids'])) {
                $product->tags()->sync($validated['tag_ids']);
            } else {
                $product->tags()->detach();
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $image) {
                    $fileName = time() . '_' . $index . '.' . $image->getClientOriginalExtension();
                    $path = "{$product->id}/images/" . $fileName;
                    
                    Storage::disk('products')->put($path, file_get_contents($image));
                    

                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = $image->getClientMimeType();
                    $media->file_size = $image->getSize();
                    $media->extension = $image->getClientOriginalExtension();
                    $media->path = $path;
                    $media->order = ProductMedia::where('product_id', $product->id)
                        ->where('type', 'image')
                        ->count();
                    $media->product_id = $product->id;
                    $media->save();


                    ProcessProductMedia::dispatch($media, $path);
                }
            }

            // Handle video upload
            if ($request->hasFile('video')) {
                // Delete existing video if any
                $existingVideo = $product->media()->where('type', 'video')->first();
                if ($existingVideo) {
                    Storage::disk('products')->deleteDirectory("products/{$product->id}/videos");
                    $existingVideo->delete();
                }

                $video = $request->file('video');
                $fileName = time() . '.' . $video->getClientOriginalExtension();
                $path = "products/{$product->id}/videos/" . $fileName;
                
                Storage::disk('products')->put($path, file_get_contents($video));
                
                $media = new ProductMedia();
                $media->name = $fileName;
                $media->type = 'video';
                $media->mime_type = $video->getClientMimeType();
                $media->file_size = $video->getSize();
                $media->extension = $video->getClientOriginalExtension();
                $media->path = $path;
                $media->product_id = $product->id;
                $media->save();

                ProcessProductMedia::dispatch($media, $path);
            }

            // Delete selected images
            if (isset($validated['delete_images'])) {
                $mediaToDelete = ProductMedia::whereIn('id', $validated['delete_images'])
                    ->where('product_id', $product->id)
                    ->get();
                    
                foreach ($mediaToDelete as $media) {
                    Storage::disk('products')->delete([
                        $media->path,
                        $media->thumbnail_path,
                        $media->small_path
                    ]);
                    $media->delete();
                }
            }

            // Delete selected video
            if (isset($validated['delete_video'])) {
                $videoMedia = ProductMedia::where('id', $validated['delete_video'])
                    ->where('product_id', $product->id)
                    ->first();
                    
                if ($videoMedia) {
                    Storage::disk('products')->deleteDirectory("products/{$product->id}/videos");
                    $videoMedia->delete();
                }
            }

            DB::commit();

            return redirect()->route('products.index')->with('success', 'Product updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()
                ->back()
                ->withInput()
                ->with('error', 'Failed to update product: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Product $product)
    {
        $this->authorize('delete', $product);

        $product->tags()->detach();
        $product->delete();

        return redirect()->route('products.index')->with('success', 'Product deleted successfully.');
    }

    /**
     * Handle product image upload.
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
        ]);

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('products', $filename, 'public');

            return response()->json([
                'success' => true,
                'url' => asset('storage/' . $path),
            ]);
        }

        return response()->json(
            [
                'success' => false,
                'message' => 'No file uploaded',
            ],
            400,
        );
    }
}
