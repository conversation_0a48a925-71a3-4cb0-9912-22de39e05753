<?php

namespace App\Http\Controllers;

use App\Models\GlobalNotification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class QueueController extends Controller
{
    /**
     * Retry all failed jobs.
     */
    public function retryAllFailedJobs()
    {
        // Run the Artisan command to retry all failed jobs
        Artisan::call('queue:retry', ['id' => 'all']);
        
        return back()->with('success', 'جميع الوظائف الفاشلة تمت إعادة محاولتها بنجاح.');
    }

    /**
     * Retry a specific failed job by ID.
     *
     * @param int $id
     */
    public function retryFailedJob($id)
    {
        // Run the Artisan command to retry a specific job by ID
        Artisan::call('queue:retry', ['id' => $id]);
        
        return back()->with('success', 'تمت إعادة محاولة الوظيفة الفاشلة بنجاح.');
    }

    /**
     * List all failed jobs.
     */
    public function listFailedJobs()
    {
        // Fetch failed jobs from the 'failed_jobs' table
        $failedJobs = DB::table('failed_jobs')->get();
        
        return back()->with('success', 'تمت إعادة محاولة الوظيفة الفاشلة بنجاح.');
    }

    /**
     * Flush all failed jobs.
     */
    public function flushFailedJobs()
    {
        // Run the Artisan command to flush all failed jobs
        Artisan::call('queue:flush');
        
        return back()->with('success', 'جميع الوظائف الفاشلة تم حذفها بنجاح.');
    }
    /**
     * Flush all failed jobs.
     */
    public function cancelJob($id)
    {
        Artisan::call('queue:forget', ['id' => $id]);
        GlobalNotification::updateNotification('', '', '', 0, $id);
        return back();
    }
}