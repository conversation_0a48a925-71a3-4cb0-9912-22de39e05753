<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Laravel\Facades\Telegram;
use Illuminate\Support\Facades\Storage;
use Throwable;

class TelegramErrorBotController extends Controller
{
    protected $chatId;
    protected $botToken;
    protected $authPassword;
    protected $allowedUsersFile = 'monitorBotUsers.json';
    protected $botName = 'error_bot';

    public function __construct()
    {
        $this->chatId = config('telegram.bots.error_bot.chat_id');
        $this->botToken = config('telegram.bots.error_bot.token');
        $this->authPassword = config('telegram.bots.error_bot.password');
    }

    public function handleUpdate(Request $request)
    {
        $update = Telegram::bot($this->botName)->getWebhookUpdates();
        $message = $update->getMessage();

        if (!$message || !$message->getText()) {
            return;
        }

        $chatId = $message->getChat()->getId();
        $text = $message->getText();

        if ($text === '/start') {
            $this->sendWelcomeMessage($chatId);
            return;
        }

        if ($text === '/auth') {
            $this->sendAuthInstructions($chatId);
            return;
        }

        if (str_starts_with($text, '/auth_')) {
            $this->handleAuth($chatId, $text);
            return;
        }

        // Check if user is authenticated
        if (!$this->isUserAuthenticated($chatId)) {
            $this->sendNotAuthenticatedMessage($chatId);
            return;
        }

        // Handle other commands here if needed
    }

    protected function isUserAuthenticated($chatId)
    {
        $allowedUsers = $this->getAllowedUsers();
        return in_array($chatId, $allowedUsers);
    }

    protected function getAllowedUsers()
    {
        try {
            return json_decode(Storage::disk('public')->get($this->allowedUsersFile), true) ?? [];
        } catch (\Exception $e) {
            return [];
        }
    }

    protected function saveAllowedUsers($users)
    {
        Storage::disk('public')->put($this->allowedUsersFile, json_encode($users, JSON_PRETTY_PRINT));
    }

    protected function sendWelcomeMessage($chatId)
    {
        $message = "👋 Welcome to Haraji Error Monitor Bot!\n\n";
        $message .= "To receive error notifications, you need to authenticate first.\n";
        $message .= "Use /auth command to start the authentication process.";

        Telegram::bot($this->botName)->sendMessage([
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => 'HTML'
        ]);
    }

    protected function sendAuthInstructions($chatId)
    {
        $message = "🔐 Authentication Instructions:\n\n";
        $message .= "To authenticate, use the following command:\n";
        $message .= "/auth_[your_password]\n\n";
        $message .= "Example: /auth_123456";

        Telegram::bot($this->botName)->sendMessage([
            'chat_id' => $chatId,
            'text' => $message,
            'parse_mode' => 'HTML'
        ]);
    }

    protected function handleAuth($chatId, $text)
    {
        $password = str_replace('/auth_', '', $text);

        if ($password === $this->authPassword) {
            $allowedUsers = $this->getAllowedUsers();
            
            if (!in_array($chatId, $allowedUsers)) {
                $allowedUsers[] = $chatId;
                $this->saveAllowedUsers($allowedUsers);
            }

            Telegram::bot($this->botName)->sendMessage([
                'chat_id' => $chatId,
                'text' => "✅ Authentication successful! You will now receive error notifications.",
                'parse_mode' => 'HTML'
            ]);
        } else {
            Telegram::bot($this->botName)->sendMessage([
                'chat_id' => $chatId,
                'text' => "❌ Invalid password. Please try again.",
                'parse_mode' => 'HTML'
            ]);
        }
    }

    protected function sendNotAuthenticatedMessage($chatId)
    {
        Telegram::bot($this->botName)->sendMessage([
            'chat_id' => $chatId,
            'text' => "⚠️ You are not authenticated to receive error notifications.\nUse /auth command to start the authentication process.",
            'parse_mode' => 'HTML'
        ]);
    }

    public function sendError(Throwable $exception)
    {
        try {
            $message = $this->formatErrorMessage($exception);
            $allowedUsers = $this->getAllowedUsers();
            
            foreach ($allowedUsers as $userId) {
                Telegram::bot($this->botName)->sendMessage([
                    'chat_id' => $userId,
                    'text' => $message,
                    'parse_mode' => 'HTML'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send error to Telegram: ' . $e->getMessage());
        }
    }

    protected function formatErrorMessage(Throwable $exception)
    {
        $message = "<b>🚨 Error Alert</b>\n\n";
        $message .= "<b>Error Type:</b> " . get_class($exception) . "\n";
        $message .= "<b>Message:</b> " . $exception->getMessage() . "\n";
        $message .= "<b>File:</b> " . $exception->getFile() . "\n";
        $message .= "<b>Line:</b> " . $exception->getLine() . "\n";
        $message .= "<b>URL:</b> " . request()->fullUrl() . "\n";
        $message .= "<b>Method:</b> " . request()->method() . "\n";
        $message .= "<b>User Agent:</b> " . request()->userAgent() . "\n";
        $message .= "<b>IP:</b> " . request()->ip() . "\n\n";

        return $message;
    }

    public function sendApiError($error, $endpoint, $method = 'GET')
    {
        try {
            $message = "<b>🚨 API Error Alert</b>\n\n";
            $message .= "<b>Endpoint:</b> " . $endpoint . "\n";
            $message .= "<b>Method:</b> " . $method . "\n";
            $message .= "<b>Error:</b> " . $error . "\n";
            $message .= "<b>Time:</b> " . now()->format('Y-m-d H:i:s') . "\n";
            $message .= "<b>IP:</b> " . request()->ip() . "\n";

            $allowedUsers = $this->getAllowedUsers();
            foreach ($allowedUsers as $userId) {
                Telegram::bot($this->botName)->sendMessage([
                    'chat_id' => $userId,
                    'text' => $message,
                    'parse_mode' => 'HTML'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send API error to Telegram: ' . $e->getMessage());
        }
    }

    public function sendPageError($error, $page)
    {
        try {
            $message = "<b>🚨 Page Error Alert</b>\n\n";
            $message .= "<b>Page:</b> " . $page . "\n";
            $message .= "<b>Error:</b> " . $error . "\n";
            $message .= "<b>Time:</b> " . now()->format('Y-m-d H:i:s') . "\n";
            $message .= "<b>URL:</b> " . request()->fullUrl() . "\n";
            $message .= "<b>IP:</b> " . request()->ip() . "\n";

            $allowedUsers = $this->getAllowedUsers();
            foreach ($allowedUsers as $userId) {
                Telegram::bot($this->botName)->sendMessage([
                    'chat_id' => $userId,
                    'text' => $message,
                    'parse_mode' => 'HTML'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to send page error to Telegram: ' . $e->getMessage());
        }
    }
} 