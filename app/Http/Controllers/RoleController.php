<?php

namespace App\Http\Controllers;

use App\RolesEnum;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleController extends Controller
{
    // Display a listing of the roles
    public function index()
    {
        $this->authorize('viewAny', 'App\Models\Role');
        $roles = Role::withCount('permissions')->withCount('users')->get();
        return view('modules.roles.index', compact('roles'));
    }

    // Show the form for creating a new role
    public function create()
    {
        $this->authorize('create', 'App\Models\Role');
        $permissions = Permission::all();
        return view('roles.create', compact('permissions'));
    }

    // Store a newly created role in storage
    public function store(Request $request)
    {
        $this->authorize('create', 'App\Models\Role');
        $request->validate([
            'name' => 'required|unique:roles,name',
            'permissions' => 'required|array',
        ]);

        $role = Role::create(['name' => $request->name]);
        $role->syncPermissions($request->permissions);

        return redirect()->route('modules.roles.index')->with('success', 'تم إنشاء الدور بنجاح.');
    }

    // Display the specified role
    public function show($id)
    {
        $this->authorize('view', 'App\Models\Role');
        $role = Role::findOrFail($id);
        return view('modules.roles.show', compact('role'));
    }

    // Show the form for editing the specified role
    public function edit($id)
    {
        $this->authorize('update', 'App\Models\Role');
        $role = Role::findOrFail($id);
        $rolePermissions = Permission::all();
        $modules = RolesEnum::modules();
        return view('modules.roles.update', compact('role', 'rolePermissions', 'modules'));
    }

    // Update the specified role in storage
    public function update(Request $request, $id)
    {
        $this->authorize('update', 'App\Models\Role');
        $request->validate([
            'permissions' => 'required|array',
        ]);

        $role = Role::findOrFail($id);

        $permissions = [];
        foreach ($request->permissions as $key => $permission) {
            foreach ($permission as $ke => $value) {
                $permissions[] = $key . '_' . $ke;
            }
        }
        $role->syncPermissions($permissions);

        $roles = Role::withCount('permissions')->withCount('users')->get();
        return view('modules.roles.index', compact('roles'))->with('success', 'تم تحديث الدور بنجاح.');
    }

    // Remove the specified role from storage
    public function destroy($id)
    {
        $this->authorize('delete', 'App\Models\Role');
        $role = Role::findOrFail($id);
        $role->delete();

        return redirect()->route('modules.roles.index')->with('success', 'تم حذف الدور بنجاح.');
    }
}
