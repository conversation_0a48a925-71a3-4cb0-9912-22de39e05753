<?php

namespace App\Http\Controllers;

use App\Models\AppNotification;
use App\Http\Requests\StoreAppNotificationRequest;
use App\Http\Requests\UpdateAppNotificationRequest;
use App\Jobs\SendAppNotificationToUsersJob;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\ImageManager;

class AppNotificationController extends Controller
{

    public function index()
    {
        $this->authorize('viewAny', AppNotification::class);

        $items = AppNotification::all();
        return view('modules.app-notifications.index', compact('items'));
    }

    public function create()
    {
        $this->authorize('create', AppNotification::class);

        return redirect()->route('app-notifications.index');
    }

    public function store(StoreAppNotificationRequest $request)
    {
        $this->authorize('create', AppNotification::class);

        $data = $request->validated();
        
        if ($request->hasFile('image')) {
            $manager = new ImageManager(Driver::class);
            $image = $manager->read($request->file('image'));
            $image->resize(512, 512);
            $generateImageName = time() . '.' . $request->file('image')->getClientOriginalExtension();
            
            if (!file_exists(public_path('storage/notifications'))) {
                mkdir(public_path('storage/notifications'), 0777, true);
            }
            $data['image'] = $image->save('storage/notifications/' . $generateImageName);
        }

        $notification = AppNotification::create($data);

        // Dispatch the job to send notifications to all users
        SendAppNotificationToUsersJob::dispatch($notification);

        return redirect()->route('app-notifications.index')->with('success', 'تم إنشاء الإشعار بنجاح.');
    }

    public function show(AppNotification $appNotification)
    {
        $this->authorize('view', $appNotification);

        return view('modules.app-notifications.show', compact('appNotification'));
    }

    public function edit(AppNotification $appNotification)
    {
        $this->authorize('update', $appNotification);

        return view('modules.app-notifications.edit', compact('appNotification'));
    }

    public function update(UpdateAppNotificationRequest $request, AppNotification $appNotification)
    {
        $this->authorize('update', $appNotification);

        $appNotification->update($request->validated());
        return redirect()->route('app-notifications.index')->with('success', 'تم تحديث الإشعار بنجاح.');
    }

    public function destroy(AppNotification $appNotification)
    {
        $this->authorize('delete', $appNotification);

        $appNotification->delete();
        return redirect()->route('app-notifications.index')->with('success', 'تم حذف الإشعار بنجاح.');
    }
}
