<?php

namespace App\Http\Controllers;

use App\Models\AppNotification;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;

class FirebaseController extends Controller
{
    public function send($title, $body, $related_id, $related_type, $fcms): void
    {
        $firebase = (new Factory())->withServiceAccount(__DIR__ . '/../../../config/firebase_credentials.json');

        $messaging = $firebase->createMessaging();

        if ($fcms == null) {
            $message = CloudMessage::fromArray([
                'topic' => 'harajy',
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                ],
                'data' => [
                    'related_id' => $related_id,
                    'related_type' => $related_type,
                ],
            ]);

            $sendReport = $messaging->send($message);
        } else {
            $message = CloudMessage::fromArray([
                'notification' => [
                    'title' => $title,
                    'body' => $body,
                ],
                'data' => [
                    'related_id' => $related_id,
                    'related_type' => $related_type,
                ],
            ]);
            $sendReport = $messaging->sendMulticast($message, $fcms);
        }
        Log::info('Notification sent successfully', [
            'sendReport' => $sendReport,
            'related_id' => $related_id,
            'related_type' => $related_type,
        ]);
    }
}
