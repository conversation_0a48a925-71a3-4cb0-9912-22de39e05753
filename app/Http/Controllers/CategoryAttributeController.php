<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CategoryAttribute;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CategoryAttributeController extends Controller
{
    /**
     * Display a listing of the attributes for a category.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function index(Category $category)
    {
        $this->authorize('viewAny', CategoryAttribute::class);

        $attributes = $category->attributes()->orderBy('order')->get();
        return view('modules.category-attributes.index', compact('category', 'attributes'));
    }

    /**
     * Show the form for creating a new attribute.
     *
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function create(Category $category)
    {
        $this->authorize('create', CategoryAttribute::class);

        return view('modules.category-attributes.create', compact('category'));
    }

    /**
     * Store a newly created attribute in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, Category $category)
    {
        $this->authorize('create', CategoryAttribute::class);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:text,select,number,checkbox,radio,date,textarea',
            'options' => 'nullable|array',
            'options.*' => 'string|max:255',
            'is_required' => 'string',
            'is_filterable' => 'string',
            'order' => 'integer',
            'placeholder' => 'nullable|string|max:255',
            'help_text' => 'nullable|string|max:255',
        ]);

        $isRequired = $request->is_required == 'on' ? true : false;
        $isFilterable = $request->is_filterable == 'on' ? true : false;

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $options = null;
        if ($request->has('options') && is_array($request->options)) {
            $options = array_filter($request->options, function($option) {
                return !empty($option);
            });
        }

        $attribute = new CategoryAttribute([
            'name' => $request->name,
            'type' => $request->type,
            'options' => $options,
            'is_required' => $isRequired,
            'is_filterable' => $isFilterable,
            'order' => $request->order ?? 0,
            'placeholder' => $request->placeholder,
            'help_text' => $request->help_text,
        ]);

        
        $category->attributes()->save($attribute);

        return redirect()->route('categories.attributes.index', $category->id)
            ->with('success', 'Attribute created successfully');
    }

    /**
     * Show the form for editing the specified attribute.
     *
     * @param  \App\Models\Category  $category
     * @param  \App\Models\CategoryAttribute  $attribute
     * @return \Illuminate\Http\Response
     */
    public function edit(Category $category, CategoryAttribute $attribute)
    {
        $this->authorize('update', $attribute);

        if (request()->ajax()) {
            return response()->json([
                'id' => $attribute->id,
                'name' => $attribute->name,
                'type' => $attribute->type,
                'options' => $attribute->options,
                'is_required' => $attribute->is_required,
                'is_filterable' => $attribute->is_filterable,
                'order' => $attribute->order,
                'placeholder' => $attribute->placeholder,
                'help_text' => $attribute->help_text,
            ]);
        }
        
        return view('modules.category-attributes.edit', compact('category', 'attribute'));
    }

    /**
     * Update the specified attribute in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Category  $category
     * @param  \App\Models\CategoryAttribute  $attribute
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Category $category, CategoryAttribute $attribute)
    {
        $this->authorize('update', $attribute);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:text,select,number,checkbox,radio,date,textarea',
            'options' => 'nullable|array',
            'options.*' => 'string|max:255',
            'is_required' => 'string',
            'is_filterable' => 'string',
            'order' => 'integer',
            'placeholder' => 'nullable|string|max:255',
            'help_text' => 'nullable|string|max:255',
        ]);

        $isRequired = $request->is_required == 'on' ? true : false;
        $isFilterable = $request->is_filterable == 'on' ? true : false;

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $options = null;
        if ($request->has('options') && is_array($request->options)) {
            $options = array_filter($request->options, function($option) {
                return !empty($option);
            });
        }

        $attribute->name = $request->name;
        $attribute->type = $request->type;
        $attribute->options = $options;
        $attribute->is_required = $isRequired;
        $attribute->is_filterable = $isFilterable;
        $attribute->order = $request->order ?? 0;
        $attribute->placeholder = $request->placeholder;
        $attribute->help_text = $request->help_text;
        $attribute->updated_by = Auth::id();
        $attribute->save();

        return redirect()->route('categories.attributes.index', $category->id)
            ->with('success', 'Attribute updated successfully');
    }

    /**
     * Remove the specified attribute from storage.
     *
     * @param  \App\Models\Category  $category
     * @param  \App\Models\CategoryAttribute  $attribute
     * @return \Illuminate\Http\Response
     */
    public function destroy(Category $category, CategoryAttribute $attribute)
    {
        $attribute->deleted_by = Auth::id();
        $attribute->save();
        $attribute->delete();

        return redirect()->route('categories.attributes.index', $category->id)
            ->with('success', 'Attribute deleted successfully');
    }

    /**
     * Update the order of attributes.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Category  $category
     * @return \Illuminate\Http\Response
     */
    public function updateOrder(Request $request, Category $category)
    {
        $validator = Validator::make($request->all(), [
            'attributes' => 'required|array',
            'attributes.*.id' => 'required|exists:category_attributes,id',
            'attributes.*.order' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        foreach ($request->attributes as $attributeData) {
            $attribute = CategoryAttribute::find($attributeData['id']);
            if ($attribute && $attribute->category_id == $category->id) {
                $attribute->order = $attributeData['order'];
                $attribute->updated_by = Auth::id();
                $attribute->save();
            }
        }

        return response()->json(['success' => true]);
    }
} 