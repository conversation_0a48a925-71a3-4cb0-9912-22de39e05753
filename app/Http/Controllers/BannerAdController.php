<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Http\Requests\StoreBannerAdRequest;
use App\Http\Requests\UpdateBannerAdRequest;
use App\Http\Resources\BannerAdResource;
use App\Models\BannerAd;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class BannerAdController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('viewAny', BannerAd::class);

        $bannerAds = BannerAd::orderBy('order')->paginate(10);
        return view('modules.banner-ads.index', compact('bannerAds'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', BannerAd::class);
        
        return view('modules.banner-ads.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBannerAdRequest $request)
    {
        $this->authorize('create', BannerAd::class);

        $data = $request->validated();
        $data['created_by'] = Auth::id();

        // Handle image upload
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            Storage::disk('banners')->put($imageName, file_get_contents($image));
            $data['image'] = 'banners/' . $imageName;
        }

        $bannerAd = BannerAd::create($data);
        return redirect()->route('banner-ads.index')->with('success', 'تم إنشاء الإعلان بنجاح');
    }

    /**
     * Display the specified resource.
     */
    public function show(BannerAd $bannerAd)
    {
        $this->authorize('view', $bannerAd);
        
        return view('modules.banner-ads.show', compact('bannerAd'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(BannerAd $bannerAd)
    {
        $this->authorize('update', $bannerAd);
        
        return view('modules.banner-ads.edit', compact('bannerAd'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateBannerAdRequest $request, BannerAd $bannerAd)
    {
        $this->authorize('update', $bannerAd);

        $data = $request->validated();
        $data['updated_by'] = Auth::id();

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($bannerAd->image && Storage::exists($bannerAd->image)) {
                Storage::delete($bannerAd->image);
            }

            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            Storage::disk('banners')->put($imageName, file_get_contents($image));
            $data['image'] = 'banners/' . $imageName;
        }

        $bannerAd->update($data);
        return redirect()->route('banner-ads.index')->with('success', 'تم تحديث الإعلان بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BannerAd $bannerAd)
    {
        $this->authorize('delete', $bannerAd);

        // Delete the image if exists
        if ($bannerAd->image && Storage::exists($bannerAd->image)) {
            Storage::delete($bannerAd->image);
        }

        $bannerAd->deleted_by = Auth::id();
        $bannerAd->save();
        $bannerAd->delete();

        Helpers::notifyAdmins([
            'title' => 'تم حذف إعلان',
            'message' => 'الإعلان ' . $bannerAd->title . ' تم حذفه بواسطة ' . Auth::user()->name,
            'href' => route('banner-ads.index'),
        ]);

        return redirect()->route('banner-ads.index')->with('success', 'تم حذف الإعلان بنجاح');
    }

    /**
     * Get banner ads for API
     */
    public function getBannerAdsForScreen(Request $request, $screen)
    {
        $platform = $request->get('platform', 'app');
        
        $bannerAds = BannerAd::active()
            ->platform($platform)
            ->screen($screen)
            ->orderBy('order')
            ->get();
            
        return BannerAdResource::collection($bannerAds);
    }
} 