<?php

namespace App\Http\Controllers\pages;

use App\Http\Controllers\Controller;
use App\Models\BannerAd;
use App\Models\Product;
use App\Models\Report;
use App\Models\User;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class HomeController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }
    public function index(Request $request)
    {
        // $data = Cache::remember('dashboard_data', 300, function () {
        //     $dashboardData = $this->dashboardService->getDashboardData();

        //     // Add additional reports
        //     $dashboardData['productReports'] = $this->getProductReports();
        //     $dashboardData['userReports'] = $this->getUserReports();
        //     $dashboardData['reportStats'] = $this->getReportStats();
        //     $dashboardData['chatAnalytics'] = $this->getChatAnalytics();
        //     $dashboardData['adPerformance'] = $this->getAdPerformance();

        //     return $dashboardData;
        // });
            $data = $this->dashboardService->getDashboardData();

            // Add additional reports
            $data['productReports'] = $this->getProductReports();
            $data['userReports'] = $this->getUserReports();
            $data['reportStats'] = $this->getReportStats();
            $data['chatAnalytics'] = $this->getChatAnalytics();
            $data['adPerformance'] = $this->getAdPerformance();


        return view('content.pages.dashboard', compact('data'));
    }

    /**
     * Get product report statistics
     */
    private function getProductReports()
    {
        $totalProducts = Product::count();
        $activeProducts = Product::where('status', 'active')->count();
        $soldProducts = Product::where('status', 'sold')->count();
        $reportedProducts = Product::whereHas('analytics')->count();

        return [
            'total' => $totalProducts,
            'active' => $activeProducts,
            'sold' => $soldProducts,
            'reported' => $reportedProducts,
            'activePercent' => $totalProducts > 0 ? round(($activeProducts / $totalProducts) * 100, 2) : 0,
            'soldPercent' => $totalProducts > 0 ? round(($soldProducts / $totalProducts) * 100, 2) : 0,
            'reportedPercent' => $totalProducts > 0 ? round(($reportedProducts / $totalProducts) * 100, 2) : 0,
        ];
    }

    /**
     * Get user report statistics
     */
    private function getUserReports()
    {
        $totalUsers = User::count();
        $activeUsers = User::where('last_activity', '>=', now()->subDays(7))->count();
        $newUsers = User::where('created_at', '>=', now()->subDays(30))->count();

        return [
            'total' => $totalUsers,
            'active' => $activeUsers,
            'new' => $newUsers,
            'activePercent' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 2) : 0,
            'newPercent' => $totalUsers > 0 ? round(($newUsers / $totalUsers) * 100, 2) : 0,
        ];
    }

    /**
     * Get report statistics
     */
    private function getReportStats()
    {
        $totalReports = Report::count();
        $pendingReports = Report::where('status', 'pending')->count();
        $reviewedReports = Report::where('status', 'reviewed')->count();

        return [
            'total' => $totalReports,
            'pending' => $pendingReports,
            'reviewed' => $reviewedReports,
            'pendingPercent' => $totalReports > 0 ? round(($pendingReports / $totalReports) * 100, 2) : 0,
            'reviewedPercent' => $totalReports > 0 ? round(($reviewedReports / $totalReports) * 100, 2) : 0,
        ];
    }

    /**
     * Get chat analytics
     */
    private function getChatAnalytics()
    {
        // Assuming you have a ChatMessage model
        $totalMessages = DB::table('chat_messages')->count();
        $messagesLastWeek = DB::table('chat_messages')
            ->where('created_at', '>=', now()->subDays(7))
            ->count();
        $activeChats = DB::table('chats')
            ->where('updated_at', '>=', now()->subDays(7))
            ->count();

        return [
            'totalMessages' => $totalMessages,
            'messagesLastWeek' => $messagesLastWeek,
            'activeChats' => $activeChats,
            'weeklyGrowth' => $totalMessages > 0 ? round(($messagesLastWeek / $totalMessages) * 100, 2) : 0,
        ];
    }

    /**
     * Get ad performance metrics
     */
    private function getAdPerformance()
    {
        $totalAds = BannerAd::count();
        $activeAds = BannerAd::active()->count();

        // Check if tables exist before querying
        $impressions = 0;
        $clicks = 0;

        try {
            if (Schema::hasTable('banner_ad_impressions')) {
                $impressions = DB::table('banner_ad_impressions')
                    ->where('created_at', '>=', now()->subDays(30))
                    ->count();
            }

            if (Schema::hasTable('banner_ad_clicks')) {
                $clicks = DB::table('banner_ad_clicks')
                    ->where('created_at', '>=', now()->subDays(30))
                    ->count();
            }
        } catch (\Exception $e) {
            // Log the error but continue
            Log::error('Error fetching banner ad metrics: ' . $e->getMessage());
        }

        return [
            'total' => $totalAds,
            'active' => $activeAds,
            'impressions' => $impressions,
            'clicks' => $clicks,
            'ctr' => $impressions > 0 ? round(($clicks / $impressions) * 100, 2) : 0,
            'activePercent' => $totalAds > 0 ? round(($activeAds / $totalAds) * 100, 2) : 0,
        ];
    }
}
