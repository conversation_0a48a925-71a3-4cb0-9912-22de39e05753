<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\AppNotification;
use App\Models\AppUserNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Display a listing of the user's notifications.
     */
    public function index()
    {
        $user = Auth::user();
        
        // Get user notifications with pagination
        $notifications = $user->userNotifications()
            ->with('appUserNotifications')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Add read status to each notification
        $notifications->getCollection()->transform(function ($notification) use ($user) {
            $userNotification = $notification->appUserNotifications
                ->where('user_id', $user->id)
                ->first();
            
            $notification->is_read = $userNotification && $userNotification->read_at !== null;
            $notification->read_at = $userNotification ? $userNotification->read_at : null;
            
            return $notification;
        });

        return view('web.notifications.index', compact('notifications'));
    }

    /**
     * Mark a notification as read.
     */
    public function markAsRead(AppNotification $notification)
    {
        $user = Auth::user();
        
        // Find or create the user notification record
        $userNotification = AppUserNotification::where('app_notification_id', $notification->id)
            ->where('user_id', $user->id)
            ->first();

        if ($userNotification && !$userNotification->read_at) {
            $userNotification->update(['read_at' => now()]);
        }

        return redirect()->back()->with('success', __('تم تحديد الإشعار كمقروء'));
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead()
    {
        $user = Auth::user();
        
        AppUserNotification::where('user_id', $user->id)
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

        return redirect()->back()->with('success', __('تم تحديد جميع الإشعارات كمقروءة'));
    }

    /**
     * Delete a notification for the user.
     */
    public function destroy(AppNotification $notification)
    {
        $user = Auth::user();
        
        // Remove the user notification record (soft delete the relationship)
        AppUserNotification::where('app_notification_id', $notification->id)
            ->where('user_id', $user->id)
            ->delete();

        return redirect()->back()->with('success', __('تم حذف الإشعار'));
    }

    /**
     * Show a specific notification and mark it as read.
     */
    public function show(AppNotification $notification)
    {
        $user = Auth::user();
        
        // Check if user has access to this notification
        $userNotification = AppUserNotification::where('app_notification_id', $notification->id)
            ->where('user_id', $user->id)
            ->first();

        if (!$userNotification) {
            abort(404);
        }

        // Mark as read if not already read
        if (!$userNotification->read_at) {
            $userNotification->update(['read_at' => now()]);
        }

        $notification->is_read = true;
        $notification->read_at = $userNotification->read_at;

        return view('web.notifications.show', compact('notification'));
    }
}
