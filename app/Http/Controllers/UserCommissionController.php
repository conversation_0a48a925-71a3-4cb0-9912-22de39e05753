<?php

namespace App\Http\Controllers;

use App\Enums\CommissionStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Commission;
use App\Models\Product;
use App\Services\CommissionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserCommissionController extends Controller
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
        $this->middleware('auth');
    }

    /**
     * Display user's commissions.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        $query = Commission::where('user_id', $user->id)
            ->with(['product', 'admin'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('year')) {
            $query->byYear($request->year);
        }

        $commissions = $query->paginate(10);

        // Get filter options
        $statusOptions = CommissionStatusEnum::getLabelsAr();
        $years = Commission::where('user_id', $user->id)
            ->selectRaw('YEAR(created_at) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year');

        // Get user's achievements
        $achievements = $user->achievements()
            ->where('role', 'seller')
            ->orderBy('user_achievements.achieved_at', 'desc')
            ->get();

        return view('content.user.commissions.index', compact(
            'commissions',
            'statusOptions',
            'years',
            'achievements'
        ));
    }

    /**
     * Show commission creation form for a product.
     */
    public function create(Product $product)
    {
        $user = Auth::user();

        // Check if user owns the product
        if ($product->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بدفع عمولة لهذا المنتج');
        }

        // Check if commission already exists for this product
        $existingCommission = Commission::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first();

        if ($existingCommission) {
            return redirect()->route('user.commissions.show', $existingCommission)
                ->with('info', 'يوجد بالفعل طلب عمولة لهذا المنتج');
        }

        $commissionAmount = Commission::calculateCommission($product);
        $paymentMethods = PaymentMethodEnum::getLabelsAr();

        return view('content.user.commissions.create', compact(
            'product',
            'commissionAmount',
            'paymentMethods'
        ));
    }

    /**
     * Store a new commission.
     */
    public function store(Request $request, Product $product)
    {
        $user = Auth::user();

        // Check if user owns the product
        if ($product->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بدفع عمولة لهذا المنتج');
        }

        // Check if commission already exists
        $existingCommission = Commission::where('user_id', $user->id)
            ->where('product_id', $product->id)
            ->first();

        if ($existingCommission) {
            return redirect()->route('user.commissions.show', $existingCommission)
                ->with('error', 'يوجد بالفعل طلب عمولة لهذا المنتج');
        }

        $request->validate([
            'payment_method' => 'required|in:' . implode(',', PaymentMethodEnum::getValues()),
            'payment_proof' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string|max:500',
        ]);

        $paymentMethod = PaymentMethodEnum::from($request->payment_method);

        // Validate payment proof for methods that require it
        if ($paymentMethod->requiresProof($paymentMethod->value) && !$request->hasFile('payment_proof')) {
            return back()->withErrors([
                'payment_proof' => 'إثبات الدفع مطلوب لهذه الطريقة'
            ])->withInput();
        }

        $commission = $this->commissionService->createCommission(
            $user,
            $product,
            $paymentMethod,
            $request->file('payment_proof'),
            $request->notes
        );

        return redirect()->route('user.commissions.show', $commission)
            ->with('success', 'تم تقديم طلب دفع العمولة بنجاح');
    }

    /**
     * Display a specific commission.
     */
    public function show(Commission $commission)
    {
        $user = Auth::user();

        // Check if user owns this commission
        if ($commission->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بعرض هذه العمولة');
        }

        $commission->load(['product', 'admin']);

        return view('content.user.commissions.show', compact('commission'));
    }

    /**
     * Show edit form for pending commission.
     */
    public function edit(Commission $commission)
    {
        $user = Auth::user();

        // Check if user owns this commission
        if ($commission->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بتعديل هذه العمولة');
        }

        // Only allow editing pending commissions
        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return redirect()->route('user.commissions.show', $commission)
                ->with('error', 'لا يمكن تعديل العمولات المراجعة');
        }

        $paymentMethods = PaymentMethodEnum::getLabelsAr();

        return view('content.user.commissions.edit', compact('commission', 'paymentMethods'));
    }

    /**
     * Update a pending commission.
     */
    public function update(Request $request, Commission $commission)
    {
        $user = Auth::user();

        // Check if user owns this commission
        if ($commission->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بتعديل هذه العمولة');
        }

        // Only allow editing pending commissions
        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return redirect()->route('user.commissions.show', $commission)
                ->with('error', 'لا يمكن تعديل العمولات المراجعة');
        }

        $request->validate([
            'payment_method' => 'required|in:' . implode(',', PaymentMethodEnum::getValues()),
            'payment_proof' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string|max:500',
        ]);

        $paymentMethod = PaymentMethodEnum::from($request->payment_method);

        // Handle payment proof upload
        $paymentProof = $commission->payment_proof;
        if ($request->hasFile('payment_proof')) {
            $paymentProof = $this->storePaymentProof($request->file('payment_proof'), $user->id);
        }

        $commission->update([
            'payment_method' => $paymentMethod,
            'payment_proof' => $paymentProof,
            'notes' => $request->notes,
        ]);

        return redirect()->route('user.commissions.show', $commission)
            ->with('success', 'تم تحديث طلب العمولة بنجاح');
    }

    /**
     * Delete a pending commission.
     */
    public function destroy(Commission $commission)
    {
        $user = Auth::user();

        // Check if user owns this commission
        if ($commission->user_id !== $user->id) {
            abort(403, 'غير مصرح لك بحذف هذه العمولة');
        }

        // Only allow deleting pending commissions
        if ($commission->status !== CommissionStatusEnum::PENDING) {
            return redirect()->route('user.commissions.index')
                ->with('error', 'لا يمكن حذف العمولات المراجعة');
        }

        $commission->delete();

        return redirect()->route('user.commissions.index')
            ->with('success', 'تم حذف طلب العمولة بنجاح');
    }

    /**
     * Store payment proof file.
     */
    private function storePaymentProof($file, $userId)
    {
        $filename = time() . '_' . $userId . '_' . $file->getClientOriginalName();
        $file->storeAs('commissions', $filename, 'public');
        return $filename;
    }
}
