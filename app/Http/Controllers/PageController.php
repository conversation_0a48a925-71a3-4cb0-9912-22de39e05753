<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{

    public function index()
    {
        $this->authorize('viewAny', Page::class);

        $pages = Page::all();
        return view('modules.pages.index', compact('pages'));
    }

    public function create()
    {
        $this->authorize('create', Page::class);

        return view('modules.pages.create');
    }

    public function store(Request $request)
    {
        $this->authorize('create', Page::class);

        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'status' => 'required|string',
            'page_type' => 'required|string',
        ]);

        $isStatus = $request->status == '1' ? true : false;

        $data = $request->except(['status']);
        $data['status'] = $isStatus;
        $data['page_type'] = $request->page_type;

        Page::create($data);

        return redirect()->route('pages.index')->with('success', 'تم إنشاء الصفحة بنجاح.');
    }

    public function show(Page $page)
    {
        return view('modules.pages.show', compact('page'));
    }
    public function open($slug)
    {
        $page = Page::where('slug', $slug)->first();
        return view('modules.pages.page', compact('page'));
    }

    public function edit(Page $page)
    {
        $this->authorize('update', $page);

        return view('modules.pages.update', compact('page'));
    }

    public function update(Request $request, Page $page)
    {
        $this->authorize('update', $page);

        $request->validate([
            'title' => 'required',
            'content' => 'required',
            'status' => 'required|string',
            'page_type' => 'required|string',
        ]);

        $data = $request->except(['status']);
        $isStatus = $request->status == '1' ? true : false;

        $data['status'] = $isStatus;

        $page->update($data);

        return redirect()->route('pages.index')->with('success', 'تم تحديث الصفحة بنجاح.');
    }

    public function destroy(Page $page)
    {
        $this->authorize('delete', $page);

        $page->delete();

        return redirect()->route('pages.index')->with('success', 'تم حذف الصفحة بنجاح.');
    }
}
