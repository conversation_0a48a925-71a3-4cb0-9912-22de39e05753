<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\Product;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BrandController extends Controller
{

    public function index(Request $request)
    {
        $this->authorize('viewAny', Brand::class);

        $perPage = $request->has('itemsPerPage') ? $request->itemsPerPage : 12;
        $brands = Brand::where(function ($query) use ($request) {
            if ($request->id != null) {
                $query->where('id', $request->id);
            }
            if ($request->search != null) {
                $query->where('name', 'LIKE', '%' . $request->search . '%')->orWhere('slug', 'LIKE', '%' . $request->search . '%');
            }
            if ($request->datefilter != null) {
                try {
                    $startDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[0])->format('Y-m-d');
                    $endDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[1])->format('Y-m-d');
                    $query->whereDate('created_at', '>=', $startDate)->whereDate('created_at', '<=', $endDate);
                } catch (\Exception $e) {
                }
            }
        })
            ->withCount(['product', 'categories'])
            ->orderBy('created_at', 'DESC')
            ->paginate($perPage);

        $brandsCount = Brand::count();
        $brandsCountLastMonth = Brand::where('created_at', '>=', Carbon::now()->subMonth())->count();
        $brandsCountLastMonthPercentage = $brandsCountLastMonth > 0 ? number_format((100 * $brandsCountLastMonth) / $brandsCount) : 0;

        $productsCount = Product::count();
        $productsWithBrandsCount = Product::whereHas('brand')->count();
        $productsWithBrandsPercentage = $productsCount > 0 ? number_format((100 * $productsWithBrandsCount) / $productsCount, 2) : 0;

        $categoriesCount = Category::count();
        $categoriesWithBrandsCount = Category::whereHas('brands')->count();
        $categoriesWithBrandsPercentage = $categoriesCount > 0 ? number_format((100 * $categoriesWithBrandsCount) / $categoriesCount, 2) : 0;

        $analyticsData = [
            [
                'title' => 'عدد العلامات التجارية',
                'description' => 'عدد العلامات التجارية الكلي',
                'value' => $brandsCount,
                'percent' => 0,
                'icon' => 'briefcase',
                'color' => 'primary',
            ],
            [
                'title' => 'العلامات التجارية الجديدة',
                'description' => 'عدد العلامات التجارية الجديدة خلال الشهر الماضي',
                'value' => $brandsCountLastMonth,
                'percent' => $brandsCountLastMonthPercentage,
                'icon' => 'briefcase',
                'color' => 'success',
            ],
            [
                'title' => 'المنتجات مع علامات تجارية',
                'description' => 'عدد المنتجات التي لها علامات تجارية',
                'value' => $productsWithBrandsCount,
                'percent' => $productsWithBrandsPercentage,
                'icon' => 'shopping-cart',
                'color' => 'warning',
            ],
            [
                'title' => 'التصنيفات مع علامات تجارية',
                'description' => 'عدد التصنيفات التي لها علامات تجارية',
                'value' => $categoriesWithBrandsCount,
                'percent' => $categoriesWithBrandsPercentage,
                'icon' => 'menu',
                'color' => 'danger',
            ],
        ];

        return view('modules.brand.index', compact('brands', 'analyticsData'));
    }

    public function create()
    {
        $this->authorize('create', Brand::class);

        return view('modules.brand.create');
    }

    public function store(Request $request)
    {
        $this->authorize('create', Brand::class);
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:brands',
            'description' => 'nullable|string',
        ]);

        $brand = new Brand();
        $brand->name = $validated['name'];
        $brand->slug = Str::slug($validated['name']);
        $brand->description = $validated['description'];
        $brand->save();

        return redirect()->route('brands.index')->with('success', 'Brand created successfully.');
    }

    public function show(Brand $brand)
    {
        abort(404);
    }

    public function edit(Brand $brand)
    {
        abort(404);
    }

    public function update(Request $request, Brand $brand)
    {
        $this->authorize('update', $brand);
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:brands,name,' . $brand->id,
            'description' => 'nullable|string',
        ]);

        $brand->name = $validated['name'];
        $brand->slug = Str::slug($validated['name']);
        $brand->description = $validated['description'];
        $brand->save();

        return redirect()->route('brands.index')->with('success', 'تم تحديث العلامة التجارية بنجاح.');
    }

    public function destroy(Brand $brand)
    {
        $this->authorize('delete', $brand);

        $brand->delete();

        return redirect()->route('brands.index')->with('success', 'Brand deleted successfully.');
    }
}
