<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use App\Models\Product;
use App\Models\Category;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TagController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Tag::class);
        $perPage = $request->has('itemsPerPage') ? $request->itemsPerPage : 12;
        $tags = Tag::where(function ($query) use ($request) {
            if ($request->id != null) {
                $query->where('id', $request->id);
            }
            if ($request->search != null) {
                $query
                    ->where('name', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('slug', 'LIKE', '%' . $request->search . '%');
            }
            if ($request->datefilter != null) {
                try {
                    $startDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[0])->format('Y-m-d');
                    $endDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[1])->format('Y-m-d');
                    $query->whereDate('created_at', '>=', $startDate)->whereDate('created_at', '<=', $endDate);
                } catch (\Exception $e) {
                }
            }
        })
            ->withCount(['products', 'categories'])
            ->orderBy('created_at', 'DESC')
            ->paginate($perPage);

        $tagsCount = Tag::count();
        $tagsCountLastMonth = Tag::where('created_at', '>=', Carbon::now()->subMonth())->count();
        $tagsCountLastMonthPercentage = $tagsCountLastMonth > 0 ? number_format((100 * $tagsCountLastMonth) / $tagsCount) : 0;

        $productsCount = Product::count();
        $productsWithTagsCount = Product::whereHas('tags')->count();
        $productsWithTagsPercentage = $productsCount > 0 ? number_format((100 * $productsWithTagsCount) / $productsCount, 2) : 0;

        $categoriesCount = Category::count();
        $categoriesWithTagsCount = Category::whereHas('tags')->count();
        $categoriesWithTagsPercentage = $categoriesCount > 0 ? number_format((100 * $categoriesWithTagsCount) / $categoriesCount, 2) : 0;

        $analyticsData = [
            [
                'title' => 'عدد العلامات',
                'description' => 'عدد العلامات الكلي',
                'value' => $tagsCount,
                'percent' => 0,
                'icon' => 'tag',
                'color' => 'primary',
            ],
            [
                'title' => 'العلامات الجديدة',
                'description' => 'عدد العلامات الجديدة خلال الشهر الماضي',
                'value' => $tagsCountLastMonth,
                'percent' => $tagsCountLastMonthPercentage,
                'icon' => 'tag',
                'color' => 'success',
            ],
            [
                'title' => 'المنتجات مع علامات',
                'description' => 'عدد المنتجات التي لها علامات',
                'value' => $productsWithTagsCount,
                'percent' => $productsWithTagsPercentage,
                'icon' => 'shopping-cart',
                'color' => 'warning',
            ],
            [
                'title' => 'التصنيفات مع علامات',
                'description' => 'عدد التصنيفات التي لها علامات',
                'value' => $categoriesWithTagsCount,
                'percent' => $categoriesWithTagsPercentage,
                'icon' => 'menu',
                'color' => 'danger',
            ],
        ];

        return view('modules.tag.index', compact('tags', 'analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Tag::class);
        return view('modules.tag.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Tag::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:tags',
            'description' => 'nullable|string',
        ]);

        $tag = new Tag();
        $tag->name = $validated['name'];
        $tag->slug = Str::slug($validated['name']);
        $tag->description = $validated['description'];
        // $tag->user_id = Auth::id();
        $tag->save();

        return redirect()->route('tags.index')
            ->with('success', 'Tag created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Tag $tag)
    {
        abort(404);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Tag $tag)
    {
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Tag $tag)
    {
        $this->authorize('update', $tag);

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:tags,name,' . $tag->id,
            'description' => 'nullable|string',
        ]);

        $tag->name = $validated['name'];
        $tag->slug = Str::slug($validated['name']);
        $tag->description = $validated['description'];
        $tag->save();

        return redirect()->route('tags.index')
            ->with('success', 'Tag updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Tag $tag)
    {
        $this->authorize('delete', $tag);

        $tag->delete();

        return redirect()->route('tags.index')
            ->with('success', 'Tag deleted successfully.');
    }
} 