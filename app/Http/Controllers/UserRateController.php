<?php

namespace App\Http\Controllers;

use App\Helpers\Helpers;
use App\Models\User;
use App\Models\UserRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserRateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', UserRate::class);
        
        $pendingRatesCount = UserRate::where('status', 'pending')->count();
        $approvedRatesCount = UserRate::where('status', 'approved')->count();
        $rejectedRatesCount = UserRate::where('status', 'rejected')->count();
        $totalRatesCount = UserRate::count();
        
        $analyticsData = [
            [
                'title' => 'إجمالي التقييمات',
                'description' => 'عدد التقييمات الكلي',
                'value' => $totalRatesCount,
                'percent' => 0,
                'icon' => 'star',
                'color' => 'primary',
            ],
            [
                'title' => 'التقييمات المعلقة',
                'description' => 'عدد التقييمات المعلقة',
                'value' => $pendingRatesCount,
                'percent' => $totalRatesCount > 0 ? number_format((100 * $pendingRatesCount) / $totalRatesCount) : 0,
                'icon' => 'hourglass',
                'color' => 'warning',
            ],
            [
                'title' => 'التقييمات المقبولة',
                'description' => 'عدد التقييمات المقبولة',
                'value' => $approvedRatesCount,
                'percent' => $totalRatesCount > 0 ? number_format((100 * $approvedRatesCount) / $totalRatesCount) : 0,
                'icon' => 'check',
                'color' => 'success',
            ],
            [
                'title' => 'التقييمات المرفوضة',
                'description' => 'عدد التقييمات المرفوضة',
                'value' => $rejectedRatesCount,
                'percent' => $totalRatesCount > 0 ? number_format((100 * $rejectedRatesCount) / $totalRatesCount) : 0,
                'icon' => 'x',
                'color' => 'danger',
            ],
        ];
        
        return view('modules.user-rate.index', compact('analyticsData'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'rated_user_id' => 'required|exists:users,id',
            'rate' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:1000',
        ]);

        // Check if user is trying to rate themselves
        if ($request->rated_user_id == Auth::id()) {
            return back()->with('error', 'لا يمكنك تقييم نفسك');
        }

        // Check if user has already rated this user
        $existingRate = UserRate::where('rater_id', Auth::id())
            ->where('rated_user_id', $request->rated_user_id)
            ->first();

        if ($existingRate) {
            return back()->with('error', 'لقد قمت بتقييم هذا المستخدم من قبل');
        }

        $userRate = UserRate::create([
            'rater_id' => Auth::id(),
            'rated_user_id' => $request->rated_user_id,
            'rate' => $request->rate,
            'comment' => $request->comment,
            'status' => 'pending',
        ]);

        // Notify admins about new rating
        Helpers::notifyAdmins([
            'title' => 'تقييم جديد',
            'message' => 'تم إضافة تقييم جديد بواسطة ' . Auth::user()->name,
            'href' => route('user-rates.index'),
        ]);

        return back()->with('success', 'تم إضافة التقييم بنجاح وسيتم مراجعته من قبل الإدارة');
    }

    /**
     * Update the status of the specified resource in storage.
     */
    public function updateStatus(Request $request, UserRate $userRate)
    {
        $this->authorize('update', $userRate);
        
        $request->validate([
            'status' => 'required|in:approved,rejected',
        ]);

        $userRate->update([
            'status' => $request->status,
            'reviewed_by' => Auth::id(),
            'reviewed_at' => now(),
        ]);

        return back()->with('success', 'تم تحديث حالة التقييم بنجاح');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UserRate $userRate)
    {
        $this->authorize('delete', $userRate);
        
        $userRate->delete();
        
        return back()->with('success', 'تم حذف التقييم بنجاح');
    }
    
    /**
     * Display user ratings on user profile.
     */
    public function userRatings(User $user)
    {
        $ratings = $user->approvedRates()
            ->with('rater')
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('modules.user-rate.user-ratings', compact('user', 'ratings'));
    }
}
