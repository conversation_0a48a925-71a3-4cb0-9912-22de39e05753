<?php

namespace App\Http\Controllers;

use App\Models\Report;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class ReportController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of the reports.
     */
    public function index()
    {
        $this->authorize('viewAny', Report::class);

        $reports = Report::with('reporter')->latest()->paginate(10);
        $pendingCount = Report::where('status', 'pending')->count();

        return view('modules.reports.index', compact('reports', 'pendingCount'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Report::class);

        return view('modules.reports.create');
    }

    /**
     * Store a newly created report in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:product,comment,user,chat_message',
            'model_id' => 'required|integer',
            'text' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $report = Report::create([
            'type' => $request->type,
            'model_id' => $request->model_id,
            'text' => $request->text,
            'reporter_id' => Auth::id(),
        ]);

        return response()->json(['data' => $report, 'message' => 'Report created successfully'], 201);
    }

    /**
     * Display the specified report.
     */
    public function show(Report $report)
    {
        $this->authorize('view', $report);

        return view('modules.reports.show', compact('report'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Report $report)
    {
        $this->authorize('update', $report);

        return view('modules.reports.edit', compact('report'));
    }

    /**
     * Update the specified report in storage.
     */
    public function update(Request $request, Report $report)
    {
        $this->authorize('update', $report);
        $validator = Validator::make($request->all(), [
            'text' => 'required|string',
            'status' => 'required|in:pending,reviewed',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $report->update([
            'text' => $request->text,
            'status' => $request->status,
        ]);

        return redirect()->route('reports.index')->with('success', 'تم تحديث البلاغ بنجاح.');
    }

    /**
     * Remove the specified report from storage.
     */
    public function destroy(Report $report)
    {
        $this->authorize('delete', $report);

        $report->delete();

        return redirect()->route('reports.index')->with('success', 'تم حذف البلاغ بنجاح.');
    }
}
