<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class LocationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Location::class);

        $perPage = $request->has('itemsPerPage') ? $request->itemsPerPage : 12;
        $locations = Location::where(function ($query) use ($request) {
            if ($request->id != null) {
                $query->where('id', $request->id);
            }
            if ($request->search != null) {
                $query
                    ->where('name', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('city', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('state', 'LIKE', '%' . $request->search . '%')
                    ->orWhere('country', 'LIKE', '%' . $request->search . '%');
            }
            if ($request->datefilter != null) {
                try {
                    $startDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[0])->format('Y-m-d');
                    $endDate = Carbon::createFromFormat('d/m/Y', explode(' - ', $request->datefilter)[1])->format('Y-m-d');
                    $query->whereDate('created_at', '>=', $startDate)->whereDate('created_at', '<=', $endDate);
                } catch (\Exception $e) {
                }
            }
        })
            ->withCount('products')
            ->orderBy('created_at', 'DESC')
            ->paginate($perPage);

        $locationsCount = Location::count();
        $locationsCountLastMonth = Location::where('created_at', '>=', Carbon::now()->subMonth())->count();
        $locationsCountLastMonthPercentage = $locationsCountLastMonth > 0 ? number_format((100 * $locationsCountLastMonth) / $locationsCount) : 0;

        $productsCount = Product::count();
        $productsWithLocationsCount = Product::whereNotNull('location_id')->count();
        $productsWithLocationsPercentage = $productsCount > 0 ? number_format((100 * $productsWithLocationsCount) / $productsCount, 2) : 0;

        $analyticsData = [
            [
                'title' => 'عدد المواقع',
                'description' => 'عدد المواقع الكلي',
                'value' => $locationsCount,
                'percent' => 0,
                'icon' => 'map-pin',
                'color' => 'primary',
            ],
            [
                'title' => 'المواقع الجديدة',
                'description' => 'عدد المواقع الجديدة خلال الشهر الماضي',
                'value' => $locationsCountLastMonth,
                'percent' => $locationsCountLastMonthPercentage,
                'icon' => 'map-pin',
                'color' => 'success',
            ],
            [
                'title' => 'المنتجات مع مواقع',
                'description' => 'عدد المنتجات التي لها مواقع',
                'value' => $productsWithLocationsCount,
                'percent' => $productsWithLocationsPercentage,
                'icon' => 'shopping-cart',
                'color' => 'warning',
            ],
            [
                'title' => 'المنتجات بدون مواقع',
                'description' => 'عدد المنتجات التي ليس لها مواقع',
                'value' => $productsCount - $productsWithLocationsCount,
                'percent' => $productsCount > 0 ? number_format((100 * ($productsCount - $productsWithLocationsCount)) / $productsCount, 2) : 0,
                'icon' => 'shopping-cart',
                'color' => 'danger',
            ],
        ];

        return view('modules.location.index', compact('locations', 'analyticsData'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Location::class);

        return view('modules.location.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Location::class);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        $location = new Location();
        $location->name = $validated['name'];
        $location->city = $validated['city'];
        $location->state = $validated['state'];
        $location->country = $validated['country'];
        $location->latitude = $validated['latitude'];
        $location->longitude = $validated['longitude'];
        $location->created_by = Auth::id();
        $location->save();

        return redirect()->route('locations.index')
            ->with('success', 'تم إنشاء الموقع بنجاح.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Location $location)
    {
        abort(404);
        $this->authorize('view', $location);

        $products = $location->products()->paginate(12);
        return view('modules.location.show', compact('location', 'products'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Location $location)
    {
        $this->authorize('update', $location);

        return view('modules.location.update', compact('location'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Location $location)
    {
        $this->authorize('update', $location);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
        ]);

        $location->name = $validated['name'];
        $location->city = $validated['city'];
        $location->state = $validated['state'];
        $location->country = $validated['country'];
        $location->latitude = $validated['latitude'];
        $location->longitude = $validated['longitude'];
        $location->updated_by = Auth::id();
        $location->save();

        return redirect()->route('locations.index')
            ->with('success', 'تم تحديث الموقع بنجاح.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Location $location)
    {
        $this->authorize('delete', $location);

        $location->delete();

        return redirect()->route('locations.index')
            ->with('success', 'تم حذف الموقع بنجاح.');
    }
}