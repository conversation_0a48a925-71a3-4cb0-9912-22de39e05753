<?php

namespace App\Http\Controllers;

use App\Models\AppUpdate;
use Illuminate\Http\Request;

class AppUpdateController extends Controller
{

    public function index()
    {
        $this->authorize('viewAny', AppUpdate::class);

        $appUpdates = AppUpdate::all();
        return view('modules.app-updates.index', compact('appUpdates'));
    }

    public function create()
    {
        $this->authorize('create', AppUpdate::class);

        return redirect()->route('app-updates.index');
    }

    public function store(Request $request)
    {
        $this->authorize('create', AppUpdate::class);

        $request->validate([
            'platform' => 'required|string|max:255',
            'version' => 'required|string|max:255',
            'message' => 'nullable|string|max:255',
            'update_message' => 'nullable|string',
            'banner_message' => 'nullable|string|max:255',
            'release_date' => 'nullable|date',
            'force_update' => 'sometimes',
            'show_banner_message' => 'sometimes',
        ]);
        $data = $request->all();
        if ($request->has('force_update')) {
            $data['force_update'] = $request->force_update == 'on' ? true : false;
        }
        if ($request->has('show_banner_message')) {
            $data['show_banner_message'] = $request->show_banner_message == 'on' ? true : false;
        }

        AppUpdate::create($data);

        return redirect()->route('app-updates.index')->with('success', 'تم إنشاء تحديث التطبيق بنجاح.');
    }

    public function show(AppUpdate $appUpdate)
    {
        $this->authorize('view', $appUpdate);
        //
    }

    public function edit(AppUpdate $appUpdate)
    {
        abort(404);
    }

    public function update(Request $request, AppUpdate $appUpdate)
    {
        $this->authorize('update', $appUpdate);

        $request->validate([
            'platform' => 'required|string|max:255',
            'version' => 'required|string|max:255',
            'message' => 'nullable|string|max:255',
            'update_message' => 'nullable|string',
            'banner_message' => 'nullable|string|max:255',
            'release_date' => 'nullable|date',
            'force_update' => 'sometimes',
            'show_banner_message' => 'sometimes',
        ]);

        $data = $request->all();
        if ($request->has('force_update')) {
            $data['force_update'] = $request->force_update == 'on' ? true : false;
        } else {
            $data['force_update'] = false;
        }
        if ($request->has('show_banner_message')) {
            $data['show_banner_message'] = $request->show_banner_message == 'on' ? true : false;
        } else {
            $data['show_banner_message'] = false;
        }
        $appUpdate->update($request->all());

        return redirect()->route('app-updates.index')->with('success', 'تم تحديث تحديث التطبيق بنجاح.');
    }

    public function destroy(AppUpdate $appUpdate)
    {
        $this->authorize('delete', $appUpdate);

        $appUpdate->delete();

        return redirect()->route('app-updates.index')->with('success', 'تم حذف تحديث التطبيق بنجاح.');
    }
}
