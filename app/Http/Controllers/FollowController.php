<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Follow;
use App\Models\Location;
use App\Models\Product;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Request;

class FollowController extends Controller
{

    public function getFollows(Request $request)
    {
        $user = auth()->user();
        $follows = $user->follows()->with('followable')->get();
        return response()->json([
            'success' => true,
            'data' => $follows
        ]);
    }

    public function toggleFollow(Request $request)
    {
        $user = auth()->user();
        $type = $request->input('type'); // e.g. 'App\Models\Product'
        $id = $request->input('id');

        if ($type == 'product') {
            $followable = Product::findOrFail($id);
        }
        if ($type == 'user') {
            $followable = User::where('hashId', $id)->firstOrFail();
        }
        //*tag
        if ($type == 'tag') {
            $followable = Tag::findOrFail($id);
        }
        //*location
        if ($type == 'location') {
            $followable = Location::findOrFail($id);
        }
        //*category
        if ($type == 'category') {
            $followable = Category::findOrFail($id);
        }

        if ($type == 'brand') {
            $followable = Brand::findOrFail($id);
        }



        $existing = Follow::where('user_id', $user->id)
            ->where('followable_type', get_class($followable))
            ->where('followable_id', $followable->id)
            ->first();
    
        if ($existing) {
            $existing->delete();
            return response()->json([
                'success' => true,
                'data' => 'unfollowed'
            ]);
        } else {
            $follow = Follow::create([
                'followable_type' => get_class($followable),
                'followable_id' => $followable->id,
                'user_id' => $user->id
            ]);
            return response()->json([
                'success' => true,
                'data' => 'followed',
            ]);
        }
    }
    }

