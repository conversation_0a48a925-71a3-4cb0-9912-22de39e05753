<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreFaqsRequest;
use App\Http\Requests\UpdateFaqsRequest;
use App\Models\Faqs;

class FaqsController extends Controller
{
    public function index()
    {
        $faqs = Faqs::all();
        return view('modules.faqs.index', compact('faqs'));
    }

    public function create()
    {
        return view('modules.faqs.create');
    }

    public function store(StoreFaqsRequest $request)
    {
        Faqs::create($request->validated());

        return redirect()->route('faqs.index')->with('success', 'تم إنشاء السؤال بنجاح.');
    }

    public function show(Faqs $faqs)
    {
        return redirect()->route('faqs.index');
    }

    public function edit(Faqs $faq)
    {
        return view('modules.faqs.update', compact('faq'));
    }

    public function update(UpdateFaqsRequest $request, Faqs $faq)
    {
 
        $data = $request->validated();
        if ($request->has('status')) {
            $data['is_active'] = $request->status == 'active' ? true : false;
        }


        $faq->update($data);

        return redirect()->route('faqs.index')->with('success', 'تم تحديث السؤال بنجاح.');
    }

    public function destroy(Faqs $faq)
    {
        $faq->delete();

        return redirect()->route('faqs.index')->with('success', 'تم حذف السؤال بنجاح.');
    }
}
