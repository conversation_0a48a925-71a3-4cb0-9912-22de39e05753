<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class CommentResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'text' => $this->text,
            'parent_id' => $this->parent_id,
            'is_reply' => $this->isReply(),
            'user' => [
                'hashId' => $this->user->hashId,
                'name' => $this->user->name,
                'avatar' => $this->user->avatar(),
            ],
            'date' => [
                'date' => $this->created_at,
                'readable' => $this->created_at->diffForHumans(),
            ],
            'replies' => $this->when(
                $this->isParent() && $this->relationLoaded('replies'),
                CommentResource::collection($this->replies)
            ),
            'replies_count' => $this->when(
                $this->isParent(),
                $this->replies()->count()
            ),
        ];
    }
}
