<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class AppNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $user = $request->user();
        
        // Check if notification is read
        $isRead = false;
        if ($user) {
            $isRead = $this->appUserNotifications()
                ->where('user_id', $user->id)
                ->whereNotNull('read_at')
                ->exists();
        }
        
        return [
            'id' => $this->id,
            'title' => $this->title,
            'body' => $this->body,
            'type' => $this->type,
            'data' => $this->data ? json_decode($this->data) : null,
            'sent_at' => $this->sent_at,
            'created_at' =>  $this->created_at->locale('ar')->diffForHumans(),
            'is_read' => $isRead,
        ];
    }
}