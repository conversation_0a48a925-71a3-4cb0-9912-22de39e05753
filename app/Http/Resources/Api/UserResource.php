<?php

namespace App\Http\Resources\Api;

use App\Models\Achievement;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $userId = $request->user()?->id;
        return [
            'id' => $this->id,
            'hashId' => $this->hashId,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'photoUrl' => url('storage/' . $this->photo ?? ''),
            'coverUrl' => url('storage/' . $this->cover ?? ''),
            'status' => $this->status,
            'username' => $this->username,
            'isSocialAuth' => $this->social_id != null,
            'isAccountVerified' => $this->email_verified_at != null,
            'isVerified' => $this->is_verified,
            'isFollowed' => $this->isFollowedBy($userId),
            'achievements' => AchievementResource::collection(Achievement::all()),
        ];
    }
}
