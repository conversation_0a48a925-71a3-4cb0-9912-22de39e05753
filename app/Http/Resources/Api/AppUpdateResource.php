<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Resources\Json\JsonResource;

class AppUpdateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'platform' => $this->platform,
            'version' => $this->version,
            'message' => $this->message,
            'update_message' => $this->update_message,
            'force_update' => (bool) $this->force_update,
            'release_date' => $this->release_date,
        ];
    }
} 