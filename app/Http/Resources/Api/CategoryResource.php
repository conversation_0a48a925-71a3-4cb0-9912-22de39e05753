<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'image' => $this->image,
            'icon' => $this->icon,
            'app_item_size' => $this->app_item_size,
            'children' => $this->when($this->children, function () {
                return $this->children->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'title' => $child->title,
                        'description' => $child->description,
                        'image' => $child->image,
                        'icon' => $child->icon,
                        'order' => $child->order,
                        'app_item_size' => $child->app_item_size,
                        'parent_id' => $child->parent_id,
                        'brands' => $child->brands->map(function ($brand) {
                            return [
                                'id' => $brand->id,
                                'name' => $brand->name,
                                'logo' => $brand->logo,
                            ];
                        }),
                    ];
                });
            }),
            'brands' => $this->when($this->brands, function () {
                return $this->brands->map(function ($brand) {
                    return [
                        'id' => $brand->id,
                        'name' => $brand->name,
                        'logo' => $brand->logo,
                    ];
                });
            }),

        ];
    }
} 