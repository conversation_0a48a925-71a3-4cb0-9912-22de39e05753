<?php

namespace App\Http\Resources\Api;

use App\Models\UserFavorite;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $isOwner = $request->user()?->id == $this->user_id;
        $userId = $request->user()?->id;
        return [
            'id' => $this->id,
            'hashId' => $this->hashId,
            'category_id' => $this->category_id,
            'title' => $this->title,
            'desc' => $this->desc,
            'price' => $this->price,
            'location' => $this->location->name,
            'isFavorite' => $this->isFavorite($request->user()?->id),
            'status' => $isOwner ? $this->status : null,
            'condition' => $this->condition,
            'allow_phone_call' => $this->allow_phone_call,
            'allow_chat' => $this->allow_chat,
            'alternative_phone' => $this->alternative_phone,
            'media' => $this->media->map(function ($media) {
                return [
                    'url' => Storage::disk('products')->url($media->path),
                    'thumbnail_url' => Storage::disk('products')->url($media->thumbnail_path),
                    'small_url' => Storage::disk('products')->url($media->small_path),
                    'type' => $media->type,
                    'order' => $media->order,
                    'is_primary' => $media->is_primary,
                ];
            }),
            'date' => [
                'date' => $this->created_at,
                'readable' => $this->created_at->diffForHumans(),
            ],
            'comments' => $this->when($this->comments->count() > 0, CommentResource::collection($this->comments)),
            'user' => $this->when($this->user, function () use ($userId) {
                return [
                    'hashId' => $this->user->hashId,
                    'name' => $this->user->name,
                    'phone' => $this->user->phone,
                    'avatar' => $this->user->avatar(),
                    'isFollowed' => $this->isFollowed($userId)
                ];
            }),
            'tags' => $this->tags->map(function ($tag) {
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                ];
            }),
        ];
    }

    public function isFavorite($userId)
    {
        return UserFavorite::where('user_id', $userId)->where('product_id', $this->id)->exists();
    }
    public function isFollowed($userId)
    {
        
        return $userId ==null ? false: $this->user->isFollowedBy($userId);
    }
}
