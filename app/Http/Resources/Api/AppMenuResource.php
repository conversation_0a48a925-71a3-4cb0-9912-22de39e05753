<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AppMenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'title_ar' => $this->title_ar,
            'description' => $this->description,
            'description_ar' => $this->description_ar,
            'icon' => $this->icon,
            'children' => $this->when($this->children, function () {
                return $this->children->map(function ($child) {
                    return [
                        'id' => $child->id,
                        'title' => $child->title,
                        'title_ar' => $child->title_ar,
                        'description' => $child->description,
                        'description_ar' => $child->description_ar,
                        'icon' => $child->icon,
                        'parent_id' => $child->parent_id,
                        'data' => $child->data,
                    ];
                });
            }),
            'data' => $this->data,

        ];
    }
} 