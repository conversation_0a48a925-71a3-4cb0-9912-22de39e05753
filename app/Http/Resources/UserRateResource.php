<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserRateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'hashId' => $this->hashId,
            'rater' => $this->whenLoaded('rater', function () {
                return [
                    'id' => $this->rater->id,
                    'name' => $this->rater->name,
                    'username' => $this->rater->username,
                    'photo' => $this->rater->photo,
                ];
            }),
            'rated_user' => $this->whenLoaded('ratedUser', function () {
                return [
                    'id' => $this->ratedUser->id,
                    'name' => $this->ratedUser->name,
                    'username' => $this->ratedUser->username,
                    'photo' => $this->ratedUser->photo,
                ];
            }),
            'rate' => $this->rate,
            'comment' => $this->comment,
            'status' => $this->status,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
