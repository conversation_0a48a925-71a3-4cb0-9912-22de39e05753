<?php

use App\Http\Middleware\EnsureEmailIsVerified;
use App\Http\Middleware\LocaleMiddleware;
use App\Http\Middleware\TrackUserSession;
use App\Http\Middleware\WebAuthenticate;
use Modules\TrafficLogs\Middleware\TrafficLogsMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Controllers\TelegramErrorBotController;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        channels: __DIR__.'/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->web(LocaleMiddleware::class);
        $middleware->web(TrafficLogsMiddleware::class);
        $middleware->web(TrackUserSession::class);

        // Register middleware aliases
        $middleware->alias([
            'verified' => EnsureEmailIsVerified::class,
            'web.auth' => WebAuthenticate::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
                if(env('APP_ENV') != 'local') {
            $exceptions->reportable(function (Throwable $e) {
                    $telegramBot = new TelegramErrorBotController();
                    $telegramBot->sendError($e);
            });
        }
    })->create();
