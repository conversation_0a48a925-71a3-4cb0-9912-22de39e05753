# Achievement System Documentation

## Overview

The Achievement System is a comprehensive solution for managing and awarding achievements to users automatically and manually. It supports multiple types of achievements and integrates with the existing notification system.

## Features

- **Automatic Achievement Checking**: Periodic checks for all users or specific users
- **Manual Achievement Management**: Admins can award/remove achievements manually
- **System vs Custom Achievements**: System achievements cannot be modified, custom ones can be managed
- **Queue-based Processing**: Scalable processing using Laravel queues
- **Notification Integration**: Automatic notifications when achievements are awarded
- **Multiple Achievement Types**: Support for various achievement categories

## Achievement Types

### 1. Commission-related Achievements (دفع العموله) - Year-based
Each achievement is awarded per year when user pays commissions:
- `commission_1_paid`: First commission paid in the year
- `commission_2_paid`: Second commission paid in the year
- `commission_3_paid`: Third commission paid in the year
- `commission_5_paid`: Fifth commission paid in the year
- `commission_10_paid`: Tenth commission paid in the year
- `commission_20_paid`: Twentieth commission paid in the year

**Note**: These achievements are year-based, meaning a user can get the same achievement multiple times (once per year).

### 2. Annual Excellence Achievements (تميز السنة المثالي)
- `annual_excellence`: Multiple achievements + high performance in the same year

### 4. Brand Verification Achievements (موثق كعلامه تجارية)
- `brand_verified`: User has approved brand verification

### 5. Premium Membership Achievements (عضوية تميز)
- `premium_membership`: 20+ approved ratings in a year

## Usage

### Console Commands

#### Check achievements for a specific user
```bash
php artisan achievements:check --user-id=123
```

#### Check achievements for all users
```bash
php artisan achievements:check --all
```

#### Run synchronously (for testing)
```bash
php artisan achievements:check --user-id=123 --sync
```

### Programmatic Usage

#### Trigger achievement check for a user
```php
use App\Jobs\ProcessAchievementQueueJob;
use App\Models\User;

$user = User::find(1);
ProcessAchievementQueueJob::dispatchForUser($user);
```

#### Trigger achievement check for all users
```php
ProcessAchievementQueueJob::dispatchForAllUsers();
```

#### Manual achievement management
```php
use App\Services\AchievementService;

$achievementService = app(AchievementService::class);

// Award achievement manually
$result = $achievementService->manuallyAwardAchievement($user, 'achievement_slug', $admin);

// Remove achievement
$result = $achievementService->removeAchievementFromUser($user, 'achievement_slug', $admin);
```

### API Endpoints

#### Get user achievements
```
GET /api/user/achievements
Authorization: Bearer {token}
```

#### Admin endpoints (example routes - implement as needed)
```
GET /admin/achievements
POST /admin/achievements/{user}/award
DELETE /admin/achievements/{user}/remove
POST /admin/achievements/trigger-check
```

## Scheduled Tasks

The system automatically runs achievement checks:

- **Daily**: Every day at 2:00 AM
- **Weekly**: Every Sunday at 3:00 AM

Configure in `routes/console.php`:
```php
Schedule::command('achievements:check --all')
    ->daily()
    ->at('02:00');
```

## Database Structure

### Achievements Table
- `id`: Primary key
- `name`: Achievement name
- `slug`: Unique slug
- `description`: Achievement description
- `is_system`: Boolean flag (system achievements cannot be modified)
- `icon`: Icon class/name
- `created_by`, `updated_by`, `deleted_by`: Audit fields

### User Achievements Table
- `user_id`: Foreign key to users
- `achievement_id`: Foreign key to achievements
- `achieved_at`: Timestamp when achieved

## Configuration

### Queue Configuration
The system uses two queue channels:
- `achievements`: For individual user checks
- `achievements-bulk`: For bulk operations

### Notification Integration
Achievements automatically create:
- Email notifications
- Database notifications
- AppNotifications for mobile apps

## Extending the System

### Adding New Achievement Types

1. **Add achievement logic** in `AchievementConditionsService`:
```php
private function checkNewAchievementType(User $user): array
{
    // Your logic here
    if ($condition) {
        $awarded = $this->awardAchievement($user, 'new_achievement_slug');
        if ($awarded) {
            return ['new_achievement_slug'];
        }
    }
    return [];
}
```

2. **Update the main check method**:
```php
public function checkAllAchievementsForUser(User $user): array
{
    // ... existing code ...
    $newAchievements = $this->checkNewAchievementType($user);
    $awardedAchievements = array_merge($awardedAchievements, $newAchievements);
    // ... rest of code ...
}
```

3. **Add achievement to seeder**:
```php
[
    'name' => 'New Achievement',
    'slug' => 'new_achievement_slug',
    'description' => 'Description of the new achievement',
    'icon' => 'ti-icon',
    'is_system' => true,
],
```

## Troubleshooting

### Common Issues

1. **Achievements not being awarded**
   - Check queue workers are running: `php artisan queue:work`
   - Verify achievement conditions in `AchievementConditionsService`
   - Check logs for errors

2. **Notifications not sent**
   - Verify notification channels are configured
   - Check mail configuration for email notifications

3. **Performance issues**
   - Monitor queue performance
   - Consider adjusting batch sizes in bulk operations

### Logging
All achievement operations are logged. Check `storage/logs/laravel.log` for:
- Achievement awards
- Errors during processing
- Queue job status

## Security Considerations

- System achievements cannot be modified by admins
- All manual operations are logged with admin user ID
- Achievement conditions are centralized for security
- Queue jobs have retry mechanisms and failure handling

## Testing

Run achievement checks in sync mode for testing:
```bash
php artisan achievements:check --user-id=1 --sync
```

This will show immediate results without queuing.
