# Haragy Application Deployment Guide

This document provides comprehensive instructions for deploying the Haragy application to a production server, including configuration of all required tools and services.

## Table of Contents

1. [Server Requirements](#server-requirements)
2. [Initial Server Setup](#initial-server-setup)
3. [Application Installation](#application-installation)
4. [Database Configuration](#database-configuration)
5. [Environment Configuration](#environment-configuration)
6. [Broadcasting Setup](#broadcasting-setup)
7. [Firebase Configuration](#firebase-configuration)
8. [Telegram Bot Configuration](#telegram-bot-configuration)
9. [Queue Worker Setup](#queue-worker-setup)
10. [Web Server Configuration](#web-server-configuration)
11. [SSL Configuration](#ssl-configuration)
12. [Scheduled Tasks](#scheduled-tasks)
13. [Post-Deployment Verification](#post-deployment-verification)
14. [Troubleshooting](#troubleshooting)

## Server Requirements

- PHP 8.2 or higher
- Composer 2.x
- Node.js 18.x or higher and NPM
- MySQL 8.0 or higher (or other supported database)
- Redis (for broadcasting and caching)
- Web server (Nginx recommended)
- SSL certificate
- FFmpeg (for video processing)
- Git

## Initial Server Setup

1. Update the server packages:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

2. Install required packages:
   ```bash
   sudo apt install -y git curl unzip nginx redis-server ffmpeg
   ```

3. Install PHP and required extensions:
   ```bash
   sudo apt install -y php8.2-fpm php8.2-cli php8.2-common php8.2-mysql php8.2-zip php8.2-gd php8.2-mbstring php8.2-curl php8.2-xml php8.2-bcmath php8.2-intl php8.2-redis
   ```

4. Install Composer:
   ```bash
   curl -sS https://getcomposer.org/installer | php
   sudo mv composer.phar /usr/local/bin/composer
   ```

5. Install Node.js and NPM:
   ```bash
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt install -y nodejs
   ```

## Application Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-repo/haragy_backend.git /var/www/haragy
   cd /var/www/haragy
   ```

2. Install PHP dependencies:
   ```bash
   composer install --no-dev --optimize-autoloader
   ```

3. Install Node.js dependencies:
   ```bash
   npm install
   ```

4. Build frontend assets:
   ```bash
   npm run build
   ```

5. Set proper permissions:
   ```bash
   sudo chown -R www-data:www-data /var/www/haragy
   sudo chmod -R 755 /var/www/haragy
   sudo chmod -R 775 /var/www/haragy/storage /var/www/haragy/bootstrap/cache
   ```

## Database Configuration

1. Create a new MySQL database and user:
   ```sql
   CREATE DATABASE haragy;
   CREATE USER 'haragy'@'localhost' IDENTIFIED BY 'your_secure_password';
   GRANT ALL PRIVILEGES ON haragy.* TO 'haragy'@'localhost';
   FLUSH PRIVILEGES;
   ```

2. Run migrations:
   ```bash
   php artisan migrate --seed
   ```

## Environment Configuration

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Generate application key:
   ```bash
   php artisan key:generate
   ```

3. Configure the `.env` file with your production settings:
   ```
   APP_ENV=production
   APP_DEBUG=false
   APP_URL=https://your-domain.com
   
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=haragy
   DB_USERNAME=haragy
   DB_PASSWORD=your_secure_password
   
   CACHE_DRIVER=redis
   SESSION_DRIVER=redis
   QUEUE_CONNECTION=redis
   
   REDIS_HOST=127.0.0.1
   REDIS_PASSWORD=null
   REDIS_PORT=6379
   ```

## Broadcasting Setup

The application uses Laravel Reverb for real-time features. Configure it as follows:

1. Configure broadcasting in `.env`:
   ```
   BROADCAST_DRIVER=reverb
   REVERB_APP_ID=your-app-id
   REVERB_APP_SECRET=your-app-secret
   REVERB_APP_KEY=your-app-key
   REVERB_HOST=your-domain.com
   REVERB_PORT=443
   REVERB_SCHEME=https
   
   REVERB_SERVER_HOST=0.0.0.0
   REVERB_SERVER_PORT=8080
   ```

2. Start the Reverb server:
   ```bash
   php artisan reverb:start
   ```

3. For production, set up a supervisor configuration to keep Reverb running:
   ```
   [program:reverb]
   process_name=%(program_name)s
   command=php /var/www/haragy/artisan reverb:start
   autostart=true
   autorestart=true
   user=www-data
   redirect_stderr=true
   stdout_logfile=/var/www/haragy/storage/logs/reverb.log
   stopwaitsecs=3600
   ```

4. Publish the Chat module's JavaScript assets:
   ```bash
   php publish-chat-assets.php
   ```

## Firebase Configuration

The application uses Firebase for push notifications:

1. Place your Firebase credentials file at `config/firebase_credentials.json`

2. Configure Firebase in `.env`:
   ```
   FIREBASE_CREDENTIALS=config/firebase_credentials.json
   ```

## Telegram Bot Configuration

If using Telegram notifications:

1. Configure Telegram in `.env`:
   ```
   TELEGRAM_BOT_TOKEN=your-bot-token
   TELEGRAM_ERROR_BOT_TOKEN=your-error-bot-token
   TELEGRAM_ERROR_BOT_CHAT_ID=your-chat-id
   ```

## Queue Worker Setup

1. Set up a supervisor configuration for queue workers:
   ```
   [program:haragy-worker]
   process_name=%(program_name)s_%(process_num)02d
   command=php /var/www/haragy/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
   autostart=true
   autorestart=true
   stopasgroup=true
   killasgroup=true
   user=www-data
   numprocs=2
   redirect_stderr=true
   stdout_logfile=/var/www/haragy/storage/logs/worker.log
   stopwaitsecs=3600
   ```

2. Reload supervisor:
   ```bash
   sudo supervisorctl reread
   sudo supervisorctl update
   sudo supervisorctl start all
   ```

## Web Server Configuration

### Nginx Configuration

Create a new Nginx site configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/haragy/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## SSL Configuration

1. Install Certbot:
   ```bash
   sudo apt install -y certbot python3-certbot-nginx
   ```

2. Obtain SSL certificate:
   ```bash
   sudo certbot --nginx -d your-domain.com
   ```

## Scheduled Tasks

Set up a cron job to run Laravel's scheduler:

```bash
* * * * * cd /var/www/haragy && php artisan schedule:run >> /dev/null 2>&1
```

## Post-Deployment Verification

1. Check application status:
   ```bash
   php artisan about
   ```

2. Verify queue workers are running:
   ```bash
   php artisan queue:monitor
   ```

3. Test broadcasting:
   ```bash
   php test-chat-broadcast.php 1 1
   ```

## Troubleshooting

### Broadcasting Issues

If real-time features are not working:

1. Check Reverb logs:
   ```bash
   tail -f /var/www/haragy/storage/logs/reverb.log
   ```

2. Verify channel authorization:
   ```bash
   php artisan tinker
   Broadcast::channel('chat.1', function ($user, $chatId) { return true; });
   ```

3. Test Echo connection in browser console:
   ```javascript
   window.Echo.connector.socket.connected
   ```

### Queue Issues

If queued jobs are not processing:

1. Check worker logs:
   ```bash
   tail -f /var/www/haragy/storage/logs/worker.log
   ```

2. Restart queue workers:
   ```bash
   php artisan queue:restart
   ```

### Permission Issues

If you encounter permission issues:

```bash
sudo chown -R www-data:www-data /var/www/haragy/storage
sudo chown -R www-data:www-data /var/www/haragy/bootstrap/cache
```
