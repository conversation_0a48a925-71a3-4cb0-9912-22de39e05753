# Haragy Project Structure Documentation

This document provides an overview of the Haragy application's project structure, explaining the purpose of each directory and key files.

## Table of Contents

1. [Root Directory](#root-directory)
2. [App Directory](#app-directory)
3. [Modules Directory](#modules-directory)
4. [Resources Directory](#resources-directory)
5. [Config Directory](#config-directory)
6. [Routes Directory](#routes-directory)
7. [Database Directory](#database-directory)
8. [Public Directory](#public-directory)
9. [Storage Directory](#storage-directory)
10. [Tests Directory](#tests-directory)

## Root Directory

The root directory contains the main configuration files and directories for the application:

- `.env`: Environment-specific configuration
- `.env.example`: Example environment configuration
- `artisan`: Laravel command-line tool
- `composer.json`: PHP dependencies
- `package.json`: JavaScript dependencies
- `vite.config.js`: Vite configuration
- `tailwind.config.js`: Tailwind CSS configuration
- `publish-chat-assets.php`: Script to publish chat assets
- `laravel-echo-server.json`: Echo server configuration (legacy)

## App Directory

The `app` directory contains the core application code:

### Controllers

- `app/Http/Controllers/`: Contains all controllers
  - `app/Http/Controllers/front/`: Frontend controllers
  - `app/Http/Controllers/admin/`: Admin controllers
  - `app/Http/Controllers/api/`: API controllers
  - `app/Http/Controllers/FirebaseController.php`: Firebase integration

### Models

- `app/Models/`: Contains all Eloquent models
  - `app/Models/User.php`: User model
  - `app/Models/Product.php`: Product model
  - `app/Models/Category.php`: Category model

### Middleware

- `app/Http/Middleware/`: Contains all middleware
  - `app/Http/Middleware/LocaleMiddleware.php`: Handles localization
  - `app/Http/Middleware/TrafficLogsMiddleware.php`: Logs traffic
  - `app/Http/Middleware/TrackUserSession.php`: Tracks user sessions

### Jobs and Events

- `app/Jobs/`: Contains all queued jobs
  - `app/Jobs/SendScheduledNotification.php`: Sends scheduled notifications
  - `app/Jobs/SendAppNotificationToUsersJob.php`: Sends app notifications

- `app/Events/`: Contains all events
  - `app/Events/UserRegisteredEvent.php`: User registration event

### Helpers and Traits

- `app/Helpers/`: Contains helper classes
  - `app/Helpers/Helpers.php`: General helper functions

- `app/Traits/`: Contains reusable traits
  - `app/Traits/HasHashId.php`: Hashid functionality for models

## Modules Directory

The `Modules` directory contains modular components of the application:

### Chat Module

- `Modules/Chat/`: Real-time chat functionality
  - `Modules/Chat/app/Models/`: Chat models
  - `Modules/Chat/app/Http/Controllers/`: Chat controllers
  - `Modules/Chat/app/Events/`: Chat events
  - `Modules/Chat/resources/`: Chat views and assets
  - `Modules/Chat/routes/`: Chat routes
  - `Modules/Chat/vite.config.js`: Module-specific Vite config

### UserTracker Module

- `Modules/UserTracker/`: User tracking functionality
  - `Modules/UserTracker/app/Models/`: User tracking models
  - `Modules/UserTracker/app/Http/Controllers/`: User tracking controllers
  - `Modules/UserTracker/resources/`: User tracking views and assets
  - `Modules/UserTracker/routes/`: User tracking routes

### TrafficLogs Module

- `Modules/TrafficLogs/`: Traffic logging functionality
  - `Modules/TrafficLogs/app/Models/`: Traffic log models
  - `Modules/TrafficLogs/app/Http/Controllers/`: Traffic log controllers
  - `Modules/TrafficLogs/resources/`: Traffic log views and assets
  - `Modules/TrafficLogs/routes/`: Traffic log routes

### Todo Module

- `Modules/Todo/`: Task management functionality
  - `Modules/Todo/app/Models/`: Todo models
  - `Modules/Todo/app/Http/Controllers/`: Todo controllers
  - `Modules/Todo/resources/`: Todo views and assets
  - `Modules/Todo/routes/`: Todo routes

## Resources Directory

The `resources` directory contains frontend assets and views:

### Views

- `resources/views/`: Contains all Blade templates
  - `resources/views/front2/`: Frontend views
  - `resources/views/admin/`: Admin views
  - `resources/views/web/`: Web views
  - `resources/views/auth/`: Authentication views

### Assets

- `resources/css/`: CSS files
  - `resources/css/app.css`: Main CSS file

- `resources/js/`: JavaScript files
  - `resources/js/app.js`: Main JavaScript file
  - `resources/js/echo.js`: Laravel Echo configuration

- `resources/assets/`: Other assets
  - `resources/assets/sass/`: SASS files
  - `resources/assets/images/`: Image files

### Localization

- `resources/lang/`: Language files
  - `resources/lang/en/`: English translations
  - `resources/lang/ar/`: Arabic translations

## Config Directory

The `config` directory contains configuration files:

- `config/app.php`: Application configuration
- `config/auth.php`: Authentication configuration
- `config/broadcasting.php`: Broadcasting configuration
- `config/reverb.php`: Reverb configuration
- `config/database.php`: Database configuration
- `config/filesystems.php`: File storage configuration
- `config/services.php`: External services configuration
- `config/telegram.php`: Telegram bot configuration
- `config/firebase_credentials.json`: Firebase credentials

## Routes Directory

The `routes` directory contains route definitions:

- `routes/web.php`: Web routes
- `routes/api.php`: API routes
- `routes/channels.php`: Broadcasting channels
- `routes/console.php`: Console commands

## Database Directory

The `database` directory contains database migrations, seeders, and factories:

- `database/migrations/`: Database migrations
- `database/seeders/`: Database seeders
- `database/factories/`: Model factories
- `database/sqlite/`: SQLite database files

## Public Directory

The `public` directory contains publicly accessible files:

- `public/index.php`: Entry point for the application
- `public/build/`: Compiled assets (generated by Vite)
- `public/storage/`: Symbolic link to `storage/app/public`
- `public/modules/`: Module-specific public assets
  - `public/modules/chat/js/`: Chat JavaScript files

## Storage Directory

The `storage` directory contains application storage:

- `storage/app/`: Application storage
  - `storage/app/public/`: Publicly accessible storage
  - `storage/app/private/`: Private storage
- `storage/logs/`: Log files
- `storage/framework/`: Framework storage

## Tests Directory

The `tests` directory contains test files:

- `tests/Feature/`: Feature tests
- `tests/Unit/`: Unit tests
- `tests/Browser/`: Browser tests

## Key Files

### Configuration Files

- `bootstrap/app.php`: Application bootstrap
- `composer.json`: PHP dependencies
- `package.json`: JavaScript dependencies
- `vite.config.js`: Vite configuration
- `tailwind.config.js`: Tailwind CSS configuration
- `laravel-echo-server.json`: Echo server configuration

### Deployment Files

- `publish-chat-assets.php`: Script to publish chat assets
- `test-chat-broadcast.php`: Script to test chat broadcasting
- `TESTING-BROADCAST-CHANNELS.md`: Guide for testing broadcast channels

### Documentation Files

- `docs/deployment.md`: Deployment documentation
- `docs/tools-documentation.md`: Tools documentation
- `docs/chat-realtime-documentation.md`: Chat and real-time features documentation
- `docs/project-structure.md`: Project structure documentation (this file)
- `docs/installation.md`: Installation documentation
- `docs/packages.md`: Packages documentation
- `docs/reverb.md`: Reverb documentation
