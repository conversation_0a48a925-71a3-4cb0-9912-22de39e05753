### Log viewer packages  
- [URL](https://github.com/opcodesio/log-viewer) 

Log Viewer is a package for Laravel allowing you to easily read, search and manage your Laravel logs. You will no longer need to read the raw Laravel log files trying to find what you're looking for.

It is built from the ground-up with custom indexing methods that make it much faster and efficient than other log viewer packages out there.

The user interface is already built for you, and all that is left for you - is to just run composer require opcodesio/log-viewer.

### Installation
```bash
composer require opcodesio/log-viewer
```

### Usage
- Visit the `/log-viewer` route in your browser to view your logs.
