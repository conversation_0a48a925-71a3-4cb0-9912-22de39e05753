# Creating a Model in Laravel

## Create the Model

To create a new model with all necessary components (migration, factory, seeder, controller, and resource), run the following command:

```bash
php artisan make:model Name --all
```

## Resources [Optional]

Create a new resource for the model to transform the model data before sending it to the client. To create a new resource, run the following command:

```bash
php artisan make:resource NameResource
```

## Migration

Open the generated migration file in the `database/migrations` directory and define the schema for the model's table.

run php artisan migrate && php artisan migrate --seed --seeder=PermissionsSeeder

## Define Relationships and Traits

Open the generated `Name.php` model file and define any necessary relationships and include the `HashId` trait.:

## Store/Update Request

Full fill the request for the model in the `FeatureRequest.php` file.

## Create CRUD Operations

Go to Controller and create CRUD operations for the model.

## Policy [Optional]

Full fill the policy for the model in the `FeaturePolicy.php` file.


## Define Web Routes

Add the necessary routes for the CRUD operations in the `web.php` file:

```php
Route::resource('names', NameController::class);
```

## Create Views for CRUD Operations

Create the Blade templates for the CRUD operations in the `resources/views/names` directory. For example:

- `index.blade.php` for listing all records
- `create.blade.php` for creating a new record
- `edit.blade.php` for editing an existing record
- `show.blade.php` for displaying a single record

## Add Permissions

Go to RolesEnum.php->allPermissions() add new permission for new feature
