# Reverb Installtion

## Installation

```bash
php artisan install:broadcasting
```

## add the following if not already in your .env file

```bash
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your-app-id
REVERB_APP_SECRET=your-app-secret
REVERB_APP_KEY=your-app-key
REVERB_APP_URL=your-app-url
```

## Add the following to your config/broadcasting.php file

```php
'reverb' => [
'driver' => 'reverb',
'app_id' => env('REVERB_APP_ID'),
'app_secret' => env('REVERB_APP_SECRET'),
'app_key' => env('REVERB_APP_KEY'),
'app_url' => env('REVERB_APP_URL'),
],
```
## Create event

```bash
php artisan make:event PushNotificationUpdate
```

## Run Queue Worker to dispatch the event when it's called

```bash
php artisan queue:work
```

## Change channel to push-notification and make the model implements ShouldBroadcast

```php
public function broadcastOn()
{
return new Channel('push-notification');
}
```
## Add the following script to your app

```javascript
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log(window.Echo);
        window.Echo.channel('push-notification')
            .listen('PushNotificationUpdate', (e) => {
                alert('New Notification');
            });
    });
</script>
```


## To push notification

```php
PushNotificationUpdate::dispatch('test 123');
```
or
```php
event(new PushNotificationUpdate());
```


#! Important
make sure Echo.js file implemented in your app


