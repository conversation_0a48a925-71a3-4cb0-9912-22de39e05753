# Install typescript

```
npm i -D typescript
npm i -D vue-tsc
tsc --init
```

### Edit tsconfig.json

```

{
  "compilerOptions": {
      "allowJs": true,
      "module": "ESNext",
      "lib": ["ES2020", "DOM", "DOM.Iterable"],
      "moduleResolution": "Node",
      "target": "esnext",
      "jsx": "preserve",
      "strict": true,
      "esModuleInterop": true,
      "skipLibCheck": true,
      "forceConsistentCasingInFileNames": true,
      "noEmit": true,
      "isolatedModules": true,
      "types": ["vite/client"]
  },
  "exclude": ["node_modules", "public"],
  "include": [
      "resources/js/**/*.ts",
      "resources/js/**/*.d.ts",
      "resources/js/**/*.vue",
      "resources/js/app.ts",
  ],

}
```

### Edit package.json build script to "vue-tsc --noEmit && vite build"

### Edit resource/js/app.js to resource/js/app.ts

# For vue installation

-   npm install --save-dev @vitejs/plugin-vue
-   npm install vue@latest
-   npm install vue-router@latest
<!-- npm install vuex@latest -->
-   npm install pinia
-   npm install vue-toast-notification@3
-   npm install @vuelidate/core @vuelidate/validators
-   npm install vue-sweetalert2
-   npm install laravel-vue-pagination
<!-- npm install vue3-apexcharts -->
-   npm install apexcharts
-   npm install vue3-apexcharts

<!-- npm install axios -->

npm install vue-axios

<!-- npm install vue3-sfc-loader -->

## EDIT YOUR VITE CONFIG FILE

```
import vue from '@vitejs/plugin-vue';

export default {
plugins: [vue()]
}
```

# For laravel installation

-   composer require laravel/ui
-   php artisan ui vue
-   npm install
-   npm run dev

## Tailwindcss
- Add tailwindcss to your project file tailwind.config.js
```
        "./resources/**/*.js",
        "./resources/**/*.ts",
        "./resources/**/*.vue",
```
