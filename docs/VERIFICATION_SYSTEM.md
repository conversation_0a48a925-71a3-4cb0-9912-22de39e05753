# Verified Badge System Documentation

## Overview

The Verified Badge System (علامة التوثيق) allows administrators to verify trusted users in the Haraji marketplace. Verified users receive a blue checkmark badge that increases trust and credibility.

## Features

### 1. Dynamic Verification Requirements
- Admins can configure custom verification requirements
- Support for different field types: text, textarea, file uploads, select dropdowns
- Required/optional field configuration
- Stored as <PERSON><PERSON><PERSON> in the settings table

### 2. User Verification Request Flow
- Users can request verification through a web form
- Upload required documents (ID cards, business licenses, etc.)
- Submit personal information based on admin requirements
- Automatic validation and file storage

### 3. Admin Management Interface
- View all verification requests in a filterable table
- Approve/reject requests with admin notes
- View submitted documents and data
- User verification status management (verify/unverify)

### 4. Verification Status Tracking
- Pending: Request submitted, awaiting review
- Approved: User verified and badge granted
- Rejected: Request denied with reason

### 5. Notification System
- Email notifications for status changes
- Database notifications for in-app alerts
- Customizable notification templates

### 6. Dashboard Analytics
- Total verified users count
- Pending verification requests
- Verification rate statistics
- Admin dashboard integration

## Database Schema

### Users Table Additions
```sql
ALTER TABLE users ADD COLUMN is_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN verified_at TIMESTAMP NULL;
ALTER TABLE users ADD COLUMN verified_by BIGINT UNSIGNED NULL;
ALTER TABLE users ADD COLUMN verification_notes TEXT NULL;
```

### User Verifications Table
```sql
CREATE TABLE user_verifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    user_type VARCHAR(255) NULL,
    submitted_data JSON NULL,
    uploaded_files JSON NULL,
    admin_notes TEXT NULL,
    reviewed_by BIGINT UNSIGNED NULL,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL
);
```

## API Endpoints

### User Routes (Web Interface)
- `GET /verification/request` - Show verification form
- `POST /verification/request` - Submit verification request

### Admin Routes
- `GET /admin/verifications/requirements` - Manage verification requirements
- `POST /admin/verifications/requirements` - Save verification requirements
- `POST /admin/verifications/verify-user/{user}` - Verify user directly
- `POST /admin/verifications/unverify-user/{user}` - Remove verification
- `POST /admin/verifications/approve/{verification}` - Approve request
- `POST /admin/verifications/reject/{verification}` - Reject request
- `GET /admin/verifications/details/{verification}` - Get verification details

## Components

### Livewire Components
1. **VerificationRequirementsForm** - Admin interface for configuring requirements
2. **VerificationRequestsTable** - Table showing all verification requests
3. **VerificationModal** - Modal for viewing/managing individual requests

### Blade Components
1. **verification-badge** - Display verification badge next to usernames
2. **verification-status** - Show user's verification status in profile

### Models
1. **UserVerification** - Main verification request model
2. **User** - Extended with verification methods and relationships

## Usage Examples

### Display Verification Badge
```blade
<x-verification-badge :user="$user" size="md" />
```

### Check if User is Verified
```php
if ($user->isVerified()) {
    // User is verified
}

if ($user->hasPendingVerification()) {
    // User has pending verification
}
```

### Admin Actions
```php
// Verify user directly
$user->markAsVerified($adminId, 'Verified manually');

// Remove verification
$user->markAsUnverified();
```

## Configuration

### Default Verification Requirements
The system comes with default requirements that can be customized:

1. National ID Number (required)
2. ID Card Photo (required)
3. Phone Number (required)
4. Business Type (optional)
5. Business License (optional for businesses)
6. Additional Information (optional)

### File Upload Settings
- Maximum file size: 10MB
- Supported formats: JPG, PNG, PDF, DOC, DOCX
- Files stored in `storage/app/public/verification_documents/`

## Security Considerations

1. **File Validation**: All uploaded files are validated for type and size
2. **Access Control**: Only authenticated users can request verification
3. **Admin Permissions**: Verification management requires admin access
4. **Data Privacy**: Sensitive documents are stored securely
5. **Audit Trail**: All verification actions are logged with timestamps

## Installation Steps

1. Run migrations:
```bash
php artisan migrate
```

2. Seed default requirements:
```bash
php artisan db:seed --class=VerificationRequirementsSeeder
```

3. Configure file storage:
```bash
php artisan storage:link
```

4. Set up email notifications in `.env`:
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
```

## Testing

Run the verification system tests:
```bash
php artisan test --filter=VerificationSystemTest
```

## Customization

### Adding New Field Types
To add new field types to the verification form:

1. Update the `VerificationRequirementsForm` component
2. Add validation rules in `VerificationController`
3. Update the user verification form view

### Custom Notification Templates
Modify `VerificationStatusNotification` to customize email templates and content.

### Dashboard Integration
The system automatically integrates with the main dashboard, showing:
- Verification statistics card
- Quick access button to verification management
- User table with verification status column

## Troubleshooting

### Common Issues

1. **File Upload Errors**: Check PHP upload limits and storage permissions
2. **Email Notifications Not Sent**: Verify email configuration in `.env`
3. **Missing Verification Badge**: Ensure user relationship is loaded
4. **Permission Denied**: Check user roles and middleware

### Debug Commands
```bash
# Check verification requirements
php artisan tinker
>>> App\Models\Settings::where('key', 'verification_requirements')->first()

# Check user verification status
>>> App\Models\User::find(1)->isVerified()

# View pending verifications
>>> App\Models\UserVerification::where('status', 'pending')->count()
```

## Future Enhancements

1. **Bulk Verification**: Allow admins to verify multiple users at once
2. **Verification Levels**: Different verification tiers (basic, premium, business)
3. **Auto-Verification**: Integrate with external ID verification services
4. **Verification Expiry**: Set expiration dates for verifications
5. **Document OCR**: Automatic data extraction from uploaded documents
6. **Mobile App Integration**: Verification request flow in mobile app
