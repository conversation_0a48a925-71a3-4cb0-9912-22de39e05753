# Database Notifications in Laravel

## Introduction

<PERSON><PERSON> provides a built-in notification system that supports various delivery channels, including database
notifications. Database notifications allow you to store notifications in the database and retrieve them later for
display in your application.

### Step-by-Step Guide

#### 1. Setting Up the Notifications Table

First, you need to create a migration for the notifications table. <PERSON><PERSON> provides a built-in command for this:

```bash
php artisan notifications:table
php artisan migrate
```

#### 2. Defining Notification Classes

Next, you need to define notification classes that extend the `Illuminate\Notifications\Notification` class. These
classes should define the `toDatabase` method to store the notification data in the database.

Here's an example of a notification class:

```
php artisan make:notification UserRegisteredNotification
```

```php
use Illuminate\Notifications\Notification;

class UserRegisteredNotification extends Notification
{
public function toDatabase($notifiable)
{
return [
'user_id' => $this->user->id,
'message' => 'A new user has registered: ' . $this->user->name,
'href' => '/users/' . $this->user->id,
];
}
}
```

#### 3. Sending Notifications

To send notifications, you can use the `notify` method on your notifiable models. For example:

```php
$user->notify(new UserRegisteredNotification($user));
or
Notification::send($admins, new UserRegisteredNotification(user));
```

#### 4. Retrieving Notifications

You can retrieve notifications for a user using the `notifications` method on the user model. For example:

```php
$notifications = $user->notifications;
```

#### 5. Marking Notifications as Read

To mark notifications as read, you can use the `markAsRead` method on the notification instance. For example:

```php
$notification->markAsRead();
```

#### 6. Displaying Notifications

You can display notifications in your application by iterating over the notifications collection and displaying the
notification data. For example:

```php
@foreach ($notifications as $notification)
    <a href="{{ $notification->data['href'] }}">{{ $notification->data['message'] }}</a>
@endforeach
```


## examples

- UserRegisteredListener 
- UserRegisteredNotification
- navbar-notification.blade.php
- profile/notification.blade.php
