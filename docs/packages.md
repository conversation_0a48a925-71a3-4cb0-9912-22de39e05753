
## Install Spatie data package

```bash
composer require spatie/laravel-data
```

This package allows you to define data objects in your Laravel app. A data object is a simple class that contains some data. It can be used to replace arrays where you would normally use them.

eg: RegisterUser.php & its usage in RegisterUserController.php

## Make Command

```bash
php artisan make:command GenerateData
php artisan make:command GenerateAction
```

<p>The command file will be located in the App\Console\Commands folder.</p>
<p> Create stub file for the command. App\Console\stubs\command.stub</p>
<p> Stubs are templates for the files that will be generated by the command. </p>

# DTO (Data Transfer Object) Command called GenerateData

<p>This command will generate a new data object class in the App\Data folder.</p>
eg: RegisterUserData.php

## Actions Command called GenerateAction

<p>This command will generate a new action class in the App\Actions folder.</p>
 eg: RegisterUserAction.php

## Create Resource

```bash
   php artisan make:resource UserResource
```

<p> This command will generate a new resource class in the App\Http\Resources folder.</p>
<p> Resource classes are responsible for transforming your models into JSON responses that can be returned from your API.</p>

## To remove the wrapping of the resource data on JSON response

```bash
     JsonResource::withoutWrapping();
```

in the boot method of AppServiceProvider.php

## Laravel Attribute Setter & Getter

<p> This new way to define getter and setter methods for attributes in Laravel </p>

```php
    public function name() : Attribute
    {
        return new Attribute(
            get: fn($value) => $this->first_name . ' ' . $this->last_name,
            set: fn($value) => [
                'first_name' => explode(' ', $value)[0],
                'last_name' => explode(' ', $value)[1],
            ]
        );
    }
```

## Laravel Hash Ids <a href="https://github.com/vinkla/laravel-hashids">Github link</a>

```bash
    composer require vinkla/hashids;
```

<p> This package allows you to generate Hashids for your models. </p>


## Make Trait

```bash
    php artisan make:trait HasHashId
```

<p> Traits are a way to share methods among classes in your application. </p>
<p> This trait will add a hash_id attribute to your models. </p>

eg: User.php && Trait HasHashId.php

change model stub file in App\Console\stubs\model.stub
to include the HasHashId trait and create hashId on boot method


## Publishing stubs
<p> If you want to customize the stubs that are used by the commands, you can publish them using the following command: </p>

```bash
    php artisan stub:publish
```

## Enable Verified Email
To enable email verification, you need to add the MustVerifyEmail interface to your User model.

```php
use Illuminate\Contracts\Auth\MustVerifyEmail;

class User extends Authenticatable implements MustVerifyEmail
{
    // ...
}
```

## Laravel adjacency link <a href="https://github.com/staudenmeir/laravel-adjacency-list">Github link</a>

```bash
    composer require staudenmeir/laravel-adjacency-list
```

<p>This Laravel Eloquent extension provides recursive relationships for trees and graphs using common table expressions (CTE).</p>
<p>Eg: Category.php can have subcategories  </p>

## Laravel Eloquent Sluggable <a href="composer require cviebrock/eloquent-sluggable">Github link</a>

```bash
    composer require cviebrock/eloquent-sluggable
```

<p>This package provides a simple way to create slugs for your Eloquent models.</p>
<p>The main feature of this package is that it automatically generates a unique slug for your model when it is created.</p>
<em>eg: Category.php</em>
<em>eg create slug for title name where there is multiple title name</em>
<em>The slug will be unique for each title name like name, name-1, name-2, name-3, etc.</em>

## Laravel Money package <a href="https://github.com/akaunting/laravel-money">Github link</a>

```bash
    composer require akaunting/laravel-money
```
<p>This package provides a simple way to work with money in Laravel applications.</p>


## Spatie Laravel Media Library <a href="https://spatie.be/docs/laravel-medialibrary/v11/installation-setup">link</a>

```bash
    composer require spatie/laravel-medialibrary
```
<p>This package can associate all sorts of files with Eloquent models. It provides a simple API to work with.</p>


##Customize Laravel Exception
<p>Customize the exception handler in Laravel to return JSON responses for exceptions.</p>
<p>e.g. App\Exceptions\Handler.php</p>
<p>to show handler file 


### Laravel Module approach <a href="https://nwidart.com/laravel-modules/v6/introduction">link</a>

```bash
    composer require nwidart/laravel-modules
```
<p>Modules are a way to organize your code in a Laravel application. They allow you to group related functionality together in a way that is easy to manage.</p>



### Api documentation generator knuckleswtf <a href="https://github.com/knuckleswtf/scribe">link</a>

```bash
    composer require --dev knuckleswtf/scribe
```
<p> Scribe is a Laravel package that automatically generates API documentation from your code. It uses your existing routes and controllers to create a beautiful, interactive API documentation site.</p>

<p> To generate the documentation, run the following command: </p>

```bash
    php artisan scribe:generate
```


## Spatie Laravel Permission <a href="https://spatie.be/docs/laravel-permission/v5/introduction">link</a>

```bash
    composer require spatie/laravel-permission
```
<p> This package provides a simple way to manage permissions in Laravel applications. It allows you to define roles and assign permissions to them. </p>

publish

```bash
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```


<p> To use the package, you need to add the HasRoles trait to your User model. </p>

```php
    use Spatie\Permission\Traits\HasRoles;

    class User extends Authenticatable
    {
        use HasRoles;
    }
```

<p> You can then use the provided methods to check if a user has a role or permission. </p>

```php
    $user->hasRole('admin');
    $user->can('edit articles');
```

<p> You can also define roles and permissions in the database using the provided Artisan commands. </p>

```bash
    php artisan permission:create-role admin
    php artisan permission:create-permission edit articles
```


