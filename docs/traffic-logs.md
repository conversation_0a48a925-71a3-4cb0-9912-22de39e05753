# Trafic logs

- traffic logs a feature that allows you to log all incoming requests . This feature is useful for debugging and
monitoring purposes. You can enable traffic logs by setting the `traffic_logs` field to `true` in your API
configuration.

## Setup

### Create new model
```php
php artisan make:model TrafficLog -m
php artisan make:model TrafficLogDetails -m
```

### Add fields to the migration file
```php
Schema::create('traffic_logs', function (Blueprint $table) {
$table->id();
$table->unsignedBigInteger('user_id')->nullable();
$table->foreign('user_id')->references('id')->on("users")->onDelete('cascade');
$table->string('traffic_landing')->nullable()->index();
$table->string('agent_name')->nullable()->index();
$table->string('browser')->nullable()->index();
$table->string('device')->nullable()->index();
$table->string('operating_system')->nullable()->index();
$table->string('code')->nullable()->index();
$table->string('country_code')->nullable()->index();
$table->string('country_name')->nullable()->index();
$table->string('note')->nullable();
});
```

### Add fields to the migration file
```php
Schema::create('traffic_log_details', function (Blueprint $table) {
        $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on("users")->onDelete('cascade');
            $table->unsignedBigInteger('traffic_logs_id')->nullable();
            $table->foreign('traffic_logs_id')->references('id')->on("rate_limits")->onDelete('cascade');
            $table->text('query')->nullable();
            $table->string('prev_link')->nullable()->index();
            $table->string('url')->nullable()->index();
            $table->string('domain')->nullable()->index();
            $table->string('ip')->index();
});
```


### Make middleware
```php
php artisan make:middleware TrafficLogMiddleware
```

### Add the following code to the `handle` method of the `TrafficLogMiddleware` class
```php
public function handle($request, Closure $next)
{
$trafficLog = new TrafficLog();
$trafficLog->user_id = auth()->id();
$trafficLog->traffic_landing = $request->header('referer');
$trafficLog->domain = $request->getHost();
$trafficLog->prev_link = $request->header('referer');
$trafficLog->ip = $request->ip();
$trafficLog->agent_name = $request->header('user-agent');
$trafficLog->browser = $request->header('user-agent');
$trafficLog->device = $request->header('user-agent');
$trafficLog->operating_system = $request->header('user-agent');
$trafficLog->code = $request->header('referer');
$trafficLog->country_code = $request->header('referer');
$trafficLog->country_name = $request->header('referer');
$trafficLog->query = json_encode($request->query());
$trafficLog->note = $request->header('referer');
$trafficLog->save();
return $next($request);
}
```

### Add the `traffic_logs` field to your API configuration
```php
'traffic_logs' => true,
```

## Usage

### Viewing traffic logs
You can view traffic logs by visiting the `/traffic-logs` route in your browser.


- This Traffic logs can help monitor the traffic of your API and help you debug any issues that may arise.
- Therefor we need to bond it with the appility to notify the system if the traffic is too high or under attack.
- And we can detect suspicious traffic and block it.

## UnderAttack model && BlockIps model
```php
php artisan make:model UnderAttack -m
php artisan make:model BlockIps -m
```

