## Packages

### nwidart/laravel-modules ![GitHub stars](https://img.shields.io/github/stars/nWidart/laravel-modules.svg?style=flat-square)

This package is a Laravel package which created to manage your large Laravel app using modules. Module is like a Laravel package, it has some views, controllers or models.

### opcodesio/log-viewer ![GitHub stars](https://img.shields.io/github/stars/opcodesio/log-viewer.svg?style=flat-square)

A Laravel package to view your log files through a web interface.


### spatie/laravel-medialibrary ![GitHub stars](https://img.shields.io/github/stars/spatie/laravel-medialibrary.svg?style=flat-square)

This package can associate all sorts of files with Eloquent models. It provides a simple, fluent API to work with.

### spatie/laravel-permission ![GitHub stars](https://img.shields.io/github/stars/spatie/laravel-permission.svg?style=flat-square)

This package allows you to manage user permissions and roles in a database.

### stevebauman/location ![GitHub stars](https://img.shields.io/github/stars/stevebauman/location.svg?style=flat-square)

This package allows you to get a user's location by their IP address.

### inkla/hashids ![GitHub stars](https://img.shields.io/github/stars/vinkla/laravel-hashids.svg?style=flat-square)

This package allows you to generate Hashids for your models.

## Dev Packages

### barryvdh/laravel-debugbar ![GitHub stars](https://img.shields.io/github/stars/barryvdh/laravel-debugbar.svg?style=flat-square)

This package provides a developer toolbar for debugging the PHP and Laravel code of your app.

