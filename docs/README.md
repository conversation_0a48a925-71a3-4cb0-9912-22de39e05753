# Haragy - Marketplace Application

Haragy is a modern marketplace application built with Laravel, featuring real-time chat, user tracking, and a modular architecture. This application supports bilingual content (Arabic/English), dark/light mode, and a responsive design using Tailwind CSS with DaisyUI.

## Features

- **Marketplace Functionality**: List, search, and manage products
- **Real-time Chat**: Communication between users with support for text, audio, image, and video messages
- **User Authentication**: Email/password authentication with email verification
- **User Profiles**: Edit profile data, manage ads, display favorites, followers, and following
- **User Rating System**: Rate other users with 1-5 stars and optional comments
- **AI Product Review**: Automated review of product listings using Gemini AI
- **Bilingual Support**: Full support for Arabic and English languages
- **Dark/Light Mode**: Theme switching for better user experience
- **Responsive Design**: Mobile-friendly interface using Tailwind CSS and DaisyUI

## Technology Stack

- **Backend**: Laravel 12.x
- **Frontend**: Tailwind CSS, DaisyUI, Alpine.js
- **Database**: MySQL/SQLite
- **Caching**: Redis
- **Real-time**: <PERSON><PERSON> Reverb, <PERSON><PERSON> Echo
- **External Services**: Firebase (push notifications), Telegram <PERSON> (admin notifications)
- **Monitoring**: <PERSON><PERSON> Telescope, Lara<PERSON> Pulse
- **Modules**: Laravel Modules for modular architecture

## Documentation

Comprehensive documentation is available in the `docs` directory:

- [Deployment Guide](deployment.md): Instructions for deploying the application to a production server
- [Tools Documentation](tools-documentation.md): Overview of all tools and packages used in the application
- [Chat & Real-time Features](chat-realtime-documentation.md): Detailed documentation of the chat functionality
- [Project Structure](project-structure.md): Overview of the codebase organization

Additional documentation:

- [Installation Guide](installation.md): Instructions for setting up the development environment
- [Packages Documentation](packages.md): Information about the packages used in the application
- [Reverb Setup](reverb.md): Guide for setting up Laravel Reverb for real-time features

## Installation

### Prerequisites

- PHP 8.2 or higher
- Composer 2.x
- Node.js 18.x or higher and NPM
- MySQL 8.0 or higher (or SQLite)
- Redis (optional, for caching and broadcasting)

### Development Setup

1. Clone the repository:

   ```bash
   git clone https://github.com/your-repo/haragy_backend.git
   cd haragy_backend
   ```

2. Install PHP dependencies:

   ```bash
   composer install
   ```

3. Install Node.js dependencies:

   ```bash
   npm install
   ```

4. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

5. Generate application key:

   ```bash
   php artisan key:generate
   ```

6. Configure your database in the `.env` file:

   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=haragy
   DB_USERNAME=root
   DB_PASSWORD=
   ```

7. Run migrations:

   ```bash
   php artisan migrate --seed
   ```

8. Start the development server:

   ```bash
   php artisan serve
   ```

9. In a separate terminal, compile assets:

   ```bash
   npm run dev
   ```

10. Publish chat assets:

    ```bash
    php publish-chat-assets.php
    ```

11. Start the Reverb server for real-time features:

    ```bash
    php artisan reverb:start
    ```

### Production Deployment

For production deployment, refer to the [Deployment Guide](deployment.md).

## Modules

The application is organized into modules:

- **Chat**: Real-time messaging functionality
- **UserTracker**: Track user activities and online presence
- **TrafficLogs**: Log traffic to the application
- **Todo**: Task management functionality

## Testing

### Running Tests

```bash
php artisan test
```

### Testing Real-time Features

Use the provided test scripts:

```bash
# Test channel authorization
php test-channel-auth.php [chat_id] [user_id]

# Test broadcasting a message
php test-chat-broadcast.php [chat_id] [user_id]
```

## Configuration

### Broadcasting

Configure broadcasting in your `.env` file:

```env
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your-app-id
REVERB_APP_SECRET=your-app-secret
REVERB_APP_KEY=your-app-key
REVERB_HOST=your-domain.com
REVERB_PORT=443
REVERB_SCHEME=https
```

### Firebase

Configure Firebase for push notifications:

```env
FIREBASE_CREDENTIALS=config/firebase_credentials.json
```

### Telegram Bot

Configure Telegram for admin notifications:

```env
TELEGRAM_BOT_TOKEN=your-bot-token
TELEGRAM_ERROR_BOT_TOKEN=your-error-bot-token
TELEGRAM_ERROR_BOT_CHAT_ID=your-chat-id
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Laravel](https://laravel.com/)
- [Tailwind CSS](https://tailwindcss.com/)
- [DaisyUI](https://daisyui.com/)
- [Laravel Modules](https://github.com/nWidart/laravel-modules)
- [Laravel Reverb](https://laravel.com/docs/10.x/reverb)
- [Laravel Echo](https://laravel.com/docs/10.x/broadcasting)
- [Firebase](https://firebase.google.com/)
- [Telegram Bot SDK](https://github.com/irazasyed/telegram-bot-sdk)
