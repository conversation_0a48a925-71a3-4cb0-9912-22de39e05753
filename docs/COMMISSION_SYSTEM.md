# Commission and Achievement System Documentation

## Overview

The Commission and Achievement System allows users to pay a 1% commission for sold products and earn achievements based on their commission payment activity. The system includes admin review functionality, user notifications, and comprehensive dashboard analytics.

## Features

### 🏆 Commission System
- **1% Commission Rate**: Users pay 1% of the product price as commission
- **Multiple Payment Methods**:
  - Mobile Wallet (requires proof)
  - Bank Transfer (requires proof)
  - Online Payment (auto-verified)
- **Admin Review Process**: Manual payments require admin approval
- **Payment Proof Upload**: Users can upload payment screenshots
- **Status Tracking**: Pending, Approved, Rejected statuses

### 🎯 Achievement System
- **First Commission Paid**: Earned on the first payment of the year
- **Loyal Seller**: 3+ commissions in the same year
- **High Volume Seller**: 5+ commissions in the same year
- **Professional Seller**: 10+ commissions in the same year
- **Diamond Seller**: 20+ commissions in the same year

### 📊 Dashboard Analytics
- Total commission requests this year
- Approved vs. rejected breakdown
- Total revenue from commissions
- Top commission-paying users
- Latest pending commission requests

### 🔔 Notifications
- Commission approval notifications
- Commission rejection notifications (with reason)
- Achievement unlock notifications

## Database Schema

### Commissions Table
```sql
CREATE TABLE commissions (
    id BIGINT PRIMARY KEY,
    user_id BIGINT FOREIGN KEY,
    product_id BIGINT FOREIGN KEY,
    amount DECIMAL(10,2),
    payment_method ENUM('wallet', 'bank_transfer', 'online_payment'),
    payment_proof VARCHAR(255) NULLABLE,
    notes TEXT NULLABLE,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    admin_id BIGINT FOREIGN KEY NULLABLE,
    review_notes TEXT NULLABLE,
    reviewed_at TIMESTAMP NULLABLE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## API Endpoints

### Admin Routes
- `GET /admin/commissions` - List all commissions with filters
- `GET /admin/commissions/{id}` - View commission details
- `POST /admin/commissions/{id}/approve` - Approve commission
- `POST /admin/commissions/{id}/reject` - Reject commission

### User Routes
- `GET /commissions` - User's commission history
- `GET /commissions/product/{product}/create` - Create commission form
- `POST /commissions/product/{product}` - Submit commission
- `GET /commissions/{commission}` - View commission details
- `PUT /commissions/{commission}` - Update pending commission
- `DELETE /commissions/{commission}` - Delete pending commission

## Usage Examples

### Creating a Commission
```php
use App\Services\CommissionService;
use App\Enums\PaymentMethodEnum;

$commissionService = new CommissionService();

$commission = $commissionService->createCommission(
    $user,
    $product,
    PaymentMethodEnum::WALLET,
    $paymentProofFile,
    'Payment completed via STC Pay'
);
```

### Approving a Commission
```php
$commissionService->approveCommission(
    $commission,
    $admin,
    'Payment verified successfully'
);
```

### Getting Commission Statistics
```php
$stats = $commissionService->getCommissionStats(2025);
// Returns: total_commissions, approved_commissions, total_revenue, etc.
```

## Configuration

### Payment Methods
Configure payment methods in `app/Enums/PaymentMethodEnum.php`:
- Add new payment methods
- Configure which methods require proof
- Set auto-verification rules

### Achievement Thresholds
Modify achievement thresholds in `app/Services/CommissionService.php`:
```php
// Award "Loyal Seller" achievement (3+ commissions)
if ($approvedCommissions >= 3) {
    $this->awardAchievement($user, 'loyal_seller');
}
```

### Commission Rate
Change the commission rate in `app/Models/Commission.php`:
```php
public static function calculateCommission(Product $product): float
{
    return round($product->price * 0.01, 2); // 1% commission
}
```

## File Storage

### Payment Proof Files
- Stored in: `storage/app/public/commissions/`
- Naming convention: `{timestamp}_{user_id}_{original_name}`
- Supported formats: JPEG, PNG, JPG, GIF
- Maximum size: 2MB

## Permissions

### Admin Permissions
- `commission_read` - View commissions
- `commission_update` - Approve/reject commissions
- `commission_delete` - Delete commissions

### User Permissions
- Users can only manage their own commissions
- Can edit/delete only pending commissions
- Can view all their commission history

## Notifications

### Email Notifications
- Commission approved: `CommissionApprovedNotification`
- Commission rejected: `CommissionRejectedNotification`

### Database Notifications
- Stored in `notifications` table
- Include commission details and links
- Support for real-time updates

## Dashboard Integration

### Commission Statistics Card
```php
// In DashboardService
$commissionStats = $this->getCommissionStats();
```

### Quick Stats
- Pending commissions count
- Total revenue this year
- Top commission users
- Latest pending requests

## Testing

### Running Tests
```bash
# Test commission calculation
php artisan tinker --execute="
use App\Models\Commission;
use App\Models\Product;
\$product = Product::first();
echo Commission::calculateCommission(\$product);
"

# Test commission creation
php artisan tinker --execute="
use App\Services\CommissionService;
\$service = new CommissionService();
// Test commission creation logic
"
```

### Sample Data
```bash
# Seed commission achievements
php artisan db:seed --class=CommissionAchievementSeeder
```

## Troubleshooting

### Common Issues

1. **Payment proof not uploading**
   - Check file permissions on `storage/app/public/commissions/`
   - Verify file size limits in `php.ini`

2. **Achievements not awarding**
   - Ensure achievement records exist in database
   - Check achievement slug names match service logic

3. **Notifications not sending**
   - Verify queue workers are running
   - Check email configuration

### Debug Commands
```bash
# Check commission statistics
php artisan tinker --execute="use App\Services\CommissionService; (new CommissionService())->getCommissionStats();"

# List all achievements
php artisan tinker --execute="use App\Models\Achievement; Achievement::where('role', 'seller')->get();"
```

## Security Considerations

- File upload validation for payment proofs
- Authorization policies for commission access
- CSRF protection on all forms
- Input sanitization for notes and review comments
- Rate limiting on commission submissions

## Future Enhancements

- Bulk commission processing
- Commission payment reminders
- Advanced reporting and analytics
- Integration with payment gateways
- Mobile app support
- Commission dispute system
