#Spatie Quick guide spatie/laravel-permission

Assigning Permissions to a User
```php
$user->givePermissionTo('edit articles');
```

Checking for Permissions
```php
$user->hasPermissionTo('edit articles');
```

Removing Permissions
```php
$user->revokePermissionTo('edit articles');
```

Syncing Permissions
sync permissions will revoke all permissions that are not in the given array.
```php
$user->syncPermissions(['edit articles', 'delete articles']);
```

Assigning Roles to a User
```php
$user->assignRole('writer');
```

Checking for Roles
```php
$user->hasRole('writer');
```

Removing Roles
```php
$user->removeRole('writer');
```

Syncing Roles
```php
$user->syncRoles(['writer', 'admin']);
```

#Spatie Quick guide spatie/laravel-medialibrary

Adding Media to a Model
```php
$newsItem->addMedia($pathToFile)->toMediaCollection('images');
```

Retrieving Media
```php
$newsItem->getMedia('images');
```

Removing Media
```php
$newsItem->clearMediaCollection('images');
```

adding multiple files
```php
$newsItem->addMultipleMediaFromRequest(['images'])
    ->each(function ($fileAdder) {
        $fileAdder->toMediaCollection('images');
    });
```

add media thumbnail
```php
$newsItem->addMedia($pathToFile)
    ->preservingOriginal()
    ->withResponsiveImages()
    ->toMediaCollection('images');
```
