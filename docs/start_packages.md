
## Laravel Hash Ids <a href="https://github.com/vinkla/laravel-hashids">Github link</a>

```bash
    composer require vinkla/hashids;
```

published config file

```bash
    php artisan vendor:publish --provider="Vinkla\Hashids\HashidsServiceProvider"
```

<p> This package allows you to generate Hashids for your models. </p>


## Make Trait

```bash
    php artisan make:trait HasHashId
```

<p> Traits are a way to share methods among classes in your application. </p>
<p> This trait will add a hash_id attribute to your models. </p>

eg: User.php && Trait HasHashId.php

change model stub file in App\Console\stubs\model.stub
to include the HasHashId trait and create hashId on boot method



## Spatie Laravel Media Library <a href="https://spatie.be/docs/laravel-medialibrary/v11/installation-setup">link</a>

```bash
    composer require spatie/laravel-medialibrary
```
<p>This package can associate all sorts of files with Eloquent models. It provides a simple API to work with.</p>


## Spatie Laravel Permission <a href="https://spatie.be/docs/laravel-permission/v5/introduction">link</a>

```bash
    composer require spatie/laravel-permission
```
<p> This package provides a simple way to manage permissions in Laravel applications. It allows you to define roles and assign permissions to them. </p>

publish

```bash
php artisan vendor:publish --provider="Spatie\Permission\PermissionServiceProvider"
```


<p> To use the package, you need to add the HasRoles trait to your User model. </p>

```php
    use Spatie\Permission\Traits\HasRoles;

    class User extends Authenticatable
    {
        use HasRoles;
    }
```

<p> You can then use the provided methods to check if a user has a role or permission. </p>

```php
    $user->hasRole('admin');
    $user->can('edit articles');
```

<p> You can also define roles and permissions in the database using the provided Artisan commands. </p>

```bash
    php artisan permission:create-role admin
    php artisan permission:create-permission edit articles
```
