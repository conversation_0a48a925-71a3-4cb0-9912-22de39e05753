# Haragy Application Tools Documentation

This document provides a comprehensive overview of all tools and packages used in the Haragy application, including their purpose, configuration requirements, and usage instructions.

## Table of Contents

1. [Core Framework](#core-framework)
2. [Frontend Tools](#frontend-tools)
3. [Database and Storage](#database-and-storage)
4. [Authentication and Authorization](#authentication-and-authorization)
5. [Real-time Communication](#real-time-communication)
6. [External Services](#external-services)
7. [Development Tools](#development-tools)
8. [Modules System](#modules-system)
9. [Monitoring and Logging](#monitoring-and-logging)
10. [Media Processing](#media-processing)

## Core Framework

### Laravel Framework (v12.x)

The application is built on Laravel 12, a PHP web application framework with expressive, elegant syntax.

**Configuration:**
- Main configuration files are in the `config/` directory
- Environment-specific configuration in `.env` file

**Key Components:**
- Routing (`routes/` directory)
- Controllers (`app/Http/Controllers/`)
- Models (`app/Models/`)
- Views (`resources/views/`)
- Middleware (`app/Http/Middleware/`)

## Frontend Tools

### Vite

Used for frontend asset bundling and development server.

**Configuration:**
- Main configuration in `vite.config.js`
- Module-specific configurations in `Modules/*/vite.config.js`

**Usage:**
```bash
# Development
npm run dev

# Production build
npm run build
```

### Tailwind CSS with DaisyUI

Used for styling the application with utility-first CSS and component library.

**Configuration:**
- Main configuration in `tailwind.config.js`

**Features:**
- Utility-first CSS framework
- DaisyUI component library
- Dark/light mode support
- Responsive design utilities

### Alpine.js

Used for adding JavaScript behavior to the frontend.

**Usage:**
- Included in the application's JavaScript bundle
- Used for interactive components

## Database and Storage

### MySQL/SQLite

The application supports multiple database systems, with MySQL recommended for production.

**Configuration:**
- Database configuration in `config/database.php`
- Environment-specific settings in `.env`

### Redis

Used for caching, session storage, and queue processing.

**Configuration:**
- Redis configuration in `config/database.php` (cache section)
- Environment-specific settings in `.env`

## Authentication and Authorization

### Laravel Sanctum

API token authentication for the application.

**Configuration:**
- Configuration in `config/sanctum.php`

**Usage:**
- Used for API authentication
- Provides token-based authentication

### Spatie Laravel Permission

Role and permission management for the application.

**Configuration:**
- Configuration in `config/permission.php`

**Usage:**
- Define roles and permissions
- Assign roles to users
- Check permissions in controllers and views

## Real-time Communication

### Laravel Reverb

WebSocket server for real-time communication.

**Configuration:**
- Configuration in `config/reverb.php`
- Environment-specific settings in `.env`

**Usage:**
```bash
# Start the Reverb server
php artisan reverb:start
```

### Laravel Echo

JavaScript library for subscribing to channels and listening to events.

**Configuration:**
- Configuration in `resources/js/echo.js`

**Usage:**
```javascript
// Subscribe to a private channel
Echo.private(`chat.${chatId}`)
    .listen('NewMessageEvent', (e) => {
        // Handle new message
    });
```

### Chat Module

Custom module for real-time messaging between users.

**Configuration:**
- Module configuration in `Modules/Chat/`
- Channel authorization in `Modules/Chat/routes/channels.php`

**Setup:**
```bash
# Publish chat assets
php publish-chat-assets.php

# Run migrations
php artisan module:migrate Chat
```

## External Services

### Firebase

Used for push notifications to mobile devices.

**Configuration:**
- Credentials file at `config/firebase_credentials.json`
- Service implementation in `app/Http/Controllers/FirebaseController.php`

**Usage:**
```php
// Send a notification
$firebaseController->send(
    $title,
    $body,
    $relatedId,
    $relatedType,
    $fcmTokens
);
```

### Telegram Bot

Used for error notifications and admin alerts.

**Configuration:**
- Configuration in `config/telegram.php`
- Environment-specific settings in `.env`

**Usage:**
```php
// Send a message
Telegram::bot('error_bot')->sendMessage([
    'chat_id' => config('telegram.bots.error_bot.chat_id'),
    'text' => $message,
]);
```

## Development Tools

### Laravel Telescope

Debug assistant for the Laravel framework.

**Configuration:**
- Configuration in `config/telescope.php`

**Usage:**
- Access at `/telescope` in development environment

### Laravel Pulse

Real-time application performance monitoring.

**Configuration:**
- Configuration in `config/pulse.php`

**Usage:**
- Access at `/pulse` in development environment

### Laravel Pint

PHP code style fixer.

**Usage:**
```bash
# Format code
./vendor/bin/pint
```

## Modules System

### Laravel Modules

Used for organizing the application into modules.

**Configuration:**
- Configuration in `config/modules.php`

**Available Modules:**
- Chat: Real-time messaging
- UserTracker: Track user activities
- TrafficLogs: Log traffic to the application
- Todo: Task management

**Usage:**
```bash
# Create a new module
php artisan module:make ModuleName

# Enable a module
php artisan module:enable ModuleName

# Disable a module
php artisan module:disable ModuleName
```

## Monitoring and Logging

### Log Viewer

Web interface for viewing application logs.

**Configuration:**
- Configuration in `config/log-viewer.php`

**Usage:**
- Access at `/log-viewer` in development environment

### Laravel Activity Log

Log user activities in the application.

**Configuration:**
- Configuration in `config/activitylog.php`

**Usage:**
```php
// Log an activity
activity()
    ->causedBy($user)
    ->performedOn($model)
    ->withProperties(['key' => 'value'])
    ->log('Look, I logged something');
```

## Media Processing

### Intervention Image

Image manipulation library.

**Usage:**
```php
// Resize an image
$image = Image::make($request->file('image'))
    ->resize(300, 200)
    ->save(storage_path('app/public/images/' . $filename));
```

### PHP-FFmpeg

Video and audio processing library.

**Usage:**
```php
// Convert a video
$ffmpeg = FFMpeg\FFMpeg::create();
$video = $ffmpeg->open($videoPath);
$video
    ->filters()
    ->resize(new FFMpeg\Coordinate\Dimension(320, 240))
    ->synchronize();
$video
    ->save(new FFMpeg\Format\Video\X264(), $outputPath);
```

## Additional Tools

### Laravel Hashids

Generate short, unique, non-sequential IDs from numbers.

**Configuration:**
- Configuration in `config/hashids.php`

**Usage:**
```php
// Encode an ID
$hashid = Hashids::encode($id);

// Decode a hash
$id = Hashids::decode($hashid);
```

### Laravel Sluggable

Generate slugs for your Eloquent models.

**Usage:**
```php
// Model configuration
public function getSlugOptions() : SlugOptions
{
    return SlugOptions::create()
        ->generateSlugsFrom('name')
        ->saveSlugsTo('slug');
}
```

### Laravel Sitemap

Generate sitemaps for the application.

**Usage:**
```php
// Generate a sitemap
SitemapGenerator::create(config('app.url'))
    ->writeToFile(public_path('sitemap.xml'));
```
