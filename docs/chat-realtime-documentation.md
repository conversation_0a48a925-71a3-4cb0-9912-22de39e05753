# Chat and Real-time Features Documentation

This document provides detailed information about the chat functionality and real-time features in the Haragy application, including setup instructions, configuration requirements, and troubleshooting tips.

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Server-Side Setup](#server-side-setup)
4. [Client-Side Setup](#client-side-setup)
5. [Publishing Chat Assets](#publishing-chat-assets)
6. [Channel Authorization](#channel-authorization)
7. [Events and Listeners](#events-and-listeners)
8. [Testing Real-time Features](#testing-real-time-features)
9. [Troubleshooting](#troubleshooting)
10. [Production Deployment](#production-deployment)

## Overview

The Haragy application includes a real-time chat system that allows users to communicate with each other. The chat system supports:

- One-to-one messaging
- Text, audio, image, and video messages
- Real-time message delivery
- Message read status tracking
- Optional product context for marketplace conversations

## Architecture

The real-time features are built using:

- **<PERSON>vel Reverb**: WebSocket server for real-time communication
- **<PERSON><PERSON> Echo**: JavaScript client for WebSocket communication
- **Chat <PERSON>dule**: Custom module for chat functionality
- **Event Broadcasting**: <PERSON><PERSON>'s event broadcasting system

### Flow of Communication

1. User sends a message through the web or API interface
2. Server stores the message in the database
3. Server broadcasts an event with the message data
4. WebSocket server pushes the event to subscribed clients
5. Client-side Echo listeners receive the event and update the UI

## Server-Side Setup

### 1. Configure Broadcasting

In your `.env` file:

```
BROADCAST_DRIVER=reverb
REVERB_APP_ID=your-app-id
REVERB_APP_SECRET=your-app-secret
REVERB_APP_KEY=your-app-key
REVERB_HOST=your-domain.com
REVERB_PORT=443
REVERB_SCHEME=https

REVERB_SERVER_HOST=0.0.0.0
REVERB_SERVER_PORT=8080
```

### 2. Configure Reverb Server

In `config/reverb.php`:

```php
'apps' => [
    'provider' => 'config',
    'apps' => [
        [
            'key' => env('REVERB_APP_KEY'),
            'secret' => env('REVERB_APP_SECRET'),
            'app_id' => env('REVERB_APP_ID'),
            'options' => [
                'host' => env('REVERB_HOST'),
                'port' => env('REVERB_PORT', 443),
                'scheme' => env('REVERB_SCHEME', 'https'),
                'useTLS' => env('REVERB_SCHEME', 'https') === 'https',
            ],
            'allowed_origins' => ['*'],
        ],
    ],
],
```

### 3. Start Reverb Server

For development:

```bash
php artisan reverb:start
```

For production, use a process manager like Supervisor:

```
[program:reverb]
process_name=%(program_name)s
command=php /path/to/your/app/artisan reverb:start
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/path/to/your/app/storage/logs/reverb.log
stopwaitsecs=3600
```

## Client-Side Setup

### 1. Configure Laravel Echo

In `resources/js/echo.js`:

```javascript
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';
window.Pusher = Pusher;
window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});
```

### 2. Configure Environment Variables

In your `.env` file:

```
VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="${REVERB_HOST}"
VITE_REVERB_PORT="${REVERB_PORT}"
VITE_REVERB_SCHEME="${REVERB_SCHEME}"
```

### 3. Include Echo in Your Application

Make sure to include the Echo configuration in your main JavaScript file:

```javascript
import './echo';
```

## Publishing Chat Assets

The Chat module includes JavaScript files that need to be published to the public directory. Use the provided script:

```bash
php publish-chat-assets.php
```

This script copies the `chat.js` file from `Modules/Chat/resources/js/chat.js` to `public/modules/chat/js/chat.js`.

## Channel Authorization

Chat channels are private and require authorization. The authorization logic is defined in `Modules/Chat/routes/channels.php`:

```php
Broadcast::channel('chat.{chatId}', function ($user, $chatId) {
    $chat = \Modules\Chat\app\Models\Chat::find($chatId);
    if (!$chat) {
        return false;
    }
    
    return $user->id === $chat->sender_id || $user->id === $chat->receiver_id;
});
```

This ensures that only the sender and receiver of a chat can subscribe to its channel.

## Events and Listeners

### NewMessageEvent

This event is dispatched when a new message is sent:

```php
// In ChatController.php
event(new NewMessageEvent($message));
```

The event class is defined in `Modules/Chat/app/Events/NewMessageEvent.php` and implements `ShouldBroadcast` to enable broadcasting.

### Client-Side Listening

To listen for new messages on the client side:

```javascript
Echo.private(`chat.${chatId}`)
    .listen('NewMessageEvent', (e) => {
        // Handle new message
        console.log(e.message);
    });
```

## Testing Real-time Features

### Using the Test Scripts

The application includes test scripts to help debug broadcasting:

#### 1. Test Channel Authorization

```bash
php test-channel-auth.php [chat_id] [user_id]
```

#### 2. Test Broadcasting a Message

```bash
php test-chat-broadcast.php [chat_id] [user_id]
```

### Manual Testing

1. Open two browser windows
2. Log in as different users in each window
3. Navigate to the same chat conversation in both windows
4. Send a message from one window
5. Verify the message appears in the other window without refreshing

## Troubleshooting

### Common Issues

#### 1. Echo is not initialized

Check browser console for errors. Make sure Echo is properly initialized before accessing it.

#### 2. Channel authorization fails

Check if the user is authenticated and has permission to access the channel. Look at the Laravel logs for authorization errors.

#### 3. No real-time updates

Verify that the event is being dispatched and that it implements the `ShouldBroadcast` interface.

#### 4. WebSocket connection fails

Check firewall settings and ensure the WebSocket server is running.

### Debugging Tips

1. Enable debug mode in Echo:

```javascript
window.Echo = new Echo({
    // Other options...
    debug: true,
});
```

2. Check Laravel logs:

```bash
tail -f storage/logs/laravel.log
```

3. Check Reverb logs:

```bash
tail -f storage/logs/reverb.log
```

4. Test WebSocket connection:

```javascript
// In browser console
window.Echo.connector.socket.connected
```

## Production Deployment

For production deployment:

1. Ensure Reverb server is running as a background service
2. Configure proper SSL for WebSocket connections
3. Set up a process manager like Supervisor to keep the Reverb server running
4. Build and minify frontend assets:

```bash
npm run build
php publish-chat-assets.php
```

5. Test the chat functionality in the production environment
6. Monitor WebSocket connections and server performance
