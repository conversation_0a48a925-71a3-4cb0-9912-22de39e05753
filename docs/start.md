
## Publishing stubs
<p> If you want to customize the stubs that are used by the commands, you can publish them using the following command: </p>

```bash
    php artisan stub:publish
```

## Make Trait

```bash
    php artisan make:trait HasHashId
```

<p> Traits are a way to share methods among classes in your application. </p>
<p> This trait will add a hash_id attribute to your models. </p>

eg: User.php && Trait HasHashId.php

change model stub file in App\Console\stubs\model.stub
to include the HasHashId trait and create hashId on boot method
