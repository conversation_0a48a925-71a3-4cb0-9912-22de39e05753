<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AppNotification>
 */
class AppNotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->sentence(),
            'body' => fake()->paragraph(),
            'type' => fake()->randomElement(['system', 'user', 'promotion']),
            'data' => json_encode(['key' => fake()->word(), 'value' => fake()->word()]),
            'sent_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'platform' => fake()->randomElement(['android', 'ios', 'all']),
        ];
    }

    /**
     * Define the notification as a system notification
     */
    public function asSystemNotification(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'system',
            ];
        });
    }

    /**
     * Define the notification as a user notification
     */
    public function asUserNotification(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'type' => 'user',
            ];
        });
    }
} 