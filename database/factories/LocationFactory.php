<?php

namespace Database\Factories;

use App\Models\Location;
use Illuminate\Database\Eloquent\Factories\Factory;

class LocationFactory extends Factory
{
    protected $model = Location::class;

    public function definition(): array
    {
        // Arabic locations
        $locations = ['الرياض', 'جدة', 'الدمام', 'الخبر', 'المدينة المنورة', 'الطائف', 'القصيم', 'تبوك', 'حائل', 'الأحساء'];

        $cities = ['الرياض', 'جدة', 'الدمام', 'الخبر', 'المدينة المنورة', 'الطائف', 'القصيم', 'تبوك', 'حائل', 'الأحساء'];

        return [
            'name' => $this->faker->randomElement($cities),
            'slug' => $this->faker->slug,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'city' => $this->faker->randomElement($cities),
            'state' => $this->faker->state,
            'country' => 'المملكة العربية السعودية',
        ];
    }
}
