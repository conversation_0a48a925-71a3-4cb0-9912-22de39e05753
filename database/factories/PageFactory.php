<?php

namespace Database\Factories;

use App\Models\Page;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PageFactory extends Factory
{
    protected $model = Page::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'content' => "<div class='content-section'><h2>" . $this->faker->sentence() . "</h2><p>" . $this->faker->paragraph() . "</p><h3>" . $this->faker->sentence() . "</h3><p>" . $this->faker->paragraph() . "</p><ul><li>" . $this->faker->sentence() . "</li><li>" . $this->faker->sentence() . "</li></ul></div>",
            'status' => true,
            'slug' => function (array $attributes) {
                return Str::slug($attributes['title']);
            },
        ];
    }

    public function about(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'title' => 'About Us',
                'content' => '<div class="about-section">
                    <h1>Welcome to Our Company</h1>
                    <p>We are dedicated to providing the best service to our customers. With years of experience and a passionate team, we strive to exceed expectations in everything we do.</p>
                    
                    <h2>Our Mission</h2>
                    <p>To create value through innovative solutions and exceptional customer service, while maintaining the highest standards of quality and reliability.</p>
                    
                    <h2>Our Values</h2>
                    <ul>
                        <li><strong>Innovation:</strong> We continuously seek new ways to improve and evolve.</li>
                        <li><strong>Integrity:</strong> We conduct our business with the highest ethical standards.</li>
                        <li><strong>Excellence:</strong> We strive for excellence in everything we do.</li>
                        <li><strong>Customer Focus:</strong> Our customers\' success is our success.</li>
                    </ul>
                    
                    <h2>Our Team</h2>
                    <p>Our diverse team of professionals brings together expertise from various fields, ensuring we can tackle any challenge with confidence and creativity.</p>
                    
                    <div class="contact-info">
                        <h3>Get in Touch</h3>
                        <p>We\'d love to hear from you! Reach out to us through our contact page or visit our offices.</p>
                    </div>
                </div>',
                'slug' => 'about-us',
            ];
        });
    }

    public function policy(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'title' => 'Privacy Policy',
                'content' => '<div class="policy-section">
                    <h1>Privacy Policy</h1>
                    <p>Last updated: ' . now()->format('F d, Y') . '</p>
                    
                    <section class="policy-content">
                        <h2>1. Information We Collect</h2>
                        <p>We collect information that you provide directly to us, including:</p>
                        <ul>
                            <li>Personal identification information (Name, email address, phone number)</li>
                            <li>Usage data and preferences</li>
                            <li>Communication history and interactions</li>
                        </ul>
                        
                        <h2>2. How We Use Your Information</h2>
                        <p>Your information helps us to:</p>
                        <ul>
                            <li>Provide, maintain, and improve our services</li>
                            <li>Process your transactions</li>
                            <li>Send you important updates and communications</li>
                            <li>Protect against fraudulent or illegal activity</li>
                        </ul>
                        
                        <h2>3. Data Protection</h2>
                        <p>We implement robust security measures to protect your personal information, including:</p>
                        <ul>
                            <li>Encryption of sensitive data</li>
                            <li>Regular security assessments</li>
                            <li>Restricted access to personal information</li>
                        </ul>
                        
                        <h2>4. Your Rights</h2>
                        <p>You have the right to:</p>
                        <ul>
                            <li>Access your personal data</li>
                            <li>Request corrections to your data</li>
                            <li>Request deletion of your data</li>
                            <li>Opt-out of marketing communications</li>
                        </ul>
                    </section>
                </div>',
                'slug' => 'privacy-policy',
            ];
        });
    }

    public function support(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'title' => 'Support',
                'content' => '<div class="support-section">
                    <h1>Customer Support Center</h1>
                    
                    <section class="contact-options">
                        <h2>Contact Us</h2>
                        <div class="contact-methods">
                            <div class="contact-card">
                                <h3>Email Support</h3>
                                <p>Email us at: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                                <p>Response time: Within 24 hours</p>
                            </div>
                            
                            <div class="contact-card">
                                <h3>Phone Support</h3>
                                <p>Call us: <strong>1-800-SUPPORT</strong></p>
                                <p>Hours: Monday - Friday, 9:00 AM - 6:00 PM EST</p>
                            </div>
                        </div>
                    </section>
                    
                    <section class="faq">
                        <h2>Frequently Asked Questions</h2>
                        <div class="faq-item">
                            <h3>How do I reset my password?</h3>
                            <p>You can reset your password by clicking the "Forgot Password" link on the login page and following the instructions sent to your email.</p>
                        </div>
                        
                        <div class="faq-item">
                            <h3>What payment methods do you accept?</h3>
                            <p>We accept all major credit cards, PayPal, and bank transfers for your convenience.</p>
                        </div>
                        
                        <div class="faq-item">
                            <h3>How can I track my order?</h3>
                            <p>Once your order is shipped, you will receive a tracking number via email to monitor your delivery status.</p>
                        </div>
                    </section>
                    
                    <section class="support-resources">
                        <h2>Additional Resources</h2>
                        <ul>
                            <li><a href="/documentation">Documentation</a></li>
                            <li><a href="/tutorials">Video Tutorials</a></li>
                            <li><a href="/community">Community Forum</a></li>
                        </ul>
                    </section>
                </div>',
                'slug' => 'support',
            ];
        });
    }
}