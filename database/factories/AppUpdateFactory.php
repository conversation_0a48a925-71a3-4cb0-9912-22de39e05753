<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AppUpdate>
 */
class AppUpdateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'platform' => fake()->randomElement(['android', 'ios']),
            'version' => '1.0.0',
            'message' => fake()->sentence(),
            'update_message' => fake()->paragraph(),
            'banner_message' => fake()->sentence(),
            'release_date' => fake()->dateTimeBetween('-1 month', '+1 month'),
            'force_update' => fake()->boolean(20), // 20% chance of being true
            'show_banner_message' => fake()->boolean(30), // 30% chance of being true
        ];
    }

    /**
     * Define the update as a forced update
     */
    public function asForceUpdate(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'force_update' => true,
            ];
        });
    }

    /**
     * Define the update for Android platform
     */
    public function forAndroid(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'platform' => 'android',
            ];
        });
    }

    /**
     * Define the update for iOS platform
     */
    public function forIOS(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'platform' => 'ios',
            ];
        });
    }
} 