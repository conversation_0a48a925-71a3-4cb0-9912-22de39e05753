<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Brand;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryFactory extends Factory
{
    protected $model = Category::class;

    public function definition(): array
    {
        $categories = [
            'الإلكترونيات' => [
                'desc' => 'كل ما يتعلق بالأجهزة الإلكترونية والتقنية',
                'icon' => 'ti-device-mobile',
                'brands' => ['Apple', 'Samsung', 'Sony', 'LG', 'Huawei', 'Dell', 'HP']
            ],
            'الأثاث المنزلي' => [
                'desc' => 'تشكيلة واسعة من الأثاث المنزلي العصري',
                'icon' => 'ti-home',
                'brands' => ['IKEA', 'Ashley Furniture', 'La-Z-Boy', '<PERSON> Allen', 'Home Depot']
            ],
            'الأجهزة المنزلية' => [
                'desc' => 'أجهزة منزلية حديثة لتسهيل حياتك اليومية',
                'icon' => 'ti-device-tv',
                'brands' => ['Samsung', 'LG', 'Whirlpool', 'Bosch', 'Electrolux', 'Panasonic']
            ],
            'الملابس والأزياء' => [
                'desc' => 'أحدث صيحات الموضة والأزياء العصرية',
                'icon' => 'ti-shirt',
                'brands' => ['Nike', 'Adidas', 'Zara', 'H&M', 'Gucci', 'Puma', 'Under Armour']
            ],
            'مستلزمات المكتب' => [
                'desc' => 'كل ما يلزم مكتبك من أدوات وتجهيزات',
                'icon' => 'ti-briefcase',
                'brands' => ['HP', 'Dell', '3M', 'Staples', 'OfficeMax', 'Logitech']
            ],
        ];

        $images = [
            'https://picsum.photos/300/200?random=1',
            'https://picsum.photos/300/200?random=2',
            'https://picsum.photos/300/200?random=3',
            'https://picsum.photos/300/200?random=4',
            'https://picsum.photos/300/200?random=5',
            'https://picsum.photos/300/200?random=6',
        ];

        $category = array_rand($categories);
        $categoryData = $categories[$category];

        static::afterCreating(function (Category $category) use ($categoryData) {
            $brandIds = collect($categoryData['brands'])->map(function ($brandName) {
                return Brand::firstOrCreate(
                    ['name' => $brandName],
                    ['description' => 'Brand for ' . $brandName]
                )->id;
            });
            
            $category->brands()->attach($brandIds);
        });

        return [
            'title' => $category,
            'description' => $categoryData['desc'],
            'image' => $this->faker->randomElement($images),
            'icon' => $categoryData['icon'],
            'order' => fake()->numberBetween(1, 100),
            'parent_id' => null,
            'created_by' => User::factory(),
        ];
    }

    public function asChild(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'parent_id' => Category::factory()->create()->id,
            ];
        });
    }
}
