<?php

namespace Database\Factories;

use App\Models\Brand;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class BrandFactory extends Factory
{
    protected $model = Brand::class;

    public function definition(): array
    {
        $brands = [
            [
                'name' => 'سامسونج',
                'name_en' => 'Samsung',
                'description' => 'شركة رائدة في مجال الإلكترونيات والأجهزة الذكية',
            ],
            [
                'name' => 'ابل',
                'name_en' => 'Apple',
                'description' => 'شركة تقنية متخصصة في الهواتف والحواسيب',
            ],
            [
                'name' => 'هواوي',
                'name_en' => 'Huawei',
                'description' => 'شركة تقنية رائدة في مجال الاتصالات والهواتف',
            ],
            [
                'name' => 'شاومي',
                'name_en' => 'Xiaomi',
                'description' => 'شركة إلكترونيات صينية معروفة بمنتجاتها المبتكرة',
            ],
            [
                'name' => 'ال جي',
                'name_en' => 'LG',
                'description' => 'شركة متخصصة في الأجهزة المنزلية والإلكترونيات',
            ],
            [
                'name' => 'سوني',
                'name_en' => 'Sony',
                'description' => 'شركة يابانية رائدة في مجال الإلكترونيات والترفيه',
            ],
            [
                'name' => 'اتش بي',
                'name_en' => 'HP',
                'description' => 'شركة متخصصة في الحواسيب والطابعات',
            ],
            [
                'name' => 'ديل',
                'name_en' => 'Dell',
                'description' => 'شركة رائدة في مجال الحواسيب والخوادم',
            ],
            [
                'name' => 'لينوفو',
                'name_en' => 'Lenovo',
                'description' => 'شركة حواسيب عالمية معروفة بمنتجاتها المبتكرة',
            ],
            [
                'name' => 'توشيبا',
                'name_en' => 'Toshiba',
                'description' => 'شركة يابانية متخصصة في الإلكترونيات والأجهزة',
            ],
        ];

        $brand = $this->faker->randomElement($brands);
        
        return [
            'name' => $brand['name'],
            'name_en' => $brand['name_en'],
            'description' => $brand['description'],
            'logo' => $this->faker->imageUrl(400, 400, 'business'),
            'created_by' => User::factory(),
        ];
    }
}