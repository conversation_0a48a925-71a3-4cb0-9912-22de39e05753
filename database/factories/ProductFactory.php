<?php

namespace Database\Factories;

use App\Jobs\ProcessProductMedia;
use App\Models\Product;
use App\Models\ProductMedia;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Location;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Storage;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        $conditions = ['new' => 'جديد', 'used' => 'مستعمل', 'refurbished' => 'مجدد'];
        $statuses = ['pending' => 'قيد المراجعة', 'approved' => 'معتمد', 'rejected' => 'مرفوض'];
        
        // Arabic product titles that make sense for e-commerce
        $productTypes = [
            'هاتف', 'لابتوب', 'ساعة ذكية', 'سماعات', 'تلفاز', 
            'طاولة', 'كرسي', 'خزانة', 'سرير', 'مكتب',
            'ثلاجة', 'غسالة', 'مكيف', 'فرن', 'ميكروويف'
        ];
        
        $productType = $this->faker->randomElement($productTypes);
        
        // Create related models first
        $location = Location::find($this->faker->randomElement(Location::pluck('id')->toArray()));
        $category = Category::find($this->faker->randomElement(Category::pluck('id')->toArray()));
        $user = User::find($this->faker->randomElement(User::pluck('id')->toArray()));
        $brand = $category->brands()->inRandomOrder()->first();
        if (!$brand) {
            $brand = Brand::factory()->create();
            $category->brands()->attach($brand->id);
        }

        // Create base product data
        $productData = [
            'title' => $brand->name . ' ' . $productType . ' ' . $this->faker->numberBetween(1, 9),
            'desc' => 'مواصفات المنتج: ' . $this->faker->realText(200, 2),
            'price' => $this->faker->randomFloat(2, 100, 10000),
            'status' => array_rand($statuses),
            'condition' => array_rand($conditions),
            'slug' => $this->faker->slug,
            'views_count' => $this->faker->numberBetween(0, 1000),
            'location_id' => $location->id,
            'category_id' => $category->id,
            'user_id' => $user->id,
            'brand_id' => $brand->id,
        ];

        // Create the product first
        $product = Product::create($productData);

        // Generate 3-5 images
        $numberOfImages = rand(1, 3);
        for ($index = 0; $index < $numberOfImages; $index++) {
            $fileMimeType = 'image/jpeg';
            $fileExtension = 'jpg';
            $fileName = time() . '_' . $index . '.' . $fileExtension;
            $path = "products/{$product->id}/images/" . $fileName;

            // Create a simple placeholder image content
            $width = 640;
            $height = 480;
            $image = imagecreatetruecolor($width, $height);
            $bgColor = imagecolorallocate($image, rand(0, 255), rand(0, 255), rand(0, 255));
            imagefill($image, 0, 0, $bgColor);
            
            // Save the image to memory
            ob_start();
            imagejpeg($image);
            $imageContent = ob_get_clean();
            imagedestroy($image);

            Storage::disk('products')->put($path, $imageContent);
                    
            $media = new ProductMedia();
            $media->name = $fileName;
            $media->type = 'image';
            $media->mime_type = $fileMimeType;
            $media->file_size = strlen($imageContent);
            $media->extension = $fileExtension;
            $media->path = $path;
            $media->order = $index;
            $media->is_primary = $index === 0;
            $media->product_id = $product->id;
            $media->save();

            ProcessProductMedia::dispatch($media, $path);
        }

        return $productData;
    }
}