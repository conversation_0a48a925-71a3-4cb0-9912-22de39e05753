<?php

namespace Database\Factories;

use App\Models\AppNotification;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AppUserNotification>
 */
class AppUserNotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'app_notification_id' => AppNotification::factory(),
            'read_at' => fake()->boolean(70) ? fake()->dateTimeBetween('-1 week', 'now') : null, // 70% chance of being read
        ];
    }

    /**
     * Define the notification as read
     */
    public function asRead(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'read_at' => fake()->dateTimeBetween('-1 week', 'now'),
            ];
        });
    }

    /**
     * Define the notification as unread
     */
    public function asUnread(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'read_at' => null,
            ];
        });
    }
} 