<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Achievement;
use App\Enums\AchievementType;

class AchievementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Achievement::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(AchievementType::cases());

        return [
            'name' => ucwords(str_replace('_', ' ', $type->value)),
            'description' => $this->faker->sentence(),
            'type' => $type,
            'role' => 'role_' . $type->value, // Example role based on type
        ];
    }
}

