<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('has_multiple_devices')->default(false)->comment('Flag to indicate if user has multiple devices or shares device with other users');
            $table->json('device_ids')->nullable()->comment('List of device IDs associated with this user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['has_multiple_devices', 'device_ids']);
        });
    }
};
