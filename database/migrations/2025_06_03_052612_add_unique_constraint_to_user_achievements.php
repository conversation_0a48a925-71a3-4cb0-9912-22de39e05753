<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_achievements', function (Blueprint $table) {
            // Add unique constraint including year
            $table->unique(['user_id', 'achievement_id', 'achieved_year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_achievements', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique(['user_id', 'achievement_id', 'achieved_year']);
        });
    }
};
