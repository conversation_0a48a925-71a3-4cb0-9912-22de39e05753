<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('achievements', function (Blueprint $table) {
            // Add is_system column
            $table->boolean('is_system')->default(false)->after('description');

            // Remove type and role columns if they exist
            if (Schema::hasColumn('achievements', 'type')) {
                $table->dropColumn('type');
            }
            if (Schema::hasColumn('achievements', 'role')) {
                $table->dropColumn('role');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('achievements', function (Blueprint $table) {
            // Remove is_system column
            $table->dropColumn('is_system');

            // Add back type and role columns
            $table->string('type')->default('achievement')->after('description');
            $table->string('role')->nullable()->after('type');
        });
    }
};
