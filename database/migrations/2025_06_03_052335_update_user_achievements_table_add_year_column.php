<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_achievements', function (Blueprint $table) {
            // Add achieved_year column as nullable first
            $table->year('achieved_year')->nullable()->after('achieved_at');

            // Drop the old unique constraint
            $table->dropUnique(['user_id', 'achievement_id']);
        });

        // Update existing records to set achieved_year from achieved_at
        // Use SQLite-compatible date function
        DB::statement("UPDATE user_achievements SET achieved_year = CAST(strftime('%Y', achieved_at) AS INTEGER)");

        Schema::table('user_achievements', function (Blueprint $table) {
            // Make achieved_year not nullable
            $table->year('achieved_year')->nullable(false)->change();

            // Add new unique constraint including year
            $table->unique(['user_id', 'achievement_id', 'achieved_year']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_achievements', function (Blueprint $table) {
            // Drop the new unique constraint
            $table->dropUnique(['user_id', 'achievement_id', 'achieved_year']);

            // Add back the old unique constraint
            $table->unique(['user_id', 'achievement_id']);

            // Drop the achieved_year column
            $table->dropColumn('achieved_year');
        });
    }
};
