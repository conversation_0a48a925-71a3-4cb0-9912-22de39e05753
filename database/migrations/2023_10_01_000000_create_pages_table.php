<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePagesTable extends Migration
{
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('title_ar')->nullable();
            $table->string('slug')->unique();
            $table->string('slug_ar')->nullable();
            $table->text('content');
            $table->text('content_ar')->nullable();
            $table->string('meta_keywords')->nullable();
            $table->string('meta_keywords_ar')->nullable();
            $table->string('meta_description')->nullable();
            $table->string('meta_description_ar')->nullable();
            $table->boolean('status')->default(true);

            $table->string('page_type')->default('page');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('pages');
    }
}
