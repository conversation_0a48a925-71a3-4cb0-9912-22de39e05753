<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app-updates', function (Blueprint $table) {
            $table->id();
            $table->string('platform'); // e.g., 'iOS', 'Android'
            $table->string('version'); // e.g., '1.0.0'
            $table->string('message')->nullable(); // Brief description
            $table->text('update_message')->nullable(); // Detailed changelog
            $table->string('banner_message')->nullable(); // URL to banner image
            $table->date('release_date')->nullable(); // Release date of the update
            $table->boolean('force_update')->default(false); // Whether the update is mandatory
            $table->boolean('show_banner_message')->default(false); // Whether the update is mandatory
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app-updates');
    }
};
