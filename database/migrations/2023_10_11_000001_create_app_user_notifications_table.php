<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppUserNotificationsTable extends Migration
{
    public function up()
    {
        Schema::create('app_user_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('app_notification_id');
            $table->unsignedBigInteger('user_id');
            $table->timestamp('read_at')->nullable();
            $table->foreign('app_notification_id')->references('id')->on('app_notifications')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('app_user_notifications');
    }
}
