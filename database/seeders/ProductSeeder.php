<?php

namespace Database\Seeders;

use App\Enums\StatusEnum;
use App\Jobs\ProcessProductMedia;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Location;
use App\Models\Product;
use App\Models\ProductMedia;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Http;

class ProductSeeder extends Seeder
{
    public function run()
    {
        $users = User::orderBy('id')->pluck('id')->all();
        $categories = Category::orderBy('id')->pluck('id')->all();
        $locations = Location::orderBy('id')->pluck('id')->all();
        $brands = Brand::orderBy('id')->pluck('id')->all();
        $tags = Tag::orderBy('id')->pluck('id')->all();

        $arabicTitles = [
            'آيفون 13 برو ماكس - أسود فلكي',
            'سامسونج جالكسي إس 22 ألترا - أحدث إصدار',
            'لاب توب ماك بوك برو 16 بوصة - M1 Max',
            'سيارة تويوتا لاندكروزر 2022 - فل كامل',
            'سيارة هيونداي سوناتا 2023 - فئة سمارت',
            'ساعة آبل الإصدار السابع - جديدة',
            'بلاي ستيشن 5 - نسخة رقمية',
            'تلفاز سامسونج QLED 65 بوصة - 4K',
            'كاميرا كانون احترافية - EOS R5',
            'سماعات آبل إيربودز برو - مع خاصية إلغاء الضوضاء',
            'أريكة جلدية فاخرة - ثلاث مقاعد',
            'طاولة طعام خشبية - 8 كراسي',
            'مكيف سبليت كاريير - 24 ألف وحدة تبريد',
            'غسالة أوتوماتيكية إل جي - 14 كجم',
            'ثلاجة سامسونج سايد باي سايد - ستانلس ستيل',
            'جهاز آيباد برو 12.9 بوصة - الجيل الخامس',
            'سجادة فارسية يدوية الصنع - حرير',
            'خاتم ذهب عيار 21 - مرصع بالألماس',
            'عطر ديور ساوفاج - أصلي',
            'ساعة رولكس سابمارينر - إصدار خاص',
            'دراجة هوائية كاربون - تريك مادون',
            'شنطة لويس فيتون - أصلية',
            'نظارة شمسية راي بان - واي فيرر',
            'مجموعة أواني طهي تيفال - 10 قطع',
            'مكتب خشب بلوط - مع رفوف',
            'هاتف هواوي P50 برو - إصدار خاص',
            'قهوة عربية فاخرة - هيل وزعفران',
            'سكين مطبخ ياباني - داماسكس',
            'كريستال باكارات - طقم شاي',
            'فستان سهرة مطرز - تصميم لبناني'
        ];
    
        $arabicDescriptions = [
            'منتج فاخر بحالة ممتازة، استخدام شهرين فقط. السعر قابل للتفاوض البسيط.',
            'جديد بالكرتونة، لم يُستخدم مطلقاً. ضمان المتجر ساري لمدة عام كامل.',
            'استخدام بسيط جداً وبحالة تشبه الجديدة تماماً. جميع الملحقات موجودة.',
            'تم شراؤه من الوكيل مباشرة، جميع الصيانات في الوكالة والسيارة خالية من الحوادث.',
            'نظيف جداً ويعمل بكفاءة عالية، سبب البيع السفر للخارج.',
            'منتج أصلي 100% مع إمكانية الفحص. مواصفات عالية وسعر تنافسي.',
            'استيراد خاص، غير متوفر في السوق المحلي. تصميم فريد وعملي.',
            'تم استخدامه لمدة قصيرة، لا يوجد أي خدوش أو عيوب. السعر نهائي.',
            'المنتج بحالة الوكالة، مع كامل الملحقات والكرتون الأصلي.',
            'العرض لفترة محدودة، اغتنم الفرصة قبل النفاذ. توصيل مجاني للمشتري.',
            'وارد أوروبا، جودة عالية جداً ومتانة في التصنيع. ضمان لمدة 3 سنوات.',
            'تصفية محل، سعر مخفض بنسبة 40% عن سعر السوق.',
            'استخدام نظيف من قبل عائلة محافظة. بدون عيوب أو مشاكل.',
            'إصدار محدود، لم يتبق سوى قطعتين فقط في السوق المحلي.',
            'منتج أصلي مع ضمان الوكيل. تم تخفيض السعر للبيع السريع.'
        ];
    
        for ($i = 0; $i < 30; $i++) {
            $categoryId = !empty($categories) ? $categories[$i % count($categories)] : null;
            $locationId = !empty($locations) ? $locations[$i % count($locations)] : null;
            $brandId = !empty($brands) ? $brands[$i % count($brands)] : null;
            $userId = !empty($users) ? $users[$i % count($users)] : null;
            
            $title = isset($arabicTitles[$i]) ? $arabicTitles[$i] : 'منتج عربي ' . ($i + 1);
            
            $status = Arr::random([
                StatusEnum::PENDING->value,
                StatusEnum::APPROVED->value,
            ]);
            
            $product = Product::create([
                'hashId' => Str::random(10),
                'user_id' => $userId,
                'category_id' => $categoryId,
                'title' => $title,
                'desc' => $arabicDescriptions[$i % count($arabicDescriptions)],
                'price' => mt_rand(50 * 100, 5000 * 100) / 100,
                'location_id' => $locationId,
                'status' => $status,
                'condition' => Arr::random(['new', 'used']),
                'slug' => Str::slug($title, '-') . '-' . Str::random(5),
                'views_count' => mt_rand(0, 1000),
                'allow_phone_call' => mt_rand(0, 1),
                'allow_chat' => mt_rand(0, 1),
                'alternative_phone' => mt_rand(0, 1) ? null : '0599999999',
                'brand_id' => $brandId,
                'created_by' => $userId,
                'updated_by' => null,
                'deleted_by' => null,
            ]);
            
            if (!empty($tags)) {
                $tagsCount = mt_rand(2, 4);
                $selectedTags = array_slice($tags, $i % count($tags), $tagsCount);
                if(count($selectedTags) > 0) {
                    $product->tags()->attach($selectedTags);
                }
            }
            
            for($j = 1; $j <= mt_rand(1, 3); $j++) {
                $imageUrl = 'https://picsum.photos/800/600?random=' . ($i *1 + $j);
                $response = Http::get($imageUrl);
                
                if ($response->successful()) {
                    $fileName = time() . '_' . $j . '.jpg';
                    $path = "products/{$product->id}/images/" . $fileName;
                    
                    // Store the image
                    Storage::disk('products')->put($path, $response->body());
                    
                    $media = new ProductMedia();
                    $media->name = $fileName;
                    $media->type = 'image';
                    $media->mime_type = 'image/jpeg';
                    $media->file_size = strlen($response->body());
                    $media->extension = 'jpg';
                    $media->path = $path;
                    $media->order = $j;
                    $media->is_primary = $j === 1;
                    $media->product_id = $product->id;
                    $media->save();
                    ProcessProductMedia::dispatch($media, $path);
                }
            }
        }
    }
}