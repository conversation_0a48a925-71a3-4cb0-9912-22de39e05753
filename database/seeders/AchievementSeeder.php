<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Enums\AchievementType;
use App\Models\Achievement;

class AchievementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        // Create achievements based on the Enum types
        foreach (AchievementType::cases() as $type) {
            Achievement::firstOrCreate(
                ['type' => $type],
                [
                    'name' => ucwords(str_replace('_', ' ', $type->value)),
                    'description' => 'Awarded for ' . strtolower(str_replace('_', ' ', $type->value)) . '.',
                    // Define specific roles if needed, otherwise use a default or leave null
                    'role' => 'role_' . $type->value, 
                ]
            );
        }

        // Example: Add specific description for registration date
        Achievement::updateOrCreate(
            ['type' => AchievementType::REWARD],
            ['description' => 'Awarded upon user registration.']
        );

        // You can add more specific details for other achievements here
        // Achievement::updateOrCreate(['type' => AchievementType::GOLDEN_USER], ['description' => '...']);
        // Achievement::updateOrCreate(['type' => AchievementType::SILVER_USER], ['description' => '...']);
        // ... and so on
    }
}
