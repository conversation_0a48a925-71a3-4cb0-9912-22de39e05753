<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Seeder;

class PageSeeder extends Seeder
{
    public function run(): void
    {
        $pages = [
            // Help Center Pages
            [
                'title' => 'Getting Started Guide',
                'title_ar' => 'دليل البدء',
                'slug' => 'getting-started',
                'content' => '<h1>Getting Started with Our App</h1><p>Welcome to our application! This guide will help you get started...</p>',
                'content_ar' => '<h1>البدء باستخدام التطبيق</h1><p>مرحبا بكم في تطبيقنا! سيساعدك هذا الدليل على البدء...</p>',
                'meta_description' => 'Complete guide to get started with our application',
                'meta_keywords' => 'getting started, guide, help, tutorial',
                'status' => true,
            ],
            [
                'title' => 'Account Issues Help',
                'title_ar' => 'مساعدة مشاكل الحساب',
                'slug' => 'account-issues',
                'content' => '<h1>Resolving Account Issues</h1><p>Find solutions to common account problems...</p>',
                'content_ar' => '<h1>حل مشاكل الحساب</h1><p>اعثر على حلول لمشاكل الحساب الشائعة...</p>',
                'meta_description' => 'Solutions for common account issues',
                'meta_keywords' => 'account, issues, problems, solutions',
                'status' => true,
            ],
            
            // FAQ Pages
            [
                'title' => 'General Questions',
                'title_ar' => 'الأسئلة العامة',
                'slug' => 'general-questions',
                'content' => '<h1>Frequently Asked Questions</h1><p>Find answers to the most common questions...</p>',
                'content_ar' => '<h1>الأسئلة الشائعة</h1><p>اعثر على إجابات لأكثر الأسئلة شيوعًا...</p>',
                'meta_description' => 'Frequently asked questions about our services',
                'meta_keywords' => 'FAQ, questions, answers, help',
                'status' => true,
            ],
            
            // Legal Pages
            [
                'title' => 'Terms of Service',
                'title_ar' => 'شروط الخدمة',
                'slug' => 'service-terms',
                'content' => '<h1>Terms of Service</h1><p>Please read these terms of service carefully...</p>',
                'content_ar' => '<h1>شروط الخدمة</h1><p>يرجى قراءة شروط الخدمة هذه بعناية...</p>',
                'meta_description' => 'Terms of service and user agreement',
                'meta_keywords' => 'terms, service, agreement, legal',
                'status' => true,
            ],
            [
                'title' => 'Privacy Policy',
                'title_ar' => 'سياسة الخصوصية',
                'slug' => 'privacy-policy',
                'content' => '<h1>Privacy Policy</h1><p>Your privacy is important to us. This policy explains how we handle your data...</p>',
                'content_ar' => '<h1>سياسة الخصوصية</h1><p>خصوصيتك مهمة بالنسبة لنا. توضح هذه السياسة كيفية التعامل مع بياناتك...</p>',
                'meta_description' => 'Our privacy policy and data handling practices',
                'meta_keywords' => 'privacy, policy, data, security',
                'status' => true,
            ],
            [
                'title' => 'Cookie Policy',
                'title_ar' => 'سياسة ملفات الارتباط',
                'slug' => 'cookie-policy',
                'content' => '<h1>Cookie Policy</h1><p>Learn about how we use cookies and similar technologies...</p>',
                'content_ar' => '<h1>سياسة ملفات الارتباط</h1><p>تعرف على كيفية استخدامنا لملفات الارتباط والتقنيات المماثلة...</p>',
                'meta_description' => 'Information about our use of cookies',
                'meta_keywords' => 'cookies, tracking, privacy',
                'status' => true,
            ],
        ];

        foreach ($pages as $page) {
            Page::create($page);
        }
    }
}