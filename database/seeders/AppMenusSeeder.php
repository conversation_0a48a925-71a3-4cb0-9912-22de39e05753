<?php

namespace Database\Seeders;

use App\Models\AppMenu;
use App\Models\Page;
use Illuminate\Database\Seeder;

class AppMenusSeeder extends Seeder
{
    public function run(): void
    {
        // Run the PageSeeder first to ensure pages exist
        $this->call(PageSeeder::class);

        // Get all pages to reference in menus
        $pages = Page::all()->keyBy('slug');

        $menus = [
            [
                'title' => 'Help Center',
                'title_ar' => 'مركز المساعدة',
                'slug' => 'help-center',
                'group' => 'footer',
                'icon' => 'https://picsum.photos/64/64?random=1',
                'order' => 1,
                'type' => 'url',
                'children' => [
                    [
                        'title' => 'Getting Started',
                        'title_ar' => 'البداية',
                        'slug' => 'getting-started',
                        'icon' => 'play-circle',
                        'order' => 1,
                        'type' => 'url',
                        'data' => config('app.url') . '/page/getting-started'
                    ],
                    [
                        'title' => 'Account Issues',
                        'title_ar' => 'مشاكل الحساب',
                        'slug' => 'account-issues',
                        'icon' => 'user-x',
                        'order' => 2,
                        'type' => 'url',
                        'data' => config('app.url') . '/page/account-issues'
                    ],
                    [
                        'title' => 'Common Problems',
                        'title_ar' => 'المشاكل الشائعة',
                        'slug' => 'common-problems',
                        'icon' => 'alert-circle',
                        'order' => 3,
                        'type' => 'url',
                        'data' => config('app.url') . '/page/common-problems'
                    ]
                ]
            ],
            [
                'title' => 'Support',
                'title_ar' => 'الدعم',
                'slug' => 'support',
                'group' => 'footer',
                'icon' => 'https://picsum.photos/64/64?random=2',
                'order' => 2,
                'type' => 'url',
                'children' => [
                    [
                        'title' => 'Contact Us',
                        'title_ar' => 'اتصل بنا',
                        'slug' => 'contact-us',
                        'icon' => 'mail',
                        'order' => 1,
                        'type' => 'url',
                        'data' => config('app.url') . '/page/contact-us'
                    ],
                    [
                        'title' => 'Report Issue',
                        'title_ar' => 'الإبلاغ عن مشكلة',
                        'slug' => 'report-issue',
                        'icon' => 'flag',
                        'order' => 2,
                        'type' => 'url',
                        'data' => config('app.url') . '/page/report-issue'
                    ],
                    [
                        'title' => 'Live Chat',
                        'title_ar' => 'الدردشة المباشرة',
                        'slug' => 'live-chat',
                        'icon' => 'message-circle',
                        'order' => 3,
                        'type' => 'text',
                        'data' => "يمكنك الدردشة معنا عبر الواتساب علي الرقم 0599999999"
                    ]
                ]
            ],
            [
                'title' => 'FAQ',
                'title_ar' => 'الأسئلة الشائعة',
                'slug' => 'faq',
                'group' => 'footer',
                'icon' => 'https://picsum.photos/64/64?random=3',
                'order' => 3,
                'type' => 'url',
                'children' => [
                    [
                        'title' => 'General Questions',
                        'title_ar' => 'أسئلة عامة',
                        'slug' => 'general-questions',
                        'icon' => 'info',
                        'order' => 1,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/general-questions'
                    ],
                    [
                        'title' => 'Payment Issues',
                        'title_ar' => 'مشاكل الدفع',
                        'slug' => 'payment-issues',
                        'icon' => 'credit-card',
                        'order' => 2,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/payment-issues'
                    ],
                    [
                        'title' => 'Technical Issues',
                        'title_ar' => 'مشاكل تقنية',
                        'slug' => 'technical-issues',
                        'icon' => 'settings',
                        'order' => 3,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/technical-issues'
                    ]
                ]
            ],
            [
                'title' => 'Terms & Conditions',
                'title_ar' => 'الشروط والأحكام',
                'slug' => 'terms',
                'group' => 'legal',
                'icon' => 'https://picsum.photos/64/64?random=4',
                'order' => 4,
                'type' => 'html',
                'children' => [
                    [
                        'title' => 'User Agreement',
                        'title_ar' => 'اتفاقية المستخدم',
                        'slug' => 'user-agreement',
                        'icon' => 'file',
                        'order' => 1,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/user-agreement'
                    ],
                    [
                        'title' => 'Service Terms',
                        'title_ar' => 'شروط الخدمة',
                        'slug' => 'service-terms',
                        'icon' => 'file',
                        'order' => 2,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/service-terms'
                    ]
                ]
            ],
            [
                'title' => 'Privacy Policy',
                'title_ar' => 'سياسة الخصوصية',
                'slug' => 'privacy-policy',
                'group' => 'legal',
                'icon' => 'https://picsum.photos/64/64?random=5',
                'order' => 5,
                'type' => 'html',
                'children' => [
                    [
                        'title' => 'Data Collection',
                        'title_ar' => 'جمع البيانات',
                        'slug' => 'data-collection',
                        'icon' => 'database',
                        'order' => 1,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/data-collection'
                    ],
                    [
                        'title' => 'Cookie Policy',
                        'title_ar' => 'سياسة ملفات الارتباط',
                        'slug' => 'cookie-policy',
                        'icon' => 'cookie',
                        'order' => 2,
                        'type' => 'html',
                        'data' => config('app.url') . '/page/cookie-policy'
                    ]
                ]
            ]
        ];

        foreach ($menus as $menu) {
            $children = $menu['children'] ?? [];
            unset($menu['children']);
            $parent = AppMenu::create($menu);

            foreach ($children as $child) {
                $child['parent_id'] = $parent->id;
                AppMenu::create($child);
            }
        }
    }
}