<?php

namespace Database\Seeders;

use App\RolesEnum;
use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\Product;
use App\Models\Tag;
use App\Models\User;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create admin user
        $user = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('Harajy.@2025'),
        ]);
        $this->call(PermissionsSeeder::class);
        $user->assignRole(RolesEnum::SUPER_ADMIN);


        $this->call(CategorySeeder::class);
    
        // Create static pages
        $this->call(PageSeeder::class);

        $this->call(AppMenusSeeder::class);

        // Create tags first - generate all possible tags
        $this->call(TagSeeder::class);
        // Create locations
        $this->call(LocationSeeder::class);

        $this->call(ProductSeeder::class);

        $this->call([AchievementSeeder::class]);
        $this->call([CommissionAchievementSeeder::class]);
        $this->call([SystemAchievementsSeeder::class]);

    }
}
