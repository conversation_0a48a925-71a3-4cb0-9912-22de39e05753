<?php

namespace Database\Seeders;

use App\Enums\AchievementType;
use App\Models\Achievement;
use Illuminate\Database\Seeder;

class CommissionAchievementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $achievements = [
            [
                'name' => 'أول عمولة مدفوعة',
                'slug' => 'first_commission_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع أول عمولة في السنة',
                'type' => AchievementType::ACHIEVEMENT,
                'role' => 'seller',
                'icon' => 'ti-coin',
            ],
            [
                'name' => 'بائع مخلص',
                'slug' => 'loyal_seller',
                'description' => 'تم منح هذا الإنجاز عند دفع 3 عمولات أو أكثر في نفس السنة',
                'type' => AchievementType::BADGE,
                'role' => 'seller',
                'icon' => 'ti-heart',
            ],
            [
                'name' => 'بائع عالي الحجم',
                'slug' => 'high_volume_seller',
                'description' => 'تم منح هذا الإنجاز عند دفع 5 عمولات أو أكثر في نفس السنة',
                'type' => AchievementType::BADGE,
                'role' => 'seller',
                'icon' => 'ti-trophy',
            ],
            [
                'name' => 'بائع محترف',
                'slug' => 'professional_seller',
                'description' => 'تم منح هذا الإنجاز عند دفع 10 عمولات أو أكثر في نفس السنة',
                'type' => AchievementType::REWARD,
                'role' => 'seller',
                'icon' => 'ti-star',
            ],
            [
                'name' => 'بائع ماسي',
                'slug' => 'diamond_seller',
                'description' => 'تم منح هذا الإنجاز عند دفع 20 عمولة أو أكثر في نفس السنة',
                'type' => AchievementType::REWARD,
                'role' => 'seller',
                'icon' => 'ti-diamond',
            ],
        ];

        foreach ($achievements as $achievementData) {
            Achievement::updateOrCreate(
                ['slug' => $achievementData['slug']],
                $achievementData
            );
        }
    }
}
