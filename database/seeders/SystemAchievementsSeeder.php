<?php

namespace Database\Seeders;

use App\Models\Achievement;
use Illuminate\Database\Seeder;

/**
 * System Achievements Seeder
 * 
 * This seeder creates all the system achievements that cannot be modified by admins.
 * It includes achievements for:
 * 1. Commission-related achievements (دفع العموله)
 * 2. Coin payment achievements (دفيع عملات)
 * 3. Annual excellence achievements (تميز السنة المثالي)
 * 4. Brand verification achievements (موثق كعلامه تجارية)
 * 5. Premium membership achievements (عضوية تميز)
 */
class SystemAchievementsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $achievements = [
            // Commission-related achievements (دفع العموله) - Year-based
            [
                'name' => 'أول عمولة مدفوعة',
                'slug' => 'commission_1_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع أول عمولة في السنة',
                'icon' => 'coin',
                'is_system' => true,
            ],
            [
                'name' => 'عمولتان مدفوعتان',
                'slug' => 'commission_2_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع عمولتين في نفس السنة',
                'icon' => 'coins',
                'is_system' => true,
            ],
            [
                'name' => 'ثلاث عمولات مدفوعة',
                'slug' => 'commission_3_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع 3 عمولات في نفس السنة',
                'icon' => 'heart',
                'is_system' => true,
            ],
            [
                'name' => 'خمس عمولات مدفوعة',
                'slug' => 'commission_5_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع 5 عمولات في نفس السنة',
                'icon' => 'trending-up',
                'is_system' => true,
            ],
            [
                'name' => 'عشر عمولات مدفوعة',
                'slug' => 'commission_10_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع 10 عمولات في نفس السنة',
                'icon' => 'award',
                'is_system' => true,
            ],
            [
                'name' => 'عشرون عمولة مدفوعة',
                'slug' => 'commission_20_paid',
                'description' => 'تم منح هذا الإنجاز عند دفع 20 عمولة في نفس السنة',
                'icon' => 'diamond',
                'is_system' => true,
            ],

            // Annual excellence achievements (تميز السنة المثالي)
            [
                'name' => 'تميز السنة المثالي',
                'slug' => 'annual_excellence',
                'description' => 'تم منح هذا الإنجاز للمستخدمين المتميزين الذين حققوا إنجازات متعددة في نفس السنة',
                'icon' => 'ti-star',
                'is_system' => true,
            ],

            // Brand verification achievements (موثق كعلامه تجارية)
            [
                'name' => 'موثق كعلامة تجارية',
                'slug' => 'brand_verified',
                'description' => 'تم منح هذا الإنجاز عند الحصول على توثيق العلامة التجارية',
                'icon' => 'ti-shield-check',
                'is_system' => true,
            ],

            // Premium membership achievements (عضوية تميز)
            [
                'name' => 'عضوية تميز',
                'slug' => 'premium_membership',
                'description' => 'تم منح هذا الإنجاز عند الحصول على 20 تقييم في السنة الواحدة',
                'icon' => 'ti-vip',
                'is_system' => true,
            ],
        ];

        foreach ($achievements as $achievementData) {
            Achievement::updateOrCreate(
                ['slug' => $achievementData['slug']],
                $achievementData
            );
        }

        $this->command->info('System achievements seeded successfully!');
    }
}
