<?php

namespace Database\Seeders;

use App\Models\Tag;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class TagSeeder extends Seeder
{
    public function run(): void
    {
        $techTags = Tag::insert([
            ['name' => 'سلكي', 'slug' => Str::slug('سلكي'), 'description' => 'منتجات تعمل بالتوصيل السلكي'],
            ['name' => 'لاسلكي', 'slug' => Str::slug('لاسلكي'), 'description' => 'منتجات تعمل بتقنية البلوتوث أو الواي فاي'],
            ['name' => 'ذكي', 'slug' => Str::slug('ذكي'), 'description' => 'منتجات ذكية قابلة للبرمجة والتحكم'],
            ['name' => '5G', 'slug' => Str::slug('5G'), 'description' => 'يدعم شبكات الجيل الخامس'],
            ['name' => 'تحكم عن بعد', 'slug' => Str::slug('تحكم عن بعد'), 'description' => 'يمكن التحكم به عن بعد'],
        ]);
        
        $powerTags = Tag::insert([
            ['name' => 'بطارية قابلة للشحن', 'slug' => Str::slug('بطارية قابلة للشحن'), 'description' => 'يحتوي على بطارية يمكن إعادة شحنها'],
            ['name' => 'موفر للطاقة', 'slug' => Str::slug('موفر للطاقة'), 'description' => 'صديق للبيئة وموفر للطاقة'],
            ['name' => 'شحن سريع', 'slug' => Str::slug('شحن سريع'), 'description' => 'يدعم تقنية الشحن السريع'],
        ]);

        $homeTags = Tag::insert([
            ['name' => 'مقاوم للماء', 'slug' => Str::slug('مقاوم للماء'), 'description' => 'منتج مقاوم للماء والرطوبة'],
            ['name' => 'تنظيف ذاتي', 'slug' => Str::slug('تنظيف ذاتي'), 'description' => 'يحتوي على خاصية التنظيف الذاتي'],
            ['name' => 'مضاد للبكتيريا', 'slug' => Str::slug('مضاد للبكتيريا'), 'description' => 'معالج ضد البكتيريا'],
        ]);

        $performanceTags = Tag::insert([
            ['name' => 'أداء عالي', 'slug' => Str::slug('أداء عالي'), 'description' => 'منتجات ذات أداء وكفاءة عالية'],
            ['name' => 'احترافي', 'slug' => Str::slug('احترافي'), 'description' => 'مناسب للاستخدام الاحترافي'],
            ['name' => 'اقتصادي', 'slug' => Str::slug('اقتصادي'), 'description' => 'منتجات اقتصادية في الاستهلاك'],
        ]);

        $designTags = Tag::insert([
            ['name' => 'تصميم عصري', 'slug' => Str::slug('تصميم عصري'), 'description' => 'تصميم حديث ومعاصر'],
            ['name' => 'قابل للطي', 'slug' => Str::slug('قابل للطي'), 'description' => 'يمكن طيه للتخزين'],
            ['name' => 'خفيف الوزن', 'slug' => Str::slug('خفيف الوزن'), 'description' => 'سهل الحمل والنقل'],
        ]);

        $featureTags = Tag::insert([
            ['name' => 'شاشة لمس', 'slug' => Str::slug('شاشة لمس'), 'description' => 'يعمل باللمس'],
            ['name' => 'صوت محيطي', 'slug' => Str::slug('صوت محيطي'), 'description' => 'نظام صوت محيطي متطور'],
            ['name' => 'تبريد فائق', 'slug' => Str::slug('تبريد فائق'), 'description' => 'نظام تبريد متقدم'],
            ['name' => 'إضاءة LED', 'slug' => Str::slug('إضاءة LED'), 'description' => 'مزود بإضاءة LED'],
        ]);
    }
}