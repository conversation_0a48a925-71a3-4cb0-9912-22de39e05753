<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ProductAnalytics;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Faker\Factory as Faker;

class ProductAnalyticsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();
        $products = Product::all();
        $users = User::all();
        
        if ($products->isEmpty()) {
            $this->command->info('No products found. Skipping product report seeding.');
            return;
        }
        
        foreach ($products as $product) {
            // Generate between 10-50 views for each product
            $viewCount = $faker->numberBetween(10, 50);
            
            for ($i = 0; $i < $viewCount; $i++) {
                $isAuthenticated = $faker->boolean(70); // 70% chance of being authenticated
                $randomUser = $users->random();
                
                $report = new ProductAnalytics();
                $report->product_id = $product->id;
                $report->user_id = $isAuthenticated ? $randomUser->id : null;
                $report->ip_address = $faker->ipv4;
                $report->user_agent = $faker->userAgent;
                $report->device = $faker->randomElement(['Mobile', 'Desktop', 'Tablet', 'Mobile', 'Desktop']);
                $report->viewed_at = Carbon::now()->subDays($faker->numberBetween(0, 30))->subHours($faker->numberBetween(0, 23));
                $report->save();
            }
            
            // Update the views_count on the product
            $product->views_count = $viewCount;
            $product->save();
        }
        
        $this->command->info('Product reports seeded successfully.');
    }
}
