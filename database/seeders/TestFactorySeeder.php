<?php

namespace Database\Seeders;

use App\Models\AppNotification;
use App\Models\AppUpdate;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TestFactorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a single user
        User::factory()->create();

        // Create multiple users
        User::factory()->count(3)->create();

        // Create a category
        Category::factory()->create();

        // Create a notification
        AppNotification::factory()->create();

        // Create an app update
        AppUpdate::factory()->create();

        // Create with specific states
        Category::factory()->asChild()->create();
        AppNotification::factory()->asSystemNotification()->create();
        AppUpdate::factory()->forAndroid()->create();
    }
}
