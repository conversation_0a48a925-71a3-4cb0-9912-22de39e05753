@extends('layouts.layoutMaster')

@section('title', 'الإنجازات')

@section('content')
    <h4 class="fw-bold py-3 mb-4">
        <span class="text-muted fw-light">النظام /</span> الإنجازات
    </h4>

    <!-- Analytics -->
    <div class="row">
        @foreach ($analyticsData as $data)
            <div class="col-lg-4 col-md-6 col-12 mb-4">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div class="card-info">
                                <p class="card-text">{{ $data['description'] }}</p>
                                <div class="d-flex align-items-end mb-2">
                                    <h4 class="card-title mb-0">{{ $data['value'] }}</h4>
                                    @if ($data['percent'] > 0)
                                        <span class="text-success ms-2 fw-semibold">
                                            <i class="ti ti-trending-up ti-xs"></i>
                                            <span>{{ $data['percent'] }}%</span>
                                        </span>
                                    @endif
                                </div>
                                <small>{{ $data['title'] }}</small>
                            </div>
                            <div class="card-icon">
                                <span class="badge bg-label-{{ $data['color'] }} rounded p-2">
                                    <i class="ti ti-{{ $data['icon'] }} ti-sm"></i>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    <!--/ Analytics -->

    <!-- Achievements Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">الإنجازات</h5>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAchievement">
                <i class="ti ti-plus me-1"></i> إضافة إنجاز
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>الأيقونة</th>
                            <th>الاسم</th>
                            <th>النوع</th>
                            <th>عدد المستخدمين</th>
                            <th>الوصف</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($achievements as $achievement)
                            <tr>
                                <td>{{ $loop->iteration }}</td>
                                <td>
                                    @if($achievement->icon)
                                        <i class="ti ti-{{ $achievement->icon }} fs-3"></i>
                                    @else
                                        <i class="ti ti-award fs-3"></i>
                                    @endif
                                </td>
                                <td>{{ $achievement->name }}</td>
                                <td>
                                    @if($achievement->is_system)
                                        <span class="badge bg-primary">نظام</span>
                                    @else
                                        <span class="badge bg-secondary">مخصص</span>
                                    @endif
                                </td>
                                <td>
                                    @if($achievement->users_count > 0)
                                        <a href="{{ route('users.index') }}?achievement={{ $achievement->id }}"
                                           class="badge bg-success text-decoration-none">
                                            <i class="ti ti-users me-1"></i>{{ $achievement->users_count }}
                                        </a>
                                    @else
                                        <span class="badge bg-secondary">0</span>
                                    @endif
                                </td>
                                <td>{{ $achievement->description }}</td>
                                <td>{{ $achievement->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('achievements.edit', $achievement->id) }}"
                                            class="btn btn-sm btn-icon btn-text-secondary rounded-pill">
                                            <i class="ti ti-edit"></i>
                                        </a>
                                        <form action="{{ route('achievements.destroy', $achievement->id) }}" method="POST"
                                            class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-icon btn-text-secondary rounded-pill"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الإنجاز؟')">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @include('modules.achievements.modal-create')
@endsection