<div class="table-responsive px-5">
    <div class="col-md-12 mt-4 mb-4 d-flex justify-content-between">
        <input type="text" wire:model.live.debounce.399ms="search" class="form-control"
            placeholder="{{ $searchPlaceholder }}" />
        <div class="dropdown ms-2">
            <button class="btn btn-secondary" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                aria-expanded="false">
                <i class="ti ti-filter"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                @foreach ($columns as $column)
                    <li>
                        <a class="dropdown-item" href="javascript:void(0)"
                            wire:click="toggleColumnVisibility('{{ $column['field'] }}')">
                            <input type="checkbox" @if (in_array($column['field'], $visibleColumns)) checked @endif>
                            {{ $column['title'] }}
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <!-- Removed the original dropdown code from here -->
    </div>
    <table class="table">
        <thead class="border-top">
            <tr>
                @foreach ($columns as $column)
                    @if (in_array($column['field'], $visibleColumns))
                        <x-datatable.table-header field="{{ $column['field'] }}" title="{{ $column['title'] }}"
                            customClass="{{ $column['class'] ?? '' }}" :sortBy="$sortField" :sortDir="$sortDirection"
                            :sortable="$column['sortable'] ?? true" />
                    @endif
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach ($data as $item)
                <tr>
                    @foreach ($columns as $column)
                        @if (in_array($column['field'], $visibleColumns) && $column['field'] != 'actions')
                            <td>{!! $this->renderColumn($column, $item) !!}</td>
                        @endif
                    @endforeach
                    <td>
                        <div class="d-flex align-items-center">
                            @foreach ($this->actions as $actionType)
                                {!! $this->renderAction($actionType, $item) !!}
                            @endforeach
                        </div>
                    </td>
                </tr>
                @if (isset($updateModalView))
                    @include($updateModalView)
                @endif
            @endforeach
        </tbody>
    </table>
    @include('components.table.delete-modal')

    <div class="col-12 p-3">
        {{ $data->links() }}
    </div>

    <!-- JavaScript functions for the component -->
    <script>
        function openDeleteModal(item) {
            $('#deleteModal').modal('show');
            if (item.deleteMessage != undefined) {
                $('#deleteModalMessage').text(item.deleteMessage);
            }
            $('#deleteForm').attr('action', item.delete);
        }

        function openEditModal(item) {
            $('#editModal' + item.id).modal('show');
        }
    </script>
</div>
