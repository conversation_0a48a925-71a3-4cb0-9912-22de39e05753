<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="{{ session()->get('locale') ?? 'ar' }}" />
    <meta http-equiv="Content-Type" content="text/html" charset="utf-8" />
    <meta name="google-site-verification" content="qwlAL9ujxQRf-igS0mWPndJeqf9wW07A4KlWL_NkjQI" />

    <!-- share image for social media -->
    <meta name="og:image" content="{{ asset('site/images/logo/share_image.png') }}" />
    <meta name="facebook:image" content="{{ asset('site/images/logo/share_image.png') }}" />
    <meta name="twitter:image" content="{{ asset('site/images/logo/share_image.png') }}" />

    <!-- url for social media -->
    <meta property="og:url" content="{{ url()->current() }}">

    <!-- twitter card -->
    <meta name="twitter:card" content="summary_large_image">

    <!-- favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('favicons/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('favicons/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('favicons/favicon-16x16.png') }}">
    <!-- manifest for chrome tab -->
    <link rel="manifest" href="{{ asset('favicons/site.webmanifest') }}">
    <!-- safari pinned tab -->
    <link rel="mask-icon" href="{{ asset('favicons/safari-pinned-tab.svg') }}" color="#5bbad5">
    <!-- windows tile color -->
    <meta name="msapplication-TileColor" content="#da532c">
    <!-- theme color -->
    <meta name="theme-color" content="#ffffff">

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'حراجي') | حراجي - سوق البيع والشراء</title>
    <meta name="description" content="@yield('description', 'منصة حراجي للبيع والشراء، اعرض منتجاتك أو ابحث عن ما تريد شراءه بكل سهولة')">
    <meta name="keywords" content="@yield('keywords', 'حراجي, سوق, بيع, شراء, إعلانات مبوبة, منتجات مستعملة, منتجات جديدة')">

    @yield('page-meta')

    <!-- CSS -->
    @vite(['resources/front/app.css', 'resources/front/app.js'], 'build/front')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        .almarai-light {
            font-family: "Almarai", sans-serif;
            font-weight: 300;
            font-style: normal;
        }

        .almarai-regular {
            font-family: "Almarai", sans-serif;
            font-weight: 400;
            font-style: normal;
        }

        .almarai-bold {
            font-family: "Almarai", sans-serif;
            font-weight: 700;
            font-style: normal;
        }

        .almarai-extrabold {
            font-family: "Almarai", sans-serif;
            font-weight: 800;
            font-style: normal;
        }

        * {
            font-family: 'almarai-regular', sans-serif;
        }

        /* Hide scrollbar but keep functionality */
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }

        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        /* Dark mode specific styles */
        .dark .text-primary {
            color: #a6adba;
        }

        /* RTL specific adjustments */
        [dir="rtl"] .ml-auto {
            margin-left: 0;
            margin-right: auto;
        }

        [dir="rtl"] .mr-auto {
            margin-right: 0;
            margin-left: auto;
        }
    </style>

    @yield('page-style')

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer></script>
    <script src="{{ asset('assets/js/front2.js') }}"></script>

    <!-- Global variables for JavaScript -->
    <script>
        window.isUserLoggedIn = "{{ auth()->check() ? 'true' : 'false' }}" === "true";
        window.loginUrl = "{{ route('login') }}";
    </script>
</head>
