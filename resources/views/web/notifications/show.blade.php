@extends('web.layouts.layout')

@section('title', $notification->title)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('web.notifications.index') }}" class="btn btn-ghost">
                <i class="fas fa-arrow-right mr-2 rtl:ml-2 rtl:mr-0 rtl:rotate-180"></i>
                {{ __('العودة للإشعارات') }}
            </a>
        </div>

        <!-- Notification Card -->
        <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
                <!-- Header -->
                <div class="flex items-center gap-4 mb-6">
                    <div class="avatar placeholder">
                        <div class="bg-primary text-primary-content rounded-full w-16">
                            <i class="fas fa-bell text-2xl"></i>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h1 class="text-2xl font-bold mb-2">{{ $notification->title }}</h1>
                        <div class="flex items-center gap-3 text-sm text-base-content/70">
                            <span class="badge badge-{{ $notification->type == 'system' ? 'info' : ($notification->type == 'promotion' ? 'warning' : 'primary') }}">
                                {{ ucfirst($notification->type) }}
                            </span>
                            <span>{{ $notification->created_at->locale('ar')->format('d/m/Y H:i') }}</span>
                            @if($notification->is_read)
                                <span class="badge badge-success">{{ __('مقروء') }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="prose max-w-none">
                    <div class="text-lg leading-relaxed mb-6">
                        {!! nl2br(e($notification->body)) !!}
                    </div>
                </div>

                <!-- Additional Data -->
                @if($notification->data)
                    @php
                        $data = is_string($notification->data) ? json_decode($notification->data, true) : $notification->data;
                    @endphp
                    @if($data && is_array($data))
                        <div class="divider">{{ __('معلومات إضافية') }}</div>
                        <div class="bg-base-200 p-4 rounded-lg">
                            @foreach($data as $key => $value)
                                <div class="flex justify-between items-center py-2 border-b border-base-300 last:border-b-0">
                                    <span class="font-medium">{{ $key }}:</span>
                                    <span>{{ $value }}</span>
                                </div>
                            @endforeach
                        </div>
                    @endif
                @endif

                <!-- Actions -->
                <div class="card-actions justify-end mt-6">
                    @if(!$notification->is_read)
                        <form action="{{ route('web.notifications.read', $notification) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-check mr-2 rtl:ml-2 rtl:mr-0"></i>
                                {{ __('تحديد كمقروء') }}
                            </button>
                        </form>
                    @endif
                    
                    <form action="{{ route('web.notifications.destroy', $notification) }}" method="POST"
                          class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-error">
                            <i class="fas fa-trash mr-2 rtl:ml-2 rtl:mr-0"></i>
                            {{ __('حذف') }}
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Related Information -->
        @if($notification->sent_at)
            <div class="mt-6 text-center text-sm text-base-content/60">
                {{ __('تم إرسال هذا الإشعار في') }}: {{ \Carbon\Carbon::parse($notification->sent_at)->locale('ar')->format('d/m/Y H:i') }}
            </div>
        @endif
    </div>
</div>
@endsection
