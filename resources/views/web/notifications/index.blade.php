@extends('web.layouts.layout')

@section('title', __('الإشعارات'))

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-base-content">{{ __('الإشعارات') }}</h1>
            @if($notifications->where('is_read', false)->count() > 0)
                <form action="{{ route('web.notifications.mark-all-read') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-check-double mr-1 rtl:ml-1 rtl:mr-0"></i>
                        {{ __('تحديد الكل كمقروء') }}
                    </button>
                </form>
            @endif
        </div>

        <!-- Notifications List -->
        @if($notifications->count() > 0)
            <div class="space-y-4">
                @foreach($notifications as $notification)
                    <div class="card bg-base-100 shadow-md {{ !$notification->is_read ? 'border-l-4 border-primary' : '' }}">
                        <div class="card-body">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <div class="avatar placeholder">
                                            <div class="bg-primary text-primary-content rounded-full w-10">
                                                <i class="fas fa-bell"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <h3 class="font-bold text-lg">{{ $notification->title }}</h3>
                                            <div class="flex items-center gap-2 text-sm text-base-content/70">
                                                <span class="badge badge-{{ $notification->type == 'system' ? 'info' : ($notification->type == 'promotion' ? 'warning' : 'primary') }} badge-sm">
                                                    {{ ucfirst($notification->type) }}
                                                </span>
                                                <span>{{ $notification->created_at->locale('ar')->diffForHumans() }}</span>
                                                @if($notification->is_read)
                                                    <span class="badge badge-success badge-sm">{{ __('مقروء') }}</span>
                                                @else
                                                    <span class="badge badge-error badge-sm">{{ __('غير مقروء') }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <p class="text-base-content/80 mb-3">{{ $notification->body }}</p>
                                    
                                    @if($notification->data)
                                        @php
                                            $data = is_string($notification->data) ? json_decode($notification->data, true) : $notification->data;
                                        @endphp
                                        @if($data && is_array($data))
                                            <div class="bg-base-200 p-3 rounded-lg text-sm">
                                                @foreach($data as $key => $value)
                                                    <div><strong>{{ $key }}:</strong> {{ $value }}</div>
                                                @endforeach
                                            </div>
                                        @endif
                                    @endif
                                </div>
                                
                                <div class="dropdown dropdown-end">
                                    <label tabindex="0" class="btn btn-ghost btn-sm btn-circle">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </label>
                                    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                        @if(!$notification->is_read)
                                            <li>
                                                <form action="{{ route('web.notifications.read', $notification) }}" method="POST">
                                                    @csrf
                                                    <button type="submit" class="w-full text-left">
                                                        <i class="fas fa-check mr-2 rtl:ml-2 rtl:mr-0"></i>
                                                        {{ __('تحديد كمقروء') }}
                                                    </button>
                                                </form>
                                            </li>
                                        @endif
                                        <li>
                                            <a href="{{ route('web.notifications.show', $notification) }}">
                                                <i class="fas fa-eye mr-2 rtl:ml-2 rtl:mr-0"></i>
                                                {{ __('عرض التفاصيل') }}
                                            </a>
                                        </li>
                                        <li>
                                            <form action="{{ route('web.notifications.destroy', $notification) }}" method="POST"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="w-full text-left text-error">
                                                    <i class="fas fa-trash mr-2 rtl:ml-2 rtl:mr-0"></i>
                                                    {{ __('حذف') }}
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $notifications->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="avatar placeholder mb-4">
                    <div class="bg-base-200 text-base-content rounded-full w-24">
                        <i class="fas fa-bell text-4xl"></i>
                    </div>
                </div>
                <h3 class="text-xl font-bold mb-2">{{ __('لا توجد إشعارات') }}</h3>
                <p class="text-base-content/70">{{ __('ستظهر إشعاراتك هنا عند وصولها') }}</p>
            </div>
        @endif
    </div>
</div>
@endsection
