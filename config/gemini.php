<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Gemini API Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Gemini AI API integration.
    | You can set your API key and other settings here.
    |
    */

    'api_key' => env('GEMINI_API_KEY', ''),
    
    'api_url' => env('GEMINI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/'),
    
    'model' => env('GEMINI_MODEL', 'gemini-pro-vision'),
    
    'text_model' => env('GEMINI_TEXT_MODEL', 'gemini-pro'),
    
    'max_output_tokens' => env('GEMINI_MAX_OUTPUT_TOKENS', 2048),
    
    'temperature' => env('GEMINI_TEMPERATURE', 0.4),
    
    'top_p' => env('GEMINI_TOP_P', 1.0),
    
    'top_k' => env('GEMINI_TOP_K', 32),
    
    /*
    |--------------------------------------------------------------------------
    | Content Moderation Settings
    |--------------------------------------------------------------------------
    |
    | Configure the content moderation settings for product reviews.
    |
    */
    
    'prohibited_categories' => [
        'hate_speech',
        'dangerous_content',
        'harassment',
        'sexually_explicit',
        'copyright_violation',
    ],
    
    'prohibited_words' => [
        'scam',
        'illegal',
        'fake',
        'counterfeit',
        'replica',
        // Add more prohibited words as needed
    ],
];
