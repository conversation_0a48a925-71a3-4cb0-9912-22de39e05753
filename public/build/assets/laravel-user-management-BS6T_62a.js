$(function(){var c=$(".datatables-users"),u=$(".select2"),f=baseUrl+"app/user/view/account",l=$("#offcanvasAddUser");if(u.length){var m=u;m.wrap('<div class="position-relative"></div>').select2({placeholder:"Select Country",dropdownParent:m.parent()})}if($.ajaxSetup({headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")}}),c.length)var p=c.DataTable({processing:!0,serverSide:!0,ajax:{url:baseUrl+"user-list"},columns:[{data:""},{data:"id"},{data:"name"},{data:"email"},{data:"email_verified_at"},{data:"action"}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(t,n,a,r){return""}},{searchable:!1,orderable:!1,targets:1,render:function(t,n,a,r){return`<span>${a.fake_id}</span>`}},{targets:2,responsivePriority:4,render:function(t,n,a,r){var d=a.name,e=Math.floor(Math.random()*6),o=["success","danger","warning","info","dark","primary","secondary"],s=o[e],d=a.name,i=d.match(/\b\w/g)||[],x;i=((i.shift()||"")+(i.pop()||"")).toUpperCase(),x='<span class="avatar-initial rounded-circle bg-label-'+s+'">'+i+"</span>";var g='<div class="d-flex justify-content-start align-items-center user-name"><div class="avatar-wrapper"><div class="avatar avatar-sm me-4">'+x+'</div></div><div class="d-flex flex-column"><a href="'+f+'" class="text-heading text-truncate"><span class="fw-medium">'+d+"</span></a></div></div>";return g}},{targets:3,render:function(t,n,a,r){var e=a.email;return'<span class="user-email">'+e+"</span>"}},{targets:4,className:"text-center",render:function(t,n,a,r){var e=a.email_verified_at;return`${e?'<i class="ti fs-4 ti-shield-check text-success"></i>':'<i class="ti fs-4 ti-shield-x text-danger" ></i>'}`}},{targets:-1,title:"Actions",searchable:!1,orderable:!1,render:function(t,n,a,r){return`<div class="d-flex align-items-center gap-50"><button class="btn btn-sm btn-icon edit-record btn-text-secondary rounded-pill waves-effect" data-id="${a.id}" data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddUser"><i class="ti ti-edit"></i></button><button class="btn btn-sm btn-icon delete-record btn-text-secondary rounded-pill waves-effect" data-id="${a.id}"><i class="ti ti-trash"></i></button><button class="btn btn-sm btn-icon btn-text-secondary rounded-pill waves-effect dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button><div class="dropdown-menu dropdown-menu-end m-0"><a href="`+f+'" class="dropdown-item">View</a><a href="javascript:;" class="dropdown-item">Suspend</a></div></div>'}}],order:[[2,"desc"]],dom:'<"row"<"col-md-2"<"ms-n2"l>><"col-md-10"<"dt-action-buttons text-xl-end text-lg-start text-md-end text-start d-flex align-items-center justify-content-end flex-md-row flex-column mb-6 mb-md-0 mt-n6 mt-md-0"fB>>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',lengthMenu:[7,10,20,50,70,100],language:{sLengthMenu:"_MENU_",search:"",searchPlaceholder:"Search User",info:"Displaying _START_ to _END_ of _TOTAL_ entries",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-secondary dropdown-toggle mx-4 waves-effect waves-light",text:'<i class="ti ti-upload me-2 ti-xs"></i>Export',buttons:[{extend:"print",title:"Users",text:'<i class="ti ti-printer me-2" ></i>Print',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,n,a){if(t.length<=0)return t;var r=$.parseHTML(t),e="";return $.each(r,function(o,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}},customize:function(t){$(t.document.body).css("color",config.colors.headingColor).css("border-color",config.colors.borderColor).css("background-color",config.colors.body),$(t.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",title:"Users",text:'<i class="ti ti-file-text me-2" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,n,a){if(t.length<=0)return t;var r=$.parseHTML(t),e="";return $.each(r,function(o,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"excel",title:"Users",text:'<i class="ti ti-file-spreadsheet me-2"></i>Excel',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,n,a){if(t.length<=0)return t;var r=$.parseHTML(t),e="";return $.each(r,function(o,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"pdf",title:"Users",text:'<i class="ti ti-file-code-2 me-2"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,n,a){if(t.length<=0)return t;var r=$.parseHTML(t),e="";return $.each(r,function(o,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}},{extend:"copy",title:"Users",text:'<i class="ti ti-copy me-2" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[1,2,3,4,5],format:{body:function(t,n,a){if(t.length<=0)return t;var r=$.parseHTML(t),e="";return $.each(r,function(o,s){s.classList!==void 0&&s.classList.contains("user-name")?e=e+s.lastChild.firstChild.textContent:s.innerText===void 0?e=e+s.textContent:e=e+s.innerText}),e}}}}]},{text:'<i class="ti ti-plus me-0 me-sm-1 ti-xs"></i><span class="d-none d-sm-inline-block">Add New User</span>',className:"add-new btn btn-primary waves-effect waves-light",attr:{"data-bs-toggle":"offcanvas","data-bs-target":"#offcanvasAddUser"}}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var n=t.data();return"Details of "+n.name}}),type:"column",renderer:function(t,n,a){var r=$.map(a,function(e,o){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return r?$('<table class="table"/><tbody />').append(r):!1}}}});$(document).on("click",".delete-record",function(){var t=$(this).data("id"),n=$(".dtr-bs-modal.show");n.length&&n.modal("hide"),Swal.fire({title:"Are you sure?",text:"You won't be able to revert this!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",customClass:{confirmButton:"btn btn-primary me-3",cancelButton:"btn btn-label-secondary"},buttonsStyling:!1}).then(function(a){a.value?($.ajax({type:"DELETE",url:`${baseUrl}user-list/${t}`,success:function(){p.draw()},error:function(r){console.log(r)}}),Swal.fire({icon:"success",title:"Deleted!",text:"The user has been deleted!",customClass:{confirmButton:"btn btn-success"}})):a.dismiss===Swal.DismissReason.cancel&&Swal.fire({title:"Cancelled",text:"The User is not deleted!",icon:"error",customClass:{confirmButton:"btn btn-success"}})})}),$(document).on("click",".edit-record",function(){var t=$(this).data("id"),n=$(".dtr-bs-modal.show");n.length&&n.modal("hide"),$("#offcanvasAddUserLabel").html("Edit User"),$.get(`${baseUrl}user-list/${t}/edit`,function(a){$("#user_id").val(a.id),$("#add-user-fullname").val(a.name),$("#add-user-email").val(a.email)})}),$(".add-new").on("click",function(){$("#user_id").val(""),$("#offcanvasAddUserLabel").html("Add User")}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300);const v=document.getElementById("addNewUserForm"),h=FormValidation.formValidation(v,{fields:{name:{validators:{notEmpty:{message:"Please enter fullname"}}},email:{validators:{notEmpty:{message:"Please enter your email"},emailAddress:{message:"The value is not a valid email address"}}},userContact:{validators:{notEmpty:{message:"Please enter your contact"}}},company:{validators:{notEmpty:{message:"Please enter your company"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(t,n){return".mb-6"}}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus}}).on("core.form.valid",function(){$.ajax({data:$("#addNewUserForm").serialize(),url:`${baseUrl}user-list`,type:"POST",success:function(t){p.draw(),l.offcanvas("hide"),Swal.fire({icon:"success",title:`Successfully ${t}!`,text:`User ${t} Successfully.`,customClass:{confirmButton:"btn btn-success"}})},error:function(t){l.offcanvas("hide"),Swal.fire({title:"Duplicate Entry!",text:"Your email should be unique.",icon:"error",customClass:{confirmButton:"btn btn-success"}})}})});l.on("hidden.bs.offcanvas",function(){h.resetForm(!0)});const b=document.querySelectorAll(".phone-mask");b&&b.forEach(function(t){new Cleave(t,{phone:!0,phoneRegionCode:"US"})})});
