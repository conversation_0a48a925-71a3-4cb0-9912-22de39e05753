(function(){const t=document.querySelector(".billings-zip-code"),n=document.querySelector(".billing-card-mask"),r=document.querySelector(".billing-expiry-date-mask"),c=document.querySelector(".billing-cvv-mask"),i=document.querySelectorAll(".form-check-input-payment");t&&new Cleave(t,{delimiter:"",numeral:!0}),n&&new Cleave(n,{creditCard:!0,onCreditCardTypeChanged:function(e){e!=""&&e!="unknown"?document.querySelector(".card-type").innerHTML='<img src="'+assetsPath+"img/icons/payments/"+e+'-cc.png" height="28"/>':document.querySelector(".card-type").innerHTML=""}}),r&&new Cleave(r,{date:!0,delimiter:"/",datePattern:["m","y"]}),c&&new Cleave(c,{numeral:!0,numeralPositiveOnly:!0}),i&&i.forEach(function(e){e.addEventListener("change",function(a){a.target.value==="credit-card"?document.querySelector("#form-credit-card").classList.remove("d-none"):document.querySelector("#form-credit-card").classList.add("d-none")})})})();
