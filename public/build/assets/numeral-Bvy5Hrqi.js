import{g as z}from"./_commonjsHelpers-BosuxZz1.js";var M={exports:{}};/*! @preserve
 * numeral.js
 * version : 2.0.6
 * author : <PERSON>
 * license : MIT
 * http://adamwdraper.github.com/Numeral-js/
 */var j=M.exports,L;function R(){return L||(L=1,function(B){(function(n,s){B.exports?B.exports=s():n.numeral=s()})(j,function(){var n,s,c="2.0.6",y={},F={},_={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},b={currentLocale:_.currentLocale,zeroFormat:_.zeroFormat,nullFormat:_.nullFormat,defaultFormat:_.defaultFormat,scalePercentBy100:_.scalePercentBy100};function T(e,i){this._input=e,this._value=i}return n=function(e){var i,l,t,r;if(n.isNumeral(e))i=e.value();else if(e===0||typeof e>"u")i=0;else if(e===null||s.isNaN(e))i=null;else if(typeof e=="string")if(b.zeroFormat&&e===b.zeroFormat)i=0;else if(b.nullFormat&&e===b.nullFormat||!e.replace(/[^0-9]+/g,"").length)i=null;else{for(l in y)if(r=typeof y[l].regexps.unformat=="function"?y[l].regexps.unformat():y[l].regexps.unformat,r&&e.match(r)){t=y[l].unformat;break}t=t||n._.stringToNumber,i=t(e)}else i=Number(e)||null;return new T(e,i)},n.version=c,n.isNumeral=function(e){return e instanceof T},n._=s={numberToFormat:function(e,i,l){var t=F[n.options.currentLocale],r=!1,o=!1,a=0,u="",f=1e12,d=1e9,h=1e6,k=1e3,p="",N=!1,g,w,m,v,E,P,x;if(e=e||0,w=Math.abs(e),n._.includes(i,"(")?(r=!0,i=i.replace(/[\(|\)]/g,"")):(n._.includes(i,"+")||n._.includes(i,"-"))&&(E=n._.includes(i,"+")?i.indexOf("+"):e<0?i.indexOf("-"):-1,i=i.replace(/[\+|\-]/g,"")),n._.includes(i,"a")&&(g=i.match(/a(k|m|b|t)?/),g=g?g[1]:!1,n._.includes(i," a")&&(u=" "),i=i.replace(new RegExp(u+"a[kmbt]?"),""),w>=f&&!g||g==="t"?(u+=t.abbreviations.trillion,e=e/f):w<f&&w>=d&&!g||g==="b"?(u+=t.abbreviations.billion,e=e/d):w<d&&w>=h&&!g||g==="m"?(u+=t.abbreviations.million,e=e/h):(w<h&&w>=k&&!g||g==="k")&&(u+=t.abbreviations.thousand,e=e/k)),n._.includes(i,"[.]")&&(o=!0,i=i.replace("[.]",".")),m=e.toString().split(".")[0],v=i.split(".")[1],P=i.indexOf(","),a=(i.split(".")[0].split(",")[0].match(/0/g)||[]).length,v?(n._.includes(v,"[")?(v=v.replace("]",""),v=v.split("["),p=n._.toFixed(e,v[0].length+v[1].length,l,v[1].length)):p=n._.toFixed(e,v.length,l),m=p.split(".")[0],n._.includes(p,".")?p=t.delimiters.decimal+p.split(".")[1]:p="",o&&Number(p.slice(1))===0&&(p="")):m=n._.toFixed(e,0,l),u&&!g&&Number(m)>=1e3&&u!==t.abbreviations.trillion)switch(m=String(Number(m)/1e3),u){case t.abbreviations.thousand:u=t.abbreviations.million;break;case t.abbreviations.million:u=t.abbreviations.billion;break;case t.abbreviations.billion:u=t.abbreviations.trillion;break}if(n._.includes(m,"-")&&(m=m.slice(1),N=!0),m.length<a)for(var S=a-m.length;S>0;S--)m="0"+m;return P>-1&&(m=m.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+t.delimiters.thousands)),i.indexOf(".")===0&&(m=""),x=m+p+(u||""),r?x=(r&&N?"(":"")+x+(r&&N?")":""):E>=0?x=E===0?(N?"-":"+")+x:x+(N?"-":"+"):N&&(x="-"+x),x},stringToNumber:function(e){var i=F[b.currentLocale],l=e,t={thousand:3,million:6,billion:9,trillion:12},r,o,a;if(b.zeroFormat&&e===b.zeroFormat)o=0;else if(b.nullFormat&&e===b.nullFormat||!e.replace(/[^0-9]+/g,"").length)o=null;else{o=1,i.delimiters.decimal!=="."&&(e=e.replace(/\./g,"").replace(i.delimiters.decimal,"."));for(r in t)if(a=new RegExp("[^a-zA-Z]"+i.abbreviations[r]+"(?:\\)|(\\"+i.currency.symbol+")?(?:\\))?)?$"),l.match(a)){o*=Math.pow(10,t[r]);break}o*=(e.split("-").length+Math.min(e.split("(").length-1,e.split(")").length-1))%2?1:-1,e=e.replace(/[^0-9\.]+/g,""),o*=Number(e)}return o},isNaN:function(e){return typeof e=="number"&&isNaN(e)},includes:function(e,i){return e.indexOf(i)!==-1},insert:function(e,i,l){return e.slice(0,l)+i+e.slice(l)},reduce:function(e,i){if(this===null)throw new TypeError("Array.prototype.reduce called on null or undefined");if(typeof i!="function")throw new TypeError(i+" is not a function");var l=Object(e),t=l.length>>>0,r=0,o;if(arguments.length===3)o=arguments[2];else{for(;r<t&&!(r in l);)r++;if(r>=t)throw new TypeError("Reduce of empty array with no initial value");o=l[r++]}for(;r<t;r++)r in l&&(o=i(o,l[r],r,l));return o},multiplier:function(e){var i=e.toString().split(".");return i.length<2?1:Math.pow(10,i[1].length)},correctionFactor:function(){var e=Array.prototype.slice.call(arguments);return e.reduce(function(i,l){var t=s.multiplier(l);return i>t?i:t},1)},toFixed:function(e,i,l,t){var r=e.toString().split("."),o=i-(t||0),a,u,f,d;return r.length===2?a=Math.min(Math.max(r[1].length,o),i):a=o,f=Math.pow(10,a),d=(l(e+"e+"+a)/f).toFixed(a),t>i-a&&(u=new RegExp("\\.?0{1,"+(t-(i-a))+"}$"),d=d.replace(u,"")),d}},n.options=b,n.formats=y,n.locales=F,n.locale=function(e){return e&&(b.currentLocale=e.toLowerCase()),b.currentLocale},n.localeData=function(e){if(!e)return F[b.currentLocale];if(e=e.toLowerCase(),!F[e])throw new Error("Unknown locale : "+e);return F[e]},n.reset=function(){for(var e in _)b[e]=_[e]},n.zeroFormat=function(e){b.zeroFormat=typeof e=="string"?e:null},n.nullFormat=function(e){b.nullFormat=typeof e=="string"?e:null},n.defaultFormat=function(e){b.defaultFormat=typeof e=="string"?e:"0.0"},n.register=function(e,i,l){if(i=i.toLowerCase(),this[e+"s"][i])throw new TypeError(i+" "+e+" already registered.");return this[e+"s"][i]=l,l},n.validate=function(e,i){var l,t,r,o,a,u,f,d;if(typeof e!="string"&&(e+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",e)),e=e.trim(),e.match(/^\d+$/))return!0;if(e==="")return!1;try{f=n.localeData(i)}catch{f=n.localeData(n.locale())}return r=f.currency.symbol,a=f.abbreviations,l=f.delimiters.decimal,f.delimiters.thousands==="."?t="\\.":t=f.delimiters.thousands,d=e.match(/^[^\d]+/),d!==null&&(e=e.substr(1),d[0]!==r)||(d=e.match(/[^\d]+$/),d!==null&&(e=e.slice(0,-1),d[0]!==a.thousand&&d[0]!==a.million&&d[0]!==a.billion&&d[0]!==a.trillion))?!1:(u=new RegExp(t+"{2}"),e.match(/[^\d.,]/g)?!1:(o=e.split(l),o.length>2?!1:o.length<2?!!o[0].match(/^\d+.*\d$/)&&!o[0].match(u):o[0].length===1?!!o[0].match(/^\d+$/)&&!o[0].match(u)&&!!o[1].match(/^\d+$/):!!o[0].match(/^\d+.*\d$/)&&!o[0].match(u)&&!!o[1].match(/^\d+$/)))},n.fn=T.prototype={clone:function(){return n(this)},format:function(e,i){var l=this._value,t=e||b.defaultFormat,r,o,a;if(i=i||Math.round,l===0&&b.zeroFormat!==null)o=b.zeroFormat;else if(l===null&&b.nullFormat!==null)o=b.nullFormat;else{for(r in y)if(t.match(y[r].regexps.format)){a=y[r].format;break}a=a||n._.numberToFormat,o=a(l,t,i)}return o},value:function(){return this._value},input:function(){return this._input},set:function(e){return this._value=Number(e),this},add:function(e){var i=s.correctionFactor.call(null,this._value,e);function l(t,r,o,a){return t+Math.round(i*r)}return this._value=s.reduce([this._value,e],l,0)/i,this},subtract:function(e){var i=s.correctionFactor.call(null,this._value,e);function l(t,r,o,a){return t-Math.round(i*r)}return this._value=s.reduce([e],l,Math.round(this._value*i))/i,this},multiply:function(e){function i(l,t,r,o){var a=s.correctionFactor(l,t);return Math.round(l*a)*Math.round(t*a)/Math.round(a*a)}return this._value=s.reduce([this._value,e],i,1),this},divide:function(e){function i(l,t,r,o){var a=s.correctionFactor(l,t);return Math.round(l*a)/Math.round(t*a)}return this._value=s.reduce([this._value,e],i),this},difference:function(e){return Math.abs(n(this._value).subtract(e).value())}},n.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(e){var i=e%10;return~~(e%100/10)===1?"th":i===1?"st":i===2?"nd":i===3?"rd":"th"},currency:{symbol:"$"}}),function(){n.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(e,i,l){var t=n._.includes(i," BPS")?" ":"",r;return e=e*1e4,i=i.replace(/\s?BPS/,""),r=n._.numberToFormat(e,i,l),n._.includes(r,")")?(r=r.split(""),r.splice(-1,0,t+"BPS"),r=r.join("")):r=r+t+"BPS",r},unformat:function(e){return+(n._.stringToNumber(e)*1e-4).toFixed(15)}})}(),function(){var e={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},i={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},l=e.suffixes.concat(i.suffixes.filter(function(r){return e.suffixes.indexOf(r)<0})),t=l.join("|");t="("+t.replace("B","B(?!PS)")+")",n.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(t)},format:function(r,o,a){var u,f=n._.includes(o,"ib")?i:e,d=n._.includes(o," b")||n._.includes(o," ib")?" ":"",h,k,p;for(o=o.replace(/\s?i?b/,""),h=0;h<=f.suffixes.length;h++)if(k=Math.pow(f.base,h),p=Math.pow(f.base,h+1),r===null||r===0||r>=k&&r<p){d+=f.suffixes[h],k>0&&(r=r/k);break}return u=n._.numberToFormat(r,o,a),u+d},unformat:function(r){var o=n._.stringToNumber(r),a,u;if(o){for(a=e.suffixes.length-1;a>=0;a--){if(n._.includes(r,e.suffixes[a])){u=Math.pow(e.base,a);break}if(n._.includes(r,i.suffixes[a])){u=Math.pow(i.base,a);break}}o*=u||1}return o}})}(),function(){n.register("format","currency",{regexps:{format:/(\$)/},format:function(e,i,l){var t=n.locales[n.options.currentLocale],r={before:i.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:i.match(/([\+|\-|\)|\s|\$]*)$/)[0]},o,a,u;for(i=i.replace(/\s?\$\s?/,""),o=n._.numberToFormat(e,i,l),e>=0?(r.before=r.before.replace(/[\-\(]/,""),r.after=r.after.replace(/[\-\)]/,"")):e<0&&!n._.includes(r.before,"-")&&!n._.includes(r.before,"(")&&(r.before="-"+r.before),u=0;u<r.before.length;u++)switch(a=r.before[u],a){case"$":o=n._.insert(o,t.currency.symbol,u);break;case" ":o=n._.insert(o," ",u+t.currency.symbol.length-1);break}for(u=r.after.length-1;u>=0;u--)switch(a=r.after[u],a){case"$":o=u===r.after.length-1?o+t.currency.symbol:n._.insert(o,t.currency.symbol,-(r.after.length-(1+u)));break;case" ":o=u===r.after.length-1?o+" ":n._.insert(o," ",-(r.after.length-(1+u)+t.currency.symbol.length-1));break}return o}})}(),function(){n.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(e,i,l){var t,r=typeof e=="number"&&!n._.isNaN(e)?e.toExponential():"0e+0",o=r.split("e");return i=i.replace(/e[\+|\-]{1}0/,""),t=n._.numberToFormat(Number(o[0]),i,l),t+"e"+o[1]},unformat:function(e){var i=n._.includes(e,"e+")?e.split("e+"):e.split("e-"),l=Number(i[0]),t=Number(i[1]);t=n._.includes(e,"e-")?t*=-1:t;function r(o,a,u,f){var d=n._.correctionFactor(o,a),h=o*d*(a*d)/(d*d);return h}return n._.reduce([l,Math.pow(10,t)],r,1)}})}(),function(){n.register("format","ordinal",{regexps:{format:/(o)/},format:function(e,i,l){var t=n.locales[n.options.currentLocale],r,o=n._.includes(i," o")?" ":"";return i=i.replace(/\s?o/,""),o+=t.ordinal(e),r=n._.numberToFormat(e,i,l),r+o}})}(),function(){n.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(e,i,l){var t=n._.includes(i," %")?" ":"",r;return n.options.scalePercentBy100&&(e=e*100),i=i.replace(/\s?\%/,""),r=n._.numberToFormat(e,i,l),n._.includes(r,")")?(r=r.split(""),r.splice(-1,0,t+"%"),r=r.join("")):r=r+t+"%",r},unformat:function(e){var i=n._.stringToNumber(e);return n.options.scalePercentBy100?i*.01:i}})}(),function(){n.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(e,i,l){var t=Math.floor(e/60/60),r=Math.floor((e-t*60*60)/60),o=Math.round(e-t*60*60-r*60);return t+":"+(r<10?"0"+r:r)+":"+(o<10?"0"+o:o)},unformat:function(e){var i=e.split(":"),l=0;return i.length===3?(l=l+Number(i[0])*60*60,l=l+Number(i[1])*60,l=l+Number(i[2])):i.length===2&&(l=l+Number(i[0])*60,l=l+Number(i[1])),Number(l)}})}(),n})}(M)),M.exports}var C=R();const I=z(C);var $={exports:{}};/*! @preserve
 * numeral.js
 * locales : 2.0.6
 * license : MIT
 * http://adamwdraper.github.com/Numeral-js/
 */var A=$.exports,O;function D(){return O||(O=1,function(B){(function(n,s){B.exports?s(R()):s(n.numeral)})(A,function(n){(function(){n.register("locale","bg",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"хил",million:"млн",billion:"млрд",trillion:"трлн"},ordinal:function(s){return""},currency:{symbol:"лв"}})})(),function(){n.register("locale","chs",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(s){return"."},currency:{symbol:"¥"}})}(),function(){n.register("locale","cs",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Kč"}})}(),function(){n.register("locale","da-dk",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mio",billion:"mia",trillion:"b"},ordinal:function(s){return"."},currency:{symbol:"DKK"}})}(),function(){n.register("locale","de-ch",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"CHF"}})}(),function(){n.register("locale","de",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","en-au",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"$"}})}(),function(){n.register("locale","en-gb",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"£"}})}(),function(){n.register("locale","en-za",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return~~(s%100/10)===1?"th":c===1?"st":c===2?"nd":c===3?"rd":"th"},currency:{symbol:"R"}})}(),function(){n.register("locale","es-es",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return c===1||c===3?"er":c===2?"do":c===7||c===0?"mo":c===8?"vo":c===9?"no":"to"},currency:{symbol:"€"}})}(),function(){n.register("locale","es",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(s){var c=s%10;return c===1||c===3?"er":c===2?"do":c===7||c===0?"mo":c===8?"vo":c===9?"no":"to"},currency:{symbol:"$"}})}(),function(){n.register("locale","et",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:" tuh",million:" mln",billion:" mld",trillion:" trl"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","fi",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","fr-ca",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"$"}})}(),function(){n.register("locale","fr-ch",{delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"CHF"}})}(),function(){n.register("locale","fr",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return s===1?"er":"e"},currency:{symbol:"€"}})}(),function(){n.register("locale","hu",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"E",million:"M",billion:"Mrd",trillion:"T"},ordinal:function(s){return"."},currency:{symbol:" Ft"}})}(),function(){n.register("locale","it",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"€"}})}(),function(){n.register("locale","ja",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十億",trillion:"兆"},ordinal:function(s){return"."},currency:{symbol:"¥"}})}(),function(){n.register("locale","lv",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:" tūkst.",million:" milj.",billion:" mljrd.",trillion:" trilj."},ordinal:function(s){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","nl-be",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:" mln",billion:" mld",trillion:" bln"},ordinal:function(s){var c=s%100;return s!==0&&c<=1||c===8||c>=20?"ste":"de"},currency:{symbol:"€ "}})}(),function(){n.register("locale","nl-nl",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mrd",trillion:"bln"},ordinal:function(s){var c=s%100;return s!==0&&c<=1||c===8||c>=20?"ste":"de"},currency:{symbol:"€ "}})}(),function(){n.register("locale","no",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"."},currency:{symbol:"kr"}})}(),function(){n.register("locale","pl",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tys.",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(s){return"."},currency:{symbol:"PLN"}})}(),function(){n.register("locale","pt-br",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mil",million:"milhões",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"R$"}})}(),function(){n.register("locale","pt-pt",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(s){return"º"},currency:{symbol:"€"}})}(),function(){n.register("locale","ru-ua",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"₴"}})}(),function(){n.register("locale","ru",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн.",billion:"млрд.",trillion:"трлн."},ordinal:function(){return"."},currency:{symbol:"руб."}})}(),function(){n.register("locale","sk",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","sl",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mio",billion:"mrd",trillion:"trilijon"},ordinal:function(){return"."},currency:{symbol:"€"}})}(),function(){n.register("locale","th",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"พัน",million:"ล้าน",billion:"พันล้าน",trillion:"ล้านล้าน"},ordinal:function(s){return"."},currency:{symbol:"฿"}})}(),function(){var s={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};n.register("locale","tr",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"bin",million:"milyon",billion:"milyar",trillion:"trilyon"},ordinal:function(c){if(c===0)return"'ıncı";var y=c%10,F=c%100-y,_=c>=100?100:null;return s[y]||s[F]||s[_]},currency:{symbol:"₺"}})}(),function(){n.register("locale","uk-ua",{delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тис.",million:"млн",billion:"млрд",trillion:"блн"},ordinal:function(){return""},currency:{symbol:"₴"}})}(),function(){n.register("locale","vi",{delimiters:{thousands:".",decimal:","},abbreviations:{thousand:" nghìn",million:" triệu",billion:" tỷ",trillion:" nghìn tỷ"},ordinal:function(){return"."},currency:{symbol:"₫"}})}()})}($)),$.exports}D();try{window.numeral=I}catch{}
