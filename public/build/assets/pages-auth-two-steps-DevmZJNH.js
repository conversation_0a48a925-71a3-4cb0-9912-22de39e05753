document.addEventListener("DOMContentLoaded",function(r){(function(){let a=document.querySelector(".numeral-mask-wrapper");for(let e of a.children)e.onkeyup=function(t){/^\d$/.test(t.key)?e.nextElementSibling&&this.value.length===parseInt(this.attributes.maxlength.value)&&e.nextElementSibling.focus():t.key==="Backspace"&&e.previousElementSibling&&e.previousElementSibling.focus()},e.onkeypress=function(t){t.key==="-"&&t.preventDefault()};const n=document.querySelector("#twoStepsForm");if(n){FormValidation.formValidation(n,{fields:{otp:{validators:{notEmpty:{message:"Please enter otp"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".mb-6"}),submitButton:new FormValidation.plugins.SubmitButton,defaultSubmit:new FormValidation.plugins.DefaultSubmit,autoFocus:new FormValidation.plugins.AutoFocus}});const e=n.querySelectorAll(".numeral-mask"),t=function(){let o=!0,i="";e.forEach(l=>{l.value===""&&(o=!1,n.querySelector('[name="otp"]').value=""),i=i+l.value}),o&&(n.querySelector('[name="otp"]').value=i)};e.forEach(o=>{o.addEventListener("keyup",t)})}})()});
