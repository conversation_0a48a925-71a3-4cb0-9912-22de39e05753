let b,v;document.addEventListener("DOMContentLoaded",function(p){(function(){const u=document.getElementById("form-add-new-record");setTimeout(()=>{const d=document.querySelector(".create-new"),i=document.querySelector("#add-new-record");d&&d.addEventListener("click",function(){v=new bootstrap.Offcanvas(i),i.querySelector(".dt-full-name").value="",i.querySelector(".dt-post").value="",i.querySelector(".dt-email").value="",i.querySelector(".dt-date").value="",i.querySelector(".dt-salary").value="",v.show()})},200),b=FormValidation.formValidation(u,{fields:{basicFullname:{validators:{notEmpty:{message:"The name is required"}}},basicPost:{validators:{notEmpty:{message:"Post field is required"}}},basicEmail:{validators:{notEmpty:{message:"The Email is required"},emailAddress:{message:"The value is not a valid email address"}}},basicDate:{validators:{notEmpty:{message:"Joining Date is required"},date:{format:"MM/DD/YYYY",message:"The value is not a valid date"}}},basicSalary:{validators:{notEmpty:{message:"Basic Salary is required"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-12"}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus},init:d=>{d.on("plugins.message.placed",function(i){i.element.parentElement.classList.contains("input-group")&&i.element.parentElement.insertAdjacentElement("afterend",i.messageElement)})}});const c=document.querySelector('[name="basicDate"]');c&&c.flatpickr({enableTime:!1,dateFormat:"m/d/Y",onChange:function(){b.revalidateField("basicDate")}})})()});$(function(){var p=$(".datatables-basic"),u=$(".dt-complex-header"),c=$(".dt-row-grouping"),d=$(".dt-multilingual"),i;p.length&&(i=p.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"id"},{data:"id"},{data:"full_name"},{data:"email"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!0,searchable:!1,responsivePriority:2,targets:0,render:function(t,r,n,s){return""}},{targets:1,orderable:!0,searchable:!1,responsivePriority:3,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,searchable:!1,visible:!1},{targets:3,responsivePriority:4,render:function(t,r,n,s){var e=n.avatar,l=n.full_name,a=n.post;if(e)var h='<img src="'+assetsPath+"img/avatars/"+e+'" alt="Avatar" class="rounded-circle">';else{var x=Math.floor(Math.random()*6),y=["success","danger","warning","info","primary","secondary"],j=y[x],l=n.full_name,m=l.match(/\b\w/g)||[];m=((m.shift()||"")+(m.pop()||"")).toUpperCase(),h='<span class="avatar-initial rounded-circle bg-label-'+j+'">'+m+"</span>"}var C='<div class="d-flex justify-content-start align-items-center user-name"><div class="avatar-wrapper"><div class="avatar me-2">'+h+'</div></div><div class="d-flex flex-column"><span class="emp_name text-truncate">'+l+'</span><small class="emp_post text-truncate text-muted">'+a+"</small></div></div>";return C}},{responsivePriority:1,targets:4},{targets:-2,render:function(t,r,n,s){var e=n.status,l={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return typeof l[e]>"u"?t:'<span class="badge '+l[e].class+'">'+l[e].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(t,r,n,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><ul class="dropdown-menu dropdown-menu-end m-0"><li><a href="javascript:;" class="dropdown-item">Details</a></li><li><a href="javascript:;" class="dropdown-item">Archive</a></li><div class="dropdown-divider"></div><li><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></li></ul></div><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon item-edit"><i class="ti ti-pencil ti-md"></i></a>'}}],order:[[2,"desc"]],dom:'<"card-header flex-column flex-md-row"<"head-label text-center"><"dt-action-buttons text-end pt-6 pt-md-0"B>><"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end mt-n6 mt-md-0"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],language:{paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},buttons:[{extend:"collection",className:"btn btn-label-primary dropdown-toggle me-4 waves-effect waves-light border-none",text:'<i class="ti ti-file-export ti-xs me-sm-1"></i> <span class="d-none d-sm-inline-block">Export</span>',buttons:[{extend:"print",text:'<i class="ti ti-printer me-1" ></i>Print',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(t,r,n){if(t.length<=0)return t;var s=$.parseHTML(t),e="";return $.each(s,function(l,a){a.classList!==void 0&&a.classList.contains("user-name")?e=e+a.lastChild.firstChild.textContent:a.innerText===void 0?e=e+a.textContent:e=e+a.innerText}),e}}},customize:function(t){$(t.document.body).css("color",config.colors.headingColor).css("border-color",config.colors.borderColor).css("background-color",config.colors.bodyBg),$(t.document.body).find("table").addClass("compact").css("color","inherit").css("border-color","inherit").css("background-color","inherit")}},{extend:"csv",text:'<i class="ti ti-file-text me-1" ></i>Csv',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(t,r,n){if(t.length<=0)return t;var s=$.parseHTML(t),e="";return $.each(s,function(l,a){a.classList!==void 0&&a.classList.contains("user-name")?e=e+a.lastChild.firstChild.textContent:a.innerText===void 0?e=e+a.textContent:e=e+a.innerText}),e}}}},{extend:"excel",text:'<i class="ti ti-file-spreadsheet me-1"></i>Excel',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(t,r,n){if(t.length<=0)return t;var s=$.parseHTML(t),e="";return $.each(s,function(l,a){a.classList!==void 0&&a.classList.contains("user-name")?e=e+a.lastChild.firstChild.textContent:a.innerText===void 0?e=e+a.textContent:e=e+a.innerText}),e}}}},{extend:"pdf",text:'<i class="ti ti-file-description me-1"></i>Pdf',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(t,r,n){if(t.length<=0)return t;var s=$.parseHTML(t),e="";return $.each(s,function(l,a){a.classList!==void 0&&a.classList.contains("user-name")?e=e+a.lastChild.firstChild.textContent:a.innerText===void 0?e=e+a.textContent:e=e+a.innerText}),e}}}},{extend:"copy",text:'<i class="ti ti-copy me-1" ></i>Copy',className:"dropdown-item",exportOptions:{columns:[3,4,5,6,7],format:{body:function(t,r,n){if(t.length<=0)return t;var s=$.parseHTML(t),e="";return $.each(s,function(l,a){a.classList!==void 0&&a.classList.contains("user-name")?e=e+a.lastChild.firstChild.textContent:a.innerText===void 0?e=e+a.textContent:e=e+a.innerText}),e}}}}]},{text:'<i class="ti ti-plus me-sm-1"></i> <span class="d-none d-sm-inline-block">Add New Record</span>',className:"create-new btn btn-primary waves-effect waves-light"}],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var r=t.data();return"Details of "+r.full_name}}),type:"column",renderer:function(t,r,n){var s=$.map(n,function(e,l){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}},initComplete:function(t,r){$(".card-header").after('<hr class="my-0">')}}),$("div.head-label").html('<h5 class="card-title mb-0">DataTable with Buttons</h5>'));var g=101;b.on("core.form.valid",function(){var t=$(".add-new-record .dt-full-name").val(),r=$(".add-new-record .dt-post").val(),n=$(".add-new-record .dt-email").val(),s=$(".add-new-record .dt-date").val(),e=$(".add-new-record .dt-salary").val();t!=""&&(i.row.add({id:g,full_name:t,post:r,email:n,start_date:s,salary:"$"+e,status:5}).draw(),g++,v.hide())}),$(".datatables-basic tbody").on("click",".delete-record",function(){i.row($(this).parents("tr")).remove().draw()}),u.length&&u.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:"full_name"},{data:"email"},{data:"city"},{data:"post"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{targets:-2,render:function(t,r,n,s){var e=n.status,l={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return typeof l[e]>"u"?t:'<span class="badge '+l[e].class+'">'+l[e].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,render:function(t,r,n,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon item-edit"><i class="ti ti-pencil ti-md"></i></a>'}}],dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end mt-n6 mt-md-0"f>><"table-responsive"t><"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],language:{paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}}});var o=2;if(c.length){var f=c.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"full_name"},{data:"post"},{data:"email"},{data:"city"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,targets:0,searchable:!1,render:function(t,r,n,s){return""}},{visible:!1,targets:o},{targets:-2,render:function(t,r,n,s){var e=n.status,l={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return typeof l[e]>"u"?t:'<span class="badge '+l[e].class+'">'+l[e].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(t,r,n,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon item-edit"><i class="ti ti-pencil ti-md"></i></a>'}}],order:[[o,"asc"]],dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end mt-n6 mt-md-0"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],language:{paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},drawCallback:function(t){var r=this.api(),n=r.rows({page:"current"}).nodes(),s=null;r.column(o,{page:"current"}).data().each(function(e,l){s!==e&&($(n).eq(l).before('<tr class="group"><td colspan="8">'+e+"</td></tr>"),s=e)})},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var r=t.data();return"Details of "+r.full_name}}),type:"column",renderer:function(t,r,n){var s=$.map(n,function(e,l){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}}});$(".dt-row-grouping tbody").on("click","tr.group",function(){var t=f.order()[0];t[0]===o&&t[1]==="asc"?f.order([o,"desc"]).draw():f.order([o,"asc"]).draw()})}var w="German";d.length&&d.DataTable({ajax:assetsPath+"json/table-datatable.json",columns:[{data:""},{data:"full_name"},{data:"post"},{data:"email"},{data:"start_date"},{data:"salary"},{data:"status"},{data:""}],columnDefs:[{className:"control",orderable:!1,targets:0,searchable:!1,render:function(t,r,n,s){return""}},{targets:-2,render:function(t,r,n,s){var e=n.status,l={1:{title:"Current",class:"bg-label-primary"},2:{title:"Professional",class:" bg-label-success"},3:{title:"Rejected",class:" bg-label-danger"},4:{title:"Resigned",class:" bg-label-warning"},5:{title:"Applied",class:" bg-label-info"}};return typeof l[e]>"u"?t:'<span class="badge '+l[e].class+'">'+l[e].title+"</span>"}},{targets:-1,title:"Actions",orderable:!1,searchable:!1,render:function(t,r,n,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div><a href="javascript:;" class="btn btn-sm btn-text-secondary rounded-pill btn-icon item-edit"><i class="ti ti-pencil ti-md"></i></a>'}}],language:{url:"//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/"+w+".json",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},order:[[2,"desc"]],displayLength:7,dom:'<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end mt-n6 mt-md-0"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',lengthMenu:[7,10,25,50,75,100],responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var r=t.data();return"Details of "+r.full_name}}),type:"column",renderer:function(t,r,n){var s=$.map(n,function(e,l){return e.title!==""?'<tr data-dt-row="'+e.rowIndex+'" data-dt-column="'+e.columnIndex+'"><td>'+e.title+":</td> <td>"+e.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}}}),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
