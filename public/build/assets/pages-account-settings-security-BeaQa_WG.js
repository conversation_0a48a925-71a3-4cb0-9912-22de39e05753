document.addEventListener("DOMContentLoaded",function(n){(function(){const e=document.querySelector("#formAccountSettings"),a=document.querySelector("#formAccountSettingsApiKey");e&&FormValidation.formValidation(e,{fields:{currentPassword:{validators:{notEmpty:{message:"Please current password"},stringLength:{min:8,message:"Password must be more than 8 characters"}}},newPassword:{validators:{notEmpty:{message:"Please enter new password"},stringLength:{min:8,message:"Password must be more than 8 characters"}}},confirmPassword:{validators:{notEmpty:{message:"Please confirm new password"},identical:{compare:function(){return e.querySelector('[name="newPassword"]').value},message:"The password and its confirm are not the same"},stringLength:{min:8,message:"Password must be more than 8 characters"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-6"}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus},init:s=>{s.on("plugins.message.placed",function(t){t.element.parentElement.classList.contains("input-group")&&t.element.parentElement.insertAdjacentElement("afterend",t.messageElement)})}}),a&&FormValidation.formValidation(a,{fields:{apiKey:{validators:{notEmpty:{message:"Please enter API key name"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:""}),submitButton:new FormValidation.plugins.SubmitButton,autoFocus:new FormValidation.plugins.AutoFocus},init:s=>{s.on("plugins.message.placed",function(t){t.element.parentElement.classList.contains("input-group")&&t.element.parentElement.insertAdjacentElement("afterend",t.messageElement)})}})})()});$(function(){var n=$(".select2");n.length&&n.each(function(){var e=$(this);e.wrap('<div class="position-relative"></div>'),e.select2({dropdownParent:e.parent()})})});
