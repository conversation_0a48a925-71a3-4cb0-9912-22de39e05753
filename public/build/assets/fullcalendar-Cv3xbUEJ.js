var vt,A,Wr,Vr,<PERSON>,ue,jn,Gr,qr,rt={},Qr=[],Hs=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function ee(t,e){for(var n in e)t[n]=e[n];return t}function Yr(t){var e=t.parentNode;e&&e.removeChild(t)}function f(t,e,n){var r,i,s,l={};for(s in e)s=="key"?r=e[s]:s=="ref"?i=e[s]:l[s]=e[s];if(arguments.length>2&&(l.children=arguments.length>3?vt.call(arguments,2):n),typeof t=="function"&&t.defaultProps!=null)for(s in t.defaultProps)l[s]===void 0&&(l[s]=t.defaultProps[s]);return Je(t,l,r,i,null)}function Je(t,e,n,r,i){var s={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++Wr};return i==null&&A.vnode!=null&&A.vnode(s),s}function L(){return{current:null}}function x(t){return t.children}function Ps(t,e,n,r,i){var s;for(s in n)s==="children"||s==="key"||s in e||it(t,s,null,n[s],r);for(s in e)i&&typeof e[s]!="function"||s==="children"||s==="key"||s==="value"||s==="checked"||n[s]===e[s]||it(t,s,e[s],n[s],r)}function Fn(t,e,n){e[0]==="-"?t.setProperty(e,n??""):t[e]=n==null?"":typeof n!="number"||Hs.test(e)?n:n+"px"}function it(t,e,n,r,i){var s;e:if(e==="style")if(typeof n=="string")t.style.cssText=n;else{if(typeof r=="string"&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||Fn(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||Fn(t.style,e,n[e])}else if(e[0]==="o"&&e[1]==="n")s=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in t?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+s]=n,n?r||t.addEventListener(e,s?Wn:zn,s):t.removeEventListener(e,s?Wn:zn,s);else if(e!=="dangerouslySetInnerHTML"){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e in t)try{t[e]=n??"";break e}catch{}typeof n=="function"||(n==null||n===!1&&e.indexOf("-")==-1?t.removeAttribute(e):t.setAttribute(e,n))}}function zn(t){De=!0;try{return this.l[t.type+!1](A.event?A.event(t):t)}finally{De=!1}}function Wn(t){De=!0;try{return this.l[t.type+!0](A.event?A.event(t):t)}finally{De=!1}}function j(t,e){this.props=t,this.context=e}function He(t,e){if(e==null)return t.__?He(t.__,t.__.__k.indexOf(t)+1):null;for(var n;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null)return n.__e;return typeof t.type=="function"?He(t):null}function Zr(t){var e,n;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((n=t.__k[e])!=null&&n.__e!=null){t.__e=t.__c.base=n.__e;break}return Zr(t)}}function Bs(t){De?setTimeout(t):Gr(t)}function Wt(t){(!t.__d&&(t.__d=!0)&&ue.push(t)&&!st.__r++||jn!==A.debounceRendering)&&((jn=A.debounceRendering)||Bs)(st)}function st(){var t,e,n,r,i,s,l,o;for(ue.sort(function(a,d){return a.__v.__b-d.__v.__b});t=ue.shift();)t.__d&&(e=ue.length,r=void 0,i=void 0,l=(s=(n=t).__v).__e,(o=n.__P)&&(r=[],(i=ee({},s)).__v=s.__v+1,on(o,s,i,n.__n,o.ownerSVGElement!==void 0,s.__h!=null?[l]:null,r,l??He(s),s.__h),ei(r,s),s.__e!=l&&Zr(s)),ue.length>e&&ue.sort(function(a,d){return a.__v.__b-d.__v.__b}));st.__r=0}function $r(t,e,n,r,i,s,l,o,a,d){var c,g,h,u,m,v,y,b=r&&r.__k||Qr,E=b.length;for(n.__k=[],c=0;c<e.length;c++)if((u=n.__k[c]=(u=e[c])==null||typeof u=="boolean"?null:typeof u=="string"||typeof u=="number"||typeof u=="bigint"?Je(null,u,null,null,u):Array.isArray(u)?Je(x,{children:u},null,null,null):u.__b>0?Je(u.type,u.props,u.key,u.ref?u.ref:null,u.__v):u)!=null){if(u.__=n,u.__b=n.__b+1,(h=b[c])===null||h&&u.key==h.key&&u.type===h.type)b[c]=void 0;else for(g=0;g<E;g++){if((h=b[g])&&u.key==h.key&&u.type===h.type){b[g]=void 0;break}h=null}on(t,u,h=h||rt,i,s,l,o,a,d),m=u.__e,(g=u.ref)&&h.ref!=g&&(y||(y=[]),h.ref&&y.push(h.ref,null,u),y.push(g,u.__c||m,u)),m!=null?(v==null&&(v=m),typeof u.type=="function"&&u.__k===h.__k?u.__d=a=Xr(u,a,t):a=Jr(t,u,h,b,m,a),typeof n.type=="function"&&(n.__d=a)):a&&h.__e==a&&a.parentNode!=t&&(a=He(h))}for(n.__e=v,c=E;c--;)b[c]!=null&&(typeof n.type=="function"&&b[c].__e!=null&&b[c].__e==n.__d&&(n.__d=Kr(r).nextSibling),ni(b[c],b[c]));if(y)for(c=0;c<y.length;c++)ti(y[c],y[++c],y[++c])}function Xr(t,e,n){for(var r,i=t.__k,s=0;i&&s<i.length;s++)(r=i[s])&&(r.__=t,e=typeof r.type=="function"?Xr(r,e,n):Jr(n,r,r,i,r.__e,e));return e}function lt(t,e){return e=e||[],t==null||typeof t=="boolean"||(Array.isArray(t)?t.some(function(n){lt(n,e)}):e.push(t)),e}function Jr(t,e,n,r,i,s){var l,o,a;if(e.__d!==void 0)l=e.__d,e.__d=void 0;else if(n==null||i!=s||i.parentNode==null)e:if(s==null||s.parentNode!==t)t.appendChild(i),l=null;else{for(o=s,a=0;(o=o.nextSibling)&&a<r.length;a+=1)if(o==i)break e;t.insertBefore(i,s),l=s}return l!==void 0?l:i.nextSibling}function Kr(t){var e,n,r;if(t.type==null||typeof t.type=="string")return t.__e;if(t.__k){for(e=t.__k.length-1;e>=0;e--)if((n=t.__k[e])&&(r=Kr(n)))return r}return null}function on(t,e,n,r,i,s,l,o,a){var d,c,g,h,u,m,v,y,b,E,D,w,O,T,k,R=e.type;if(e.constructor!==void 0)return null;n.__h!=null&&(a=n.__h,o=e.__e=n.__e,e.__h=null,s=[o]),(d=A.__b)&&d(e);try{e:if(typeof R=="function"){if(y=e.props,b=(d=R.contextType)&&r[d.__c],E=d?b?b.props.value:d.__:r,n.__c?v=(c=e.__c=n.__c).__=c.__E:("prototype"in R&&R.prototype.render?e.__c=c=new R(y,E):(e.__c=c=new j(y,E),c.constructor=R,c.render=Us),b&&b.sub(c),c.props=y,c.state||(c.state={}),c.context=E,c.__n=r,g=c.__d=!0,c.__h=[],c._sb=[]),c.__s==null&&(c.__s=c.state),R.getDerivedStateFromProps!=null&&(c.__s==c.state&&(c.__s=ee({},c.__s)),ee(c.__s,R.getDerivedStateFromProps(y,c.__s))),h=c.props,u=c.state,c.__v=e,g)R.getDerivedStateFromProps==null&&c.componentWillMount!=null&&c.componentWillMount(),c.componentDidMount!=null&&c.__h.push(c.componentDidMount);else{if(R.getDerivedStateFromProps==null&&y!==h&&c.componentWillReceiveProps!=null&&c.componentWillReceiveProps(y,E),!c.__e&&c.shouldComponentUpdate!=null&&c.shouldComponentUpdate(y,c.__s,E)===!1||e.__v===n.__v){for(e.__v!==n.__v&&(c.props=y,c.state=c.__s,c.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.forEach(function(K){K&&(K.__=e)}),D=0;D<c._sb.length;D++)c.__h.push(c._sb[D]);c._sb=[],c.__h.length&&l.push(c);break e}c.componentWillUpdate!=null&&c.componentWillUpdate(y,c.__s,E),c.componentDidUpdate!=null&&c.__h.push(function(){c.componentDidUpdate(h,u,m)})}if(c.context=E,c.props=y,c.__P=t,w=A.__r,O=0,"prototype"in R&&R.prototype.render){for(c.state=c.__s,c.__d=!1,w&&w(e),d=c.render(c.props,c.state,c.context),T=0;T<c._sb.length;T++)c.__h.push(c._sb[T]);c._sb=[]}else do c.__d=!1,w&&w(e),d=c.render(c.props,c.state,c.context),c.state=c.__s;while(c.__d&&++O<25);c.state=c.__s,c.getChildContext!=null&&(r=ee(ee({},r),c.getChildContext())),g||c.getSnapshotBeforeUpdate==null||(m=c.getSnapshotBeforeUpdate(h,u)),k=d!=null&&d.type===x&&d.key==null?d.props.children:d,$r(t,Array.isArray(k)?k:[k],e,n,r,i,s,l,o,a),c.base=e.__e,e.__h=null,c.__h.length&&l.push(c),v&&(c.__E=c.__=null),c.__e=!1}else s==null&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):e.__e=Ls(n.__e,e,n,r,i,s,l,a);(d=A.diffed)&&d(e)}catch(K){e.__v=null,(a||s!=null)&&(e.__e=o,e.__h=!!a,s[s.indexOf(o)]=null),A.__e(K,e,n)}}function ei(t,e){A.__c&&A.__c(e,t),t.some(function(n){try{t=n.__h,n.__h=[],t.some(function(r){r.call(n)})}catch(r){A.__e(r,n.__v)}})}function Ls(t,e,n,r,i,s,l,o){var a,d,c,g=n.props,h=e.props,u=e.type,m=0;if(u==="svg"&&(i=!0),s!=null){for(;m<s.length;m++)if((a=s[m])&&"setAttribute"in a==!!u&&(u?a.localName===u:a.nodeType===3)){t=a,s[m]=null;break}}if(t==null){if(u===null)return document.createTextNode(h);t=i?document.createElementNS("http://www.w3.org/2000/svg",u):document.createElement(u,h.is&&h),s=null,o=!1}if(u===null)g===h||o&&t.data===h||(t.data=h);else{if(s=s&&vt.call(t.childNodes),d=(g=n.props||rt).dangerouslySetInnerHTML,c=h.dangerouslySetInnerHTML,!o){if(s!=null)for(g={},m=0;m<t.attributes.length;m++)g[t.attributes[m].name]=t.attributes[m].value;(c||d)&&(c&&(d&&c.__html==d.__html||c.__html===t.innerHTML)||(t.innerHTML=c&&c.__html||""))}if(Ps(t,h,g,i,o),c)e.__k=[];else if(m=e.props.children,$r(t,Array.isArray(m)?m:[m],e,n,r,i&&u!=="foreignObject",s,l,s?s[0]:n.__k&&He(n,0),o),s!=null)for(m=s.length;m--;)s[m]!=null&&Yr(s[m]);o||("value"in h&&(m=h.value)!==void 0&&(m!==t.value||u==="progress"&&!m||u==="option"&&m!==g.value)&&it(t,"value",m,g.value,!1),"checked"in h&&(m=h.checked)!==void 0&&m!==t.checked&&it(t,"checked",m,g.checked,!1))}return t}function ti(t,e,n){try{typeof t=="function"?t(e):t.current=e}catch(r){A.__e(r,n)}}function ni(t,e,n){var r,i;if(A.unmount&&A.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||ti(r,null,e)),(r=t.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(s){A.__e(s,e)}r.base=r.__P=null,t.__c=void 0}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&ni(r[i],e,n||typeof t.type!="function");n||t.__e==null||Yr(t.__e),t.__=t.__e=t.__d=void 0}function Us(t,e,n){return this.constructor(t,n)}function Pe(t,e,n){var r,i,s;A.__&&A.__(t,e),i=(r=typeof n=="function")?null:e.__k,s=[],on(e,t=(!r&&n||e).__k=f(x,null,[t]),i||rt,rt,e.ownerSVGElement!==void 0,!r&&n?[n]:i?null:e.firstChild?vt.call(e.childNodes):null,s,!r&&n?n:i?i.__e:e.firstChild,r),ei(s,t)}function js(t,e){var n={__c:e="__cC"+qr++,__:t,Consumer:function(r,i){return r.children(i)},Provider:function(r){var i,s;return this.getChildContext||(i=[],(s={})[e]=this,this.getChildContext=function(){return s},this.shouldComponentUpdate=function(l){this.props.value!==l.value&&i.some(function(o){o.__e=!0,Wt(o)})},this.sub=function(l){i.push(l);var o=l.componentWillUnmount;l.componentWillUnmount=function(){i.splice(i.indexOf(l),1),o&&o.call(l)}}),r.children}};return n.Provider.__=n.Consumer.contextType=n}vt=Qr.slice,A={__e:function(t,e,n,r){for(var i,s,l;e=e.__;)if((i=e.__c)&&!i.__)try{if((s=i.constructor)&&s.getDerivedStateFromError!=null&&(i.setState(s.getDerivedStateFromError(t)),l=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(t,r||{}),l=i.__d),l)return i.__E=i}catch(o){t=o}throw t}},Wr=0,Vr=function(t){return t!=null&&t.constructor===void 0},De=!1,j.prototype.setState=function(t,e){var n;n=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=ee({},this.state),typeof t=="function"&&(t=t(ee({},n),this.props)),t&&ee(n,t),t!=null&&this.__v&&(e&&this._sb.push(e),Wt(this))},j.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Wt(this))},j.prototype.render=x,ue=[],Gr=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,st.__r=0,qr=0;var V,_t,Vn,ri=[],Tt=[],Gn=A.__b,qn=A.__r,Qn=A.diffed,Yn=A.__c,Zn=A.unmount;function Fs(){for(var t;t=ri.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Ke),t.__H.__h.forEach(Vt),t.__H.__h=[]}catch(e){t.__H.__h=[],A.__e(e,t.__v)}}A.__b=function(t){V=null,Gn&&Gn(t)},A.__r=function(t){qn&&qn(t);var e=(V=t.__c).__H;e&&(_t===V?(e.__h=[],V.__h=[],e.__.forEach(function(n){n.__N&&(n.__=n.__N),n.__V=Tt,n.__N=n.i=void 0})):(e.__h.forEach(Ke),e.__h.forEach(Vt),e.__h=[])),_t=V},A.diffed=function(t){Qn&&Qn(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(ri.push(e)!==1&&Vn===A.requestAnimationFrame||((Vn=A.requestAnimationFrame)||zs)(Fs)),e.__H.__.forEach(function(n){n.i&&(n.__H=n.i),n.__V!==Tt&&(n.__=n.__V),n.i=void 0,n.__V=Tt})),_t=V=null},A.__c=function(t,e){e.some(function(n){try{n.__h.forEach(Ke),n.__h=n.__h.filter(function(r){return!r.__||Vt(r)})}catch(r){e.some(function(i){i.__h&&(i.__h=[])}),e=[],A.__e(r,n.__v)}}),Yn&&Yn(t,e)},A.unmount=function(t){Zn&&Zn(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{Ke(r)}catch(i){e=i}}),n.__H=void 0,e&&A.__e(e,n.__v))};var $n=typeof requestAnimationFrame=="function";function zs(t){var e,n=function(){clearTimeout(r),$n&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);$n&&(e=requestAnimationFrame(n))}function Ke(t){var e=V,n=t.__c;typeof n=="function"&&(t.__c=void 0,n()),V=e}function Vt(t){var e=V;t.__c=t.__(),V=e}function Ws(t,e){for(var n in e)t[n]=e[n];return t}function Xn(t,e){for(var n in t)if(n!=="__source"&&!(n in e))return!0;for(var r in e)if(r!=="__source"&&t[r]!==e[r])return!0;return!1}function Jn(t){this.props=t}(Jn.prototype=new j).isPureReactComponent=!0,Jn.prototype.shouldComponentUpdate=function(t,e){return Xn(this.props,t)||Xn(this.state,e)};var Kn=A.__b;A.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),Kn&&Kn(t)};var Vs=A.__e;A.__e=function(t,e,n,r){if(t.then){for(var i,s=e;s=s.__;)if((i=s.__c)&&i.__c)return e.__e==null&&(e.__e=n.__e,e.__k=n.__k),i.__c(t,e)}Vs(t,e,n,r)};var er=A.unmount;function ii(t,e,n){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(r){typeof r.__c=="function"&&r.__c()}),t.__c.__H=null),(t=Ws({},t)).__c!=null&&(t.__c.__P===n&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map(function(r){return ii(r,e,n)})),t}function si(t,e,n){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(r){return si(r,e,n)}),t.__c&&t.__c.__P===e&&(t.__e&&n.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=n)),t}function xt(){this.__u=0,this.t=null,this.__b=null}function li(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Ve(){this.u=null,this.o=null}A.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&t.__h===!0&&(t.type=null),er&&er(t)},(xt.prototype=new j).__c=function(t,e){var n=e.__c,r=this;r.t==null&&(r.t=[]),r.t.push(n);var i=li(r.__v),s=!1,l=function(){s||(s=!0,n.__R=null,i?i(o):o())};n.__R=l;var o=function(){if(!--r.__u){if(r.state.__a){var d=r.state.__a;r.__v.__k[0]=si(d,d.__c.__P,d.__c.__O)}var c;for(r.setState({__a:r.__b=null});c=r.t.pop();)c.forceUpdate()}},a=e.__h===!0;r.__u++||a||r.setState({__a:r.__b=r.__v.__k[0]}),t.then(l,l)},xt.prototype.componentWillUnmount=function(){this.t=[]},xt.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=ii(this.__b,n,r.__O=r.__P)}this.__b=null}var i=e.__a&&f(x,null,t.fallback);return i&&(i.__h=null),[f(x,null,e.__a?null:t.children),i]};var tr=function(t,e,n){if(++n[1]===n[0]&&t.o.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.o.size))for(n=t.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.u=n=n[2]}};function Gs(t){return this.getChildContext=function(){return t.context},t.children}function qs(t){var e=this,n=t.i;e.componentWillUnmount=function(){Pe(null,e.l),e.l=null,e.i=null},e.i&&e.i!==n&&e.componentWillUnmount(),t.__v?(e.l||(e.i=n,e.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(r){this.childNodes.push(r),e.i.appendChild(r)},insertBefore:function(r,i){this.childNodes.push(r),e.i.appendChild(r)},removeChild:function(r){this.childNodes.splice(this.childNodes.indexOf(r)>>>1,1),e.i.removeChild(r)}}),Pe(f(Gs,{context:e.context},t.__v),e.l)):e.l&&e.componentWillUnmount()}function Qs(t,e){var n=f(qs,{__v:t,i:e});return n.containerInfo=e,n}(Ve.prototype=new j).__a=function(t){var e=this,n=li(e.__v),r=e.o.get(t);return r[0]++,function(i){var s=function(){e.props.revealOrder?(r.push(i),tr(e,t,r)):i()};n?n(s):s()}},Ve.prototype.render=function(t){this.u=null,this.o=new Map;var e=lt(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var n=e.length;n--;)this.o.set(e[n],this.u=[1,0,this.u]);return t.children},Ve.prototype.componentDidUpdate=Ve.prototype.componentDidMount=function(){var t=this;this.o.forEach(function(e,n){tr(t,n,e)})};var Ys=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Zs=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,$s=typeof document<"u",Xs=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(t)};j.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(j.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var nr=A.event;function Js(){}function Ks(){return this.cancelBubble}function el(){return this.defaultPrevented}A.event=function(t){return nr&&(t=nr(t)),t.persist=Js,t.isPropagationStopped=Ks,t.isDefaultPrevented=el,t.nativeEvent=t};var rr={configurable:!0,get:function(){return this.class}},ir=A.vnode;A.vnode=function(t){var e=t.type,n=t.props,r=n;if(typeof e=="string"){var i=e.indexOf("-")===-1;for(var s in r={},n){var l=n[s];$s&&s==="children"&&e==="noscript"||s==="value"&&"defaultValue"in n&&l==null||(s==="defaultValue"&&"value"in n&&n.value==null?s="value":s==="download"&&l===!0?l="":/ondoubleclick/i.test(s)?s="ondblclick":/^onchange(textarea|input)/i.test(s+e)&&!Xs(n.type)?s="oninput":/^onfocus$/i.test(s)?s="onfocusin":/^onblur$/i.test(s)?s="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(s)?s=s.toLowerCase():i&&Zs.test(s)?s=s.replace(/[A-Z0-9]/g,"-$&").toLowerCase():l===null&&(l=void 0),/^oninput$/i.test(s)&&(s=s.toLowerCase(),r[s]&&(s="oninputCapture")),r[s]=l)}e=="select"&&r.multiple&&Array.isArray(r.value)&&(r.value=lt(n.children).forEach(function(o){o.props.selected=r.value.indexOf(o.props.value)!=-1})),e=="select"&&r.defaultValue!=null&&(r.value=lt(n.children).forEach(function(o){o.props.selected=r.multiple?r.defaultValue.indexOf(o.props.value)!=-1:r.defaultValue==o.props.value})),t.props=r,n.class!=n.className&&(rr.enumerable="className"in n,n.className!=null&&(r.class=n.className),Object.defineProperty(r,"className",rr))}t.$$typeof=Ys,ir&&ir(t)};var sr=A.__r;A.__r=function(t){sr&&sr(t),t.__c};const oi=[],Gt=new Map;function yt(t){oi.push(t),Gt.forEach(e=>{ci(e,t)})}function tl(t){t.isConnected&&t.getRootNode&&ai(t.getRootNode())}function ai(t){let e=Gt.get(t);if(!e||!e.isConnected){if(e=t.querySelector("style[data-fullcalendar]"),!e){e=document.createElement("style"),e.setAttribute("data-fullcalendar","");const n=rl();n&&(e.nonce=n);const r=t===document?document.head:t,i=t===document?r.querySelector("script,link[rel=stylesheet],link[as=style],style"):r.firstChild;r.insertBefore(e,i)}Gt.set(t,e),nl(e)}}function nl(t){for(const e of oi)ci(t,e)}function ci(t,e){const{sheet:n}=t,r=n.cssRules.length;e.split("}").forEach((i,s)=>{i=i.trim(),i&&n.insertRule(i+"}",r+s)})}let It;function rl(){return It===void 0&&(It=il()),It}function il(){const t=document.querySelector('meta[name="csp-nonce"]');if(t&&t.hasAttribute("content"))return t.getAttribute("content");const e=document.querySelector("script[nonce]");return e&&e.nonce||""}typeof document<"u"&&ai(document);var sl=':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}';yt(sl);class an{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),e==null?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:n}=this;n[e]=(n[e]||0)+1,this.clearTimeout()}resume(e="",n){let{pauseDepths:r}=this;e in r&&(n?delete r[e]:(r[e]-=1,r[e]<=0&&delete r[e]),this.tryDrain())}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}function cn(t){t.parentNode&&t.parentNode.removeChild(t)}function P(t,e){if(t.closest)return t.closest(e);if(!document.documentElement.contains(t))return null;do{if(ll(t,e))return t;t=t.parentElement||t.parentNode}while(t!==null&&t.nodeType===1);return null}function ll(t,e){return(t.matches||t.matchesSelector||t.msMatchesSelector).call(t,e)}function ol(t,e){let n=t instanceof HTMLElement?[t]:t,r=[];for(let i=0;i<n.length;i+=1){let s=n[i].querySelectorAll(e);for(let l=0;l<s.length;l+=1)r.push(s[l])}return r}const al=/(top|left|right|bottom|width|height)$/i;function Me(t,e){for(let n in e)di(t,n,e[n])}function di(t,e,n){n==null?t.style[e]="":typeof n=="number"&&al.test(e)?t.style[e]=`${n}px`:t.style[e]=n}function ui(t){var e,n;return(n=(e=t.composedPath)===null||e===void 0?void 0:e.call(t)[0])!==null&&n!==void 0?n:t.target}let lr=0;function re(){return lr+=1,"fc-dom-"+lr}function bt(t){t.preventDefault()}function cl(t,e){return n=>{let r=P(n.target,t);r&&e.call(r,n,r)}}function fi(t,e,n,r){let i=cl(n,r);return t.addEventListener(e,i),()=>{t.removeEventListener(e,i)}}function dl(t,e,n,r){let i;return fi(t,"mouseover",e,(s,l)=>{if(l!==i){i=l,n(s,l);let o=a=>{i=null,r(a,l),l.removeEventListener("mouseleave",o)};l.addEventListener("mouseleave",o)}})}const or=["webkitTransitionEnd","otransitionend","oTransitionEnd","msTransitionEnd","transitionend"];function ul(t,e){let n=r=>{e(r),or.forEach(i=>{t.removeEventListener(i,n)})};or.forEach(r=>{t.addEventListener(r,n)})}function hi(t){return Object.assign({onClick:t},gi(t))}function gi(t){return{tabIndex:0,onKeyDown(e){(e.key==="Enter"||e.key===" ")&&(t(e),e.preventDefault())}}}let ar=0;function me(){return ar+=1,String(ar)}function dn(){document.body.classList.add("fc-not-allowed")}function un(){document.body.classList.remove("fc-not-allowed")}function fl(t){t.style.userSelect="none",t.style.webkitUserSelect="none",t.addEventListener("selectstart",bt)}function hl(t){t.style.userSelect="",t.style.webkitUserSelect="",t.removeEventListener("selectstart",bt)}function gl(t){t.addEventListener("contextmenu",bt)}function pl(t){t.removeEventListener("contextmenu",bt)}function ml(t){let e=[],n=[],r,i;for(typeof t=="string"?n=t.split(/\s*,\s*/):typeof t=="function"?n=[t]:Array.isArray(t)&&(n=t),r=0;r<n.length;r+=1)i=n[r],typeof i=="string"?e.push(i.charAt(0)==="-"?{field:i.substring(1),order:-1}:{field:i,order:1}):typeof i=="function"&&e.push({func:i});return e}function vl(t,e,n){let r,i;for(r=0;r<n.length;r+=1)if(i=yl(t,e,n[r]),i)return i;return 0}function yl(t,e,n){return n.func?n.func(t,e):bl(t[n.field],e[n.field])*(n.order||1)}function bl(t,e){return!t&&!e?0:e==null?-1:t==null?1:typeof t=="string"||typeof e=="string"?String(t).localeCompare(String(e)):t-e}function Ee(t,e){let n=String(t);return"000".substr(0,e-n.length)+n}function ke(t,e,n){return typeof t=="function"?t(...e):typeof t=="string"?e.reduce((r,i,s)=>r.replace("$"+s,i||""),t):n}function El(t,e){return t-e}function et(t){return t%1===0}function Sl(t){let e=t.querySelector(".fc-scrollgrid-shrink-frame"),n=t.querySelector(".fc-scrollgrid-shrink-cushion");if(!e)throw new Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw new Error("needs fc-scrollgrid-shrink-cushion className");return t.getBoundingClientRect().width-e.getBoundingClientRect().width+n.getBoundingClientRect().width}const cr=["years","months","days","milliseconds"],Al=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function C(t,e){return typeof t=="string"?Dl(t):typeof t=="object"&&t?dr(t):typeof t=="number"?dr({[e||"milliseconds"]:t}):null}function Dl(t){let e=Al.exec(t);if(e){let n=e[1]?-1:1;return{years:0,months:0,days:n*(e[2]?parseInt(e[2],10):0),milliseconds:n*((e[3]?parseInt(e[3],10):0)*60*60*1e3+(e[4]?parseInt(e[4],10):0)*60*1e3+(e[5]?parseInt(e[5],10):0)*1e3+(e[6]?parseInt(e[6],10):0))}}return null}function dr(t){let e={years:t.years||t.year||0,months:t.months||t.month||0,days:t.days||t.day||0,milliseconds:(t.hours||t.hour||0)*60*60*1e3+(t.minutes||t.minute||0)*60*1e3+(t.seconds||t.second||0)*1e3+(t.milliseconds||t.millisecond||t.ms||0)},n=t.weeks||t.week;return n&&(e.days+=n*7,e.specifiedWeeks=!0),e}function wl(t,e){return t.years===e.years&&t.months===e.months&&t.days===e.days&&t.milliseconds===e.milliseconds}function qt(t,e){return{years:t.years+e.years,months:t.months+e.months,days:t.days+e.days,milliseconds:t.milliseconds+e.milliseconds}}function Cl(t,e){return{years:t.years-e.years,months:t.months-e.months,days:t.days-e.days,milliseconds:t.milliseconds-e.milliseconds}}function Rl(t,e){return{years:t.years*e,months:t.months*e,days:t.days*e,milliseconds:t.milliseconds*e}}function _l(t){return Se(t)/365}function Tl(t){return Se(t)/30}function Se(t){return z(t)/864e5}function z(t){return t.years*(365*864e5)+t.months*(30*864e5)+t.days*864e5+t.milliseconds}function fn(t,e){let n=null;for(let r=0;r<cr.length;r+=1){let i=cr[r];if(e[i]){let s=t[i]/e[i];if(!et(s)||n!==null&&n!==s)return null;n=s}else if(t[i])return null}return n}function Qt(t){let e=t.milliseconds;if(e){if(e%1e3!==0)return{unit:"millisecond",value:e};if(e%(1e3*60)!==0)return{unit:"second",value:e/1e3};if(e%(1e3*60*60)!==0)return{unit:"minute",value:e/(1e3*60)};if(e)return{unit:"hour",value:e/(1e3*60*60)}}return t.days?t.specifiedWeeks&&t.days%7===0?{unit:"week",value:t.days/7}:{unit:"day",value:t.days}:t.months?{unit:"month",value:t.months}:t.years?{unit:"year",value:t.years}:{unit:"millisecond",value:0}}function ie(t,e,n){if(t===e)return!0;let r=t.length,i;if(r!==e.length)return!1;for(i=0;i<r;i+=1)if(!(n?n(t[i],e[i]):t[i]===e[i]))return!1;return!0}const xl=["sun","mon","tue","wed","thu","fri","sat"];function ur(t,e){let n=te(t);return n[2]+=e*7,U(n)}function H(t,e){let n=te(t);return n[2]+=e,U(n)}function se(t,e){let n=te(t);return n[6]+=e,U(n)}function Il(t,e){return oe(t,e)/7}function oe(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60*24)}function Ml(t,e){return(e.valueOf()-t.valueOf())/(1e3*60*60)}function kl(t,e){return(e.valueOf()-t.valueOf())/(1e3*60)}function Nl(t,e){return(e.valueOf()-t.valueOf())/1e3}function Ol(t,e){let n=I(t),r=I(e);return{years:0,months:0,days:Math.round(oe(n,r)),milliseconds:e.valueOf()-r.valueOf()-(t.valueOf()-n.valueOf())}}function Hl(t,e){let n=ot(t,e);return n!==null&&n%7===0?n/7:null}function ot(t,e){return ne(t)===ne(e)?Math.round(oe(t,e)):null}function I(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()])}function Pl(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours()])}function Bl(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes()])}function Ll(t){return U([t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds()])}function Ul(t,e,n){let r=t.getUTCFullYear(),i=Mt(t,r,e,n);if(i<1)return Mt(t,r-1,e,n);let s=Mt(t,r+1,e,n);return s>=1?Math.min(i,s):i}function Mt(t,e,n,r){let i=U([e,0,1+jl(e,n,r)]),s=I(t),l=Math.round(oe(i,s));return Math.floor(l/7)+1}function jl(t,e,n){let r=7+e-n;return-((7+U([t,0,r]).getUTCDay()-e)%7)+r-1}function fr(t){return[t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()]}function hr(t){return new Date(t[0],t[1]||0,t[2]==null?1:t[2],t[3]||0,t[4]||0,t[5]||0)}function te(t){return[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate(),t.getUTCHours(),t.getUTCMinutes(),t.getUTCSeconds(),t.getUTCMilliseconds()]}function U(t){return t.length===1&&(t=t.concat([0])),new Date(Date.UTC(...t))}function pi(t){return!isNaN(t.valueOf())}function ne(t){return t.getUTCHours()*1e3*60*60+t.getUTCMinutes()*1e3*60+t.getUTCSeconds()*1e3+t.getUTCMilliseconds()}function mi(t,e,n=!1){let r=t.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(e==null?r=r.replace("Z",""):e!==0&&(r=r.replace("Z",hn(e,!0)))),r}function Ue(t){return t.toISOString().replace(/T.*$/,"")}function Fl(t){return t.toISOString().match(/^\d{4}-\d{2}/)[0]}function zl(t){return Ee(t.getUTCHours(),2)+":"+Ee(t.getUTCMinutes(),2)+":"+Ee(t.getUTCSeconds(),2)}function hn(t,e=!1){let n=t<0?"-":"+",r=Math.abs(t),i=Math.floor(r/60),s=Math.round(r%60);return e?`${n+Ee(i,2)}:${Ee(s,2)}`:`GMT${n}${i}${s?`:${Ee(s,2)}`:""}`}function S(t,e,n){let r,i;return function(...s){if(!r)i=t.apply(this,s);else if(!ie(r,s)){let l=t.apply(this,s);(!e||!e(l,i))&&(i=l)}return r=s,i}}function tt(t,e,n){let r,i;return s=>(r?G(r,s)||(i=t.call(this,s)):i=t.call(this,s),r=s,i)}const gr={week:3,separator:0,omitZeroMinute:0,meridiem:0,omitCommas:0},at={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},Ge=/\s*([ap])\.?m\.?/i,Wl=/,/g,Vl=/\s+/g,Gl=/\u200e/g,ql=/UTC|GMT/;class Ql{constructor(e){let n={},r={},i=0;for(let s in e)s in gr?(r[s]=e[s],i=Math.max(gr[s],i)):(n[s]=e[s],s in at&&(i=Math.max(at[s],i)));this.standardDateProps=n,this.extendedSettings=r,this.severity=i,this.buildFormattingFunc=S(pr)}format(e,n){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,n)(e)}formatRange(e,n,r,i){let{standardDateProps:s,extendedSettings:l}=this,o=Kl(e.marker,n.marker,r.calendarSystem);if(!o)return this.format(e,r);let a=o;a>1&&(s.year==="numeric"||s.year==="2-digit")&&(s.month==="numeric"||s.month==="2-digit")&&(s.day==="numeric"||s.day==="2-digit")&&(a=1);let d=this.format(e,r),c=this.format(n,r);if(d===c)return d;let g=eo(s,a),h=pr(g,l,r),u=h(e),m=h(n),v=to(d,u,c,m),y=l.separator||i||r.defaultSeparator||"";return v?v.before+u+y+m+v.after:d+y+c}getLargestUnit(){switch(this.severity){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function pr(t,e,n){let r=Object.keys(t).length;return r===1&&t.timeZoneName==="short"?i=>hn(i.timeZoneOffset):r===0&&e.week?i=>Jl(n.computeWeekNumber(i.marker),n.weekText,n.weekTextLong,n.locale,e.week):Yl(t,e,n)}function Yl(t,e,n){t=Object.assign({},t),e=Object.assign({},e),Zl(t,e),t.timeZone="UTC";let r=new Intl.DateTimeFormat(n.locale.codes,t),i;if(e.omitZeroMinute){let s=Object.assign({},t);delete s.minute,i=new Intl.DateTimeFormat(n.locale.codes,s)}return s=>{let{marker:l}=s,o;i&&!l.getUTCMinutes()?o=i:o=r;let a=o.format(l);return $l(a,s,t,e,n)}}function Zl(t,e){t.timeZoneName&&(t.hour||(t.hour="2-digit"),t.minute||(t.minute="2-digit")),t.timeZoneName==="long"&&(t.timeZoneName="short"),e.omitZeroMinute&&(t.second||t.millisecond)&&delete e.omitZeroMinute}function $l(t,e,n,r,i){return t=t.replace(Gl,""),n.timeZoneName==="short"&&(t=Xl(t,i.timeZone==="UTC"||e.timeZoneOffset==null?"UTC":hn(e.timeZoneOffset))),r.omitCommas&&(t=t.replace(Wl,"").trim()),r.omitZeroMinute&&(t=t.replace(":00","")),r.meridiem===!1?t=t.replace(Ge,"").trim():r.meridiem==="narrow"?t=t.replace(Ge,(s,l)=>l.toLocaleLowerCase()):r.meridiem==="short"?t=t.replace(Ge,(s,l)=>`${l.toLocaleLowerCase()}m`):r.meridiem==="lowercase"&&(t=t.replace(Ge,s=>s.toLocaleLowerCase())),t=t.replace(Vl," "),t=t.trim(),t}function Xl(t,e){let n=!1;return t=t.replace(ql,()=>(n=!0,e)),n||(t+=` ${e}`),t}function Jl(t,e,n,r,i){let s=[];return i==="long"?s.push(n):(i==="short"||i==="narrow")&&s.push(e),(i==="long"||i==="short")&&s.push(" "),s.push(r.simpleNumberFormat.format(t)),r.options.direction==="rtl"&&s.reverse(),s.join("")}function Kl(t,e,n){return n.getMarkerYear(t)!==n.getMarkerYear(e)?5:n.getMarkerMonth(t)!==n.getMarkerMonth(e)?4:n.getMarkerDay(t)!==n.getMarkerDay(e)?2:ne(t)!==ne(e)?1:0}function eo(t,e){let n={};for(let r in t)(!(r in at)||at[r]<=e)&&(n[r]=t[r]);return n}function to(t,e,n,r){let i=0;for(;i<t.length;){let s=t.indexOf(e,i);if(s===-1)break;let l=t.substr(0,s);i=s+e.length;let o=t.substr(i),a=0;for(;a<n.length;){let d=n.indexOf(r,a);if(d===-1)break;let c=n.substr(0,d);a=d+r.length;let g=n.substr(a);if(l===c&&o===g)return{before:l,after:o}}}return null}function mr(t,e){let n=e.markerToArray(t.marker);return{marker:t.marker,timeZoneOffset:t.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function ct(t,e,n,r){let i=mr(t,n.calendarSystem),s=e?mr(e,n.calendarSystem):null;return{date:i,start:i,end:s,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}class no{constructor(e){this.cmdStr=e}format(e,n,r){return n.cmdFormatter(this.cmdStr,ct(e,null,n,r))}formatRange(e,n,r,i){return r.cmdFormatter(this.cmdStr,ct(e,n,r,i))}}class ro{constructor(e){this.func=e}format(e,n,r){return this.func(ct(e,null,n,r))}formatRange(e,n,r,i){return this.func(ct(e,n,r,i))}}function M(t){return typeof t=="object"&&t?new Ql(t):typeof t=="string"?new no(t):typeof t=="function"?new ro(t):null}const vr={navLinkDayClick:p,navLinkWeekClick:p,duration:C,bootstrapFontAwesome:p,buttonIcons:p,customButtons:p,defaultAllDayEventDuration:C,defaultTimedEventDuration:C,nextDayThreshold:C,scrollTime:C,scrollTimeReset:Boolean,slotMinTime:C,slotMaxTime:C,dayPopoverFormat:M,slotDuration:C,snapDuration:C,headerToolbar:p,footerToolbar:p,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:M,dayHeaderClassNames:p,dayHeaderContent:p,dayHeaderDidMount:p,dayHeaderWillUnmount:p,dayCellClassNames:p,dayCellContent:p,dayCellDidMount:p,dayCellWillUnmount:p,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:p,weekNumbers:Boolean,weekNumberClassNames:p,weekNumberContent:p,weekNumberDidMount:p,weekNumberWillUnmount:p,editable:Boolean,viewClassNames:p,viewDidMount:p,viewWillUnmount:p,nowIndicator:Boolean,nowIndicatorClassNames:p,nowIndicatorContent:p,nowIndicatorDidMount:p,nowIndicatorWillUnmount:p,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:p,locale:p,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:p,eventOrder:ml,eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:p,contentHeight:p,direction:String,weekNumberFormat:M,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:p,initialDate:p,now:p,eventDataTransform:p,stickyHeaderDates:p,stickyFooterScrollbar:p,viewHeight:p,defaultAllDay:Boolean,eventSourceFailure:p,eventSourceSuccess:p,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:p,eventConstraint:p,eventAllow:p,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:p,eventContent:p,eventDidMount:p,eventWillUnmount:p,selectConstraint:p,selectOverlap:p,selectAllow:p,droppable:Boolean,unselectCancel:String,slotLabelFormat:p,slotLaneClassNames:p,slotLaneContent:p,slotLaneDidMount:p,slotLaneWillUnmount:p,slotLabelClassNames:p,slotLabelContent:p,slotLabelDidMount:p,slotLabelWillUnmount:p,dayMaxEvents:p,dayMaxEventRows:p,dayMinWidth:Number,slotLabelInterval:C,allDayText:String,allDayClassNames:p,allDayContent:p,allDayDidMount:p,allDayWillUnmount:p,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:M,rerenderDelay:Number,moreLinkText:p,moreLinkHint:p,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:p,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:C,hiddenDays:p,fixedWeekCount:Boolean,validRange:p,visibleRange:p,titleFormat:p,eventInteractive:Boolean,noEventsText:String,viewHint:p,navLinkHint:p,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:p,moreLinkClassNames:p,moreLinkContent:p,moreLinkDidMount:p,moreLinkWillUnmount:p,monthStartFormat:M,handleCustomRendering:p,customRenderingMetaMap:p,customRenderingReplaces:Boolean},Ne={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},yr={datesSet:p,eventsSet:p,eventAdd:p,eventChange:p,eventRemove:p,windowResize:p,eventClick:p,eventMouseEnter:p,eventMouseLeave:p,select:p,unselect:p,loading:p,_unmount:p,_beforeprint:p,_afterprint:p,_noEventDrop:p,_noEventResize:p,_resize:p,_scrollRequest:p},br={buttonText:p,buttonHints:p,views:p,plugins:p,initialEvents:p,events:p,eventSources:p},ce={headerToolbar:de,footerToolbar:de,buttonText:de,buttonHints:de,buttonIcons:de,dateIncrement:de,plugins:qe,events:qe,eventSources:qe,resources:qe};function de(t,e){return typeof t=="object"&&typeof e=="object"&&t&&e?G(t,e):t===e}function qe(t,e){return Array.isArray(t)&&Array.isArray(e)?ie(t,e):t===e}const io={type:String,component:p,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:p,usesMinMaxTime:Boolean,classNames:p,content:p,didMount:p,willUnmount:p};function kt(t){return pn(t,ce)}function gn(t,e){let n={},r={};for(let i in e)i in t&&(n[i]=e[i](t[i]));for(let i in t)i in e||(r[i]=t[i]);return{refined:n,extra:r}}function p(t){return t}const{hasOwnProperty:dt}=Object.prototype;function pn(t,e){let n={};if(e){for(let r in e)if(e[r]===de){let i=[];for(let s=t.length-1;s>=0;s-=1){let l=t[s][r];if(typeof l=="object"&&l)i.unshift(l);else if(l!==void 0){n[r]=l;break}}i.length&&(n[r]=pn(i))}}for(let r=t.length-1;r>=0;r-=1){let i=t[r];for(let s in i)s in n||(n[s]=i[s])}return n}function ge(t,e){let n={};for(let r in t)e(t[r],r)&&(n[r]=t[r]);return n}function $(t,e){let n={};for(let r in t)n[r]=e(t[r],r);return n}function vi(t){let e={};for(let n of t)e[n]=!0;return e}function mn(t){let e=[];for(let n in t)e.push(t[n]);return e}function G(t,e){if(t===e)return!0;for(let n in t)if(dt.call(t,n)&&!(n in e))return!1;for(let n in e)if(dt.call(e,n)&&t[n]!==e[n])return!1;return!0}const so=/^on[A-Z]/;function lo(t,e){const n=Yt(t,e);for(let r of n)if(!so.test(r))return!1;return!0}function Yt(t,e){let n=[];for(let r in t)dt.call(t,r)&&(r in e||n.push(r));for(let r in e)dt.call(e,r)&&t[r]!==e[r]&&n.push(r);return n}function Nt(t,e,n={}){if(t===e)return!0;for(let r in e)if(!(r in t&&oo(t[r],e[r],n[r])))return!1;for(let r in t)if(!(r in e))return!1;return!0}function oo(t,e,n){return t===e||n===!0?!0:n?n(t,e):!1}function ao(t,e=0,n,r=1){let i=[];n==null&&(n=Object.keys(t).length);for(let s=e;s<n;s+=r){let l=t[s];l!==void 0&&i.push(l)}return i}let yi={};function co(t,e){yi[t]=e}function uo(t){return new yi[t]}class fo{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return U(e)}markerToArray(e){return te(e)}}co("gregory",fo);const ho=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;function go(t){let e=ho.exec(t);if(e){let n=new Date(Date.UTC(Number(e[1]),e[3]?Number(e[3])-1:0,Number(e[5]||1),Number(e[7]||0),Number(e[8]||0),Number(e[10]||0),e[12]?+`0.${e[12]}`*1e3:0));if(pi(n)){let r=null;return e[13]&&(r=(e[15]==="-"?-1:1)*(Number(e[16]||0)*60+Number(e[18]||0))),{marker:n,isTimeUnspecified:!e[6],timeZoneOffset:r}}}return null}class po{constructor(e){let n=this.timeZone=e.timeZone,r=n!=="local"&&n!=="UTC";e.namedTimeZoneImpl&&r&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(n)),this.canComputeOffset=!!(!r||this.namedTimeZoneImpl),this.calendarSystem=uo(e.calendarSystem),this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,e.weekNumberCalculation==="ISO"&&(this.weekDow=1,this.weekDoy=4),typeof e.firstDay=="number"&&(this.weekDow=e.firstDay),typeof e.weekNumberCalculation=="function"&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=e.weekText!=null?e.weekText:e.locale.options.weekText,this.weekTextLong=(e.weekTextLong!=null?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let n=this.createMarkerMeta(e);return n===null?null:n.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker(new Date().valueOf()):U(fr(new Date))}createMarkerMeta(e){if(typeof e=="string")return this.parse(e);let n=null;return typeof e=="number"?n=this.timestampToMarker(e):e instanceof Date?(e=e.valueOf(),isNaN(e)||(n=this.timestampToMarker(e))):Array.isArray(e)&&(n=U(e)),n===null||!pi(n)?null:{marker:n,isTimeUnspecified:!1,forcedTzo:null}}parse(e){let n=go(e);if(n===null)return null;let{marker:r}=n,i=null;return n.timeZoneOffset!==null&&(this.canComputeOffset?r=this.timestampToMarker(r.valueOf()-n.timeZoneOffset*60*1e3):i=n.timeZoneOffset),{marker:r,isTimeUnspecified:n.isTimeUnspecified,forcedTzo:i}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]+=n.years,r[1]+=n.months,r[2]+=n.days,r[6]+=n.milliseconds,this.calendarSystem.arrayToMarker(r)}subtract(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]-=n.years,r[1]-=n.months,r[2]-=n.days,r[6]-=n.milliseconds,this.calendarSystem.arrayToMarker(r)}addYears(e,n){let r=this.calendarSystem.markerToArray(e);return r[0]+=n,this.calendarSystem.arrayToMarker(r)}addMonths(e,n){let r=this.calendarSystem.markerToArray(e);return r[1]+=n,this.calendarSystem.arrayToMarker(r)}diffWholeYears(e,n){let{calendarSystem:r}=this;return ne(e)===ne(n)&&r.getMarkerDay(e)===r.getMarkerDay(n)&&r.getMarkerMonth(e)===r.getMarkerMonth(n)?r.getMarkerYear(n)-r.getMarkerYear(e):null}diffWholeMonths(e,n){let{calendarSystem:r}=this;return ne(e)===ne(n)&&r.getMarkerDay(e)===r.getMarkerDay(n)?r.getMarkerMonth(n)-r.getMarkerMonth(e)+(r.getMarkerYear(n)-r.getMarkerYear(e))*12:null}greatestWholeUnit(e,n){let r=this.diffWholeYears(e,n);return r!==null?{unit:"year",value:r}:(r=this.diffWholeMonths(e,n),r!==null?{unit:"month",value:r}:(r=Hl(e,n),r!==null?{unit:"week",value:r}:(r=ot(e,n),r!==null?{unit:"day",value:r}:(r=Ml(e,n),et(r)?{unit:"hour",value:r}:(r=kl(e,n),et(r)?{unit:"minute",value:r}:(r=Nl(e,n),et(r)?{unit:"second",value:r}:{unit:"millisecond",value:n.valueOf()-e.valueOf()}))))))}countDurationsBetween(e,n,r){let i;return r.years&&(i=this.diffWholeYears(e,n),i!==null)?i/_l(r):r.months&&(i=this.diffWholeMonths(e,n),i!==null)?i/Tl(r):r.days&&(i=ot(e,n),i!==null)?i/Se(r):(n.valueOf()-e.valueOf())/z(r)}startOf(e,n){return n==="year"?this.startOfYear(e):n==="month"?this.startOfMonth(e):n==="week"?this.startOfWeek(e):n==="day"?I(e):n==="hour"?Pl(e):n==="minute"?Bl(e):n==="second"?Ll(e):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):Ul(e,this.weekDow,this.weekDoy)}format(e,n,r={}){return n.format({marker:e,timeZoneOffset:r.forcedTzo!=null?r.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,n,r,i={}){return i.isEndExclusive&&(n=se(n,-1)),r.formatRange({marker:e,timeZoneOffset:i.forcedStartTzo!=null?i.forcedStartTzo:this.offsetForMarker(e)},{marker:n,timeZoneOffset:i.forcedEndTzo!=null?i.forcedEndTzo:this.offsetForMarker(n)},this,i.defaultSeparator)}formatIso(e,n={}){let r=null;return n.omitTimeZoneOffset||(n.forcedTzo!=null?r=n.forcedTzo:r=this.offsetForMarker(e)),mi(e,r,n.omitTime)}timestampToMarker(e){return this.timeZone==="local"?U(fr(new Date(e))):this.timeZone==="UTC"||!this.namedTimeZoneImpl?new Date(e):U(this.namedTimeZoneImpl.timestampToArray(e))}offsetForMarker(e){return this.timeZone==="local"?-hr(te(e)).getTimezoneOffset():this.timeZone==="UTC"?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(te(e)):null}toDate(e,n){return this.timeZone==="local"?hr(te(e)):this.timeZone==="UTC"?new Date(e.valueOf()):this.namedTimeZoneImpl?new Date(e.valueOf()-this.namedTimeZoneImpl.offsetForArray(te(e))*1e3*60):new Date(e.valueOf()-(n||0))}}class je{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let n,r;if(typeof e=="object"&&e){n=Object.assign({},this.iconClasses);for(r in e)n[r]=this.applyIconOverridePrefix(e[r]);this.iconClasses=n}else e===!1&&(this.iconClasses={})}applyIconOverridePrefix(e){let n=this.iconOverridePrefix;return n&&e.indexOf(n)!==0&&(e=n+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,n){let r;return n&&this.rtlIconClasses?r=this.rtlIconClasses[e]||this.iconClasses[e]:r=this.iconClasses[e],r?`${this.baseIconClass} ${r}`:""}getCustomButtonIconClass(e){let n;return this.iconOverrideCustomButtonOption&&(n=e[this.iconOverrideCustomButtonOption],n)?`${this.baseIconClass} ${this.applyIconOverridePrefix(n)}`:""}}je.prototype.classes={};je.prototype.iconClasses={};je.prototype.baseIconClass="";je.prototype.iconOverridePrefix="";function ut(t){t();let e=A.debounceRendering,n=[];function r(i){n.push(i)}for(A.debounceRendering=r,Pe(f(mo,{}),document.createElement("div"));n.length;)n.shift()();A.debounceRendering=e}class mo extends j{render(){return f("div",{})}componentDidMount(){this.setState({})}}function bi(t){let e=js(t),n=e.Provider;return e.Provider=function(){let r=!this.getChildContext,i=n.apply(this,arguments);if(r){let s=[];this.shouldComponentUpdate=l=>{this.props.value!==l.value&&s.forEach(o=>{o.context=l.value,o.forceUpdate()})},this.sub=l=>{s.push(l);let o=l.componentWillUnmount;l.componentWillUnmount=()=>{s.splice(s.indexOf(l),1),o&&o.call(l)}}}return i},e}class vo{constructor(e,n,r,i){this.execFunc=e,this.emitter=n,this.scrollTime=r,this.scrollTimeReset=i,this.handleScrollRequest=s=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},s),this.drain()},n.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}const J=bi({});function yo(t,e,n,r,i,s,l,o,a,d,c,g,h){return{dateEnv:i,options:n,pluginHooks:l,emitter:d,dispatch:o,getCurrentData:a,calendarApi:c,viewSpec:t,viewApi:e,dateProfileGenerator:r,theme:s,isRtl:n.direction==="rtl",addResizeHandler(u){d.on("_resize",u)},removeResizeHandler(u){d.off("_resize",u)},createScrollResponder(u){return new vo(u,d,C(n.scrollTime),n.scrollTimeReset)},registerInteractiveComponent:g,unregisterInteractiveComponent:h}}class ve extends j{shouldComponentUpdate(e,n){return this.debug&&console.log(Yt(e,this.props),Yt(n,this.state)),!Nt(this.props,e,this.propEquality)||!Nt(this.state,n,this.stateEquality)}safeSetState(e){Nt(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}ve.addPropsEquality=bo;ve.addStateEquality=Eo;ve.contextType=J;ve.prototype.propEquality={};ve.prototype.stateEquality={};class _ extends ve{}_.contextType=J;function bo(t){let e=Object.create(this.prototype.propEquality);Object.assign(e,t),this.prototype.propEquality=e}function Eo(t){let e=Object.create(this.prototype.stateEquality);Object.assign(e,t),this.prototype.stateEquality=e}function X(t,e){typeof t=="function"?t(e):t&&(t.current=e)}class vn extends _{constructor(){super(...arguments),this.id=me(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{const{options:n}=this.context,{generatorName:r}=this.props;(!n.customRenderingReplaces||!Zt(r,n))&&this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&X(this.props.elRef,e)}}render(){const{props:e,context:n}=this,{options:r}=n,{customGenerator:i,defaultGenerator:s,renderProps:l}=e,o=Ei(e,[],this.handleEl);let a=!1,d,c=[],g;if(i!=null){const h=typeof i=="function"?i(l,f):i;if(h===!0)a=!0;else{const u=h&&typeof h=="object";u&&"html"in h?o.dangerouslySetInnerHTML={__html:h.html}:u&&"domNodes"in h?c=Array.prototype.slice.call(h.domNodes):(u?Vr(h):typeof h!="function")?d=h:g=h}}else a=!Zt(e.generatorName,r);return a&&s&&(d=s(l)),this.queuedDomNodes=c,this.currentGeneratorMeta=g,f(e.elTag,o,d)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var n;const{props:r,context:i}=this,{handleCustomRendering:s,customRenderingMetaMap:l}=i.options;if(s){const o=(n=this.currentGeneratorMeta)!==null&&n!==void 0?n:l==null?void 0:l[r.generatorName];o&&s(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:o},r),{elClasses:(r.elClasses||[]).filter(So)}))}}applyQueueudDomNodes(){const{queuedDomNodes:e,currentDomNodes:n}=this,r=this.base;if(!ie(e,n)){n.forEach(cn);for(let i of e)r.appendChild(i);this.currentDomNodes=e}}}vn.addPropsEquality({elClasses:ie,elStyle:G,elAttrs:lo,renderProps:G});function Zt(t,e){var n;return!!(e.handleCustomRendering&&t&&(!((n=e.customRenderingMetaMap)===null||n===void 0)&&n[t]))}function Ei(t,e,n){const r=Object.assign(Object.assign({},t.elAttrs),{ref:n});return(t.elClasses||e)&&(r.className=(t.elClasses||[]).concat(e||[]).concat(r.className||[]).filter(Boolean).join(" ")),t.elStyle&&(r.style=t.elStyle),r}function So(t){return!!t}const Si=bi(0);class B extends j{constructor(){super(...arguments),this.InnerContent=Ao.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(X(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){const{props:e}=this,n=Do(e.classNameGenerator,e.renderProps);if(e.children){const r=Ei(e,n,this.handleEl),i=e.children(this.InnerContent,e.renderProps,r);return e.elTag?f(e.elTag,r,i):i}else return f(vn,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(n),renderId:this.context}))}componentDidMount(){var e,n;this.el?(n=(e=this.props).didMount)===null||n===void 0||n.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,n;(n=(e=this.props).willUnmount)===null||n===void 0||n.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}B.contextType=Si;function Ao(t,e){const n=t.props;return f(vn,Object.assign({renderProps:n.renderProps,generatorName:n.generatorName,customGenerator:n.customGenerator,defaultGenerator:n.defaultGenerator,renderId:t.context},e))}function Do(t,e){const n=typeof t=="function"?t(e):t||[];return typeof n=="string"?[n]:n}class Be extends _{render(){let{props:e,context:n}=this,{options:r}=n,i={view:n.viewApi};return f(B,Object.assign({},e,{elTag:e.elTag||"div",elClasses:[...Ai(e.viewSpec),...e.elClasses||[]],renderProps:i,classNameGenerator:r.viewClassNames,generatorName:void 0,didMount:r.viewDidMount,willUnmount:r.viewWillUnmount}),()=>e.children)}}function Ai(t){return[`fc-${t.type}-view`,"fc-view"]}function wo(t,e){let n=null,r=null;return t.start&&(n=e.createMarker(t.start)),t.end&&(r=e.createMarker(t.end)),!n&&!r||n&&r&&r<n?null:{start:n,end:r}}function Er(t,e){let n=[],{start:r}=e,i,s;for(t.sort(Co),i=0;i<t.length;i+=1)s=t[i],s.start>r&&n.push({start:r,end:s.start}),s.end>r&&(r=s.end);return r<e.end&&n.push({start:r,end:e.end}),n}function Co(t,e){return t.start.valueOf()-e.start.valueOf()}function le(t,e){let{start:n,end:r}=t,i=null;return e.start!==null&&(n===null?n=e.start:n=new Date(Math.max(n.valueOf(),e.start.valueOf()))),e.end!=null&&(r===null?r=e.end:r=new Date(Math.min(r.valueOf(),e.end.valueOf()))),(n===null||r===null||n<r)&&(i={start:n,end:r}),i}function Ro(t,e){return(t.start===null?null:t.start.valueOf())===(e.start===null?null:e.start.valueOf())&&(t.end===null?null:t.end.valueOf())===(e.end===null?null:e.end.valueOf())}function yn(t,e){return(t.end===null||e.start===null||t.end>e.start)&&(t.start===null||e.end===null||t.start<e.end)}function Et(t,e){return(t.start===null||e.start!==null&&e.start>=t.start)&&(t.end===null||e.end!==null&&e.end<=t.end)}function Y(t,e){return(t.start===null||e>=t.start)&&(t.end===null||e<t.end)}function _o(t,e){return e.start!=null&&t<e.start?e.start:e.end!=null&&t>=e.end?new Date(e.end.valueOf()-1):t}function Di(t){let e=Math.floor(oe(t.start,t.end))||1,n=I(t.start),r=H(n,e);return{start:n,end:r}}function bn(t,e=C(0)){let n=null,r=null;if(t.end){r=I(t.end);let i=t.end.valueOf()-r.valueOf();i&&i>=z(e)&&(r=H(r,1))}return t.start&&(n=I(t.start),r&&r<=n&&(r=H(n,1))),{start:n,end:r}}function To(t){let e=bn(t);return oe(e.start,e.end)>1}function be(t,e,n,r){return r==="year"?C(n.diffWholeYears(t,e),"year"):r==="month"?C(n.diffWholeMonths(t,e),"month"):Ol(t,e)}function xo(t,e){switch(e.type){case"CHANGE_DATE":return e.dateMarker;default:return t}}function Io(t,e){let n=t.initialDate;return n!=null?e.createMarker(n):Fe(t.now,e)}function Fe(t,e){return typeof t=="function"&&(t=t()),t==null?e.createNowMarker():e.createMarker(t)}class wi{constructor(e){this.props=e,this.nowDate=Fe(e.nowInput,e.dateEnv),this.initHiddenDays()}buildPrev(e,n,r){let{dateEnv:i}=this.props,s=i.subtract(i.startOf(n,e.currentRangeUnit),e.dateIncrement);return this.build(s,-1,r)}buildNext(e,n,r){let{dateEnv:i}=this.props,s=i.add(i.startOf(n,e.currentRangeUnit),e.dateIncrement);return this.build(s,1,r)}build(e,n,r=!0){let{props:i}=this,s,l,o,a,d,c;return s=this.buildValidRange(),s=this.trimHiddenDays(s),r&&(e=_o(e,s)),l=this.buildCurrentRangeInfo(e,n),o=/^(year|month|week|day)$/.test(l.unit),a=this.buildRenderRange(this.trimHiddenDays(l.range),l.unit,o),a=this.trimHiddenDays(a),d=a,i.showNonCurrentDates||(d=le(d,l.range)),d=this.adjustActiveRange(d),d=le(d,s),c=yn(l.range,s),Y(a,e)||(e=a.start),{currentDate:e,validRange:s,currentRange:l.range,currentRangeUnit:l.unit,isRangeAllDay:o,activeRange:d,renderRange:a,slotMinTime:i.slotMinTime,slotMaxTime:i.slotMaxTime,isValid:c,dateIncrement:this.buildDateIncrement(l.duration)}}buildValidRange(){let e=this.props.validRangeInput,n=typeof e=="function"?e.call(this.props.calendarApi,this.nowDate):e;return this.refineRange(n)||{start:null,end:null}}buildCurrentRangeInfo(e,n){let{props:r}=this,i=null,s=null,l=null,o;return r.duration?(i=r.duration,s=r.durationUnit,l=this.buildRangeFromDuration(e,n,i,s)):(o=this.props.dayCount)?(s="day",l=this.buildRangeFromDayCount(e,n,o)):(l=this.buildCustomVisibleRange(e))?s=r.dateEnv.greatestWholeUnit(l.start,l.end).unit:(i=this.getFallbackDuration(),s=Qt(i).unit,l=this.buildRangeFromDuration(e,n,i,s)),{duration:i,unit:s,range:l}}getFallbackDuration(){return C({day:1})}adjustActiveRange(e){let{dateEnv:n,usesMinMaxTime:r,slotMinTime:i,slotMaxTime:s}=this.props,{start:l,end:o}=e;return r&&(Se(i)<0&&(l=I(l),l=n.add(l,i)),Se(s)>1&&(o=I(o),o=H(o,-1),o=n.add(o,s))),{start:l,end:o}}buildRangeFromDuration(e,n,r,i){let{dateEnv:s,dateAlignment:l}=this.props,o,a,d;if(!l){let{dateIncrement:g}=this.props;g&&z(g)<z(r)?l=Qt(g).unit:l=i}Se(r)<=1&&this.isHiddenDay(o)&&(o=this.skipHiddenDays(o,n),o=I(o));function c(){o=s.startOf(e,l),a=s.add(o,r),d={start:o,end:a}}return c(),this.trimHiddenDays(d)||(e=this.skipHiddenDays(e,n),c()),d}buildRangeFromDayCount(e,n,r){let{dateEnv:i,dateAlignment:s}=this.props,l=0,o=e,a;s&&(o=i.startOf(o,s)),o=I(o),o=this.skipHiddenDays(o,n),a=o;do a=H(a,1),this.isHiddenDay(a)||(l+=1);while(l<r);return{start:o,end:a}}buildCustomVisibleRange(e){let{props:n}=this,r=n.visibleRangeInput,i=typeof r=="function"?r.call(n.calendarApi,n.dateEnv.toDate(e)):r,s=this.refineRange(i);return s&&(s.start==null||s.end==null)?null:s}buildRenderRange(e,n,r){return e}buildDateIncrement(e){let{dateIncrement:n}=this.props,r;return n||((r=this.props.dateAlignment)?C(1,r):e||C({days:1}))}refineRange(e){if(e){let n=wo(e,this.props.dateEnv);return n&&(n=bn(n)),n}return null}initHiddenDays(){let e=this.props.hiddenDays||[],n=[],r=0,i;for(this.props.weekends===!1&&e.push(0,6),i=0;i<7;i+=1)(n[i]=e.indexOf(i)!==-1)||(r+=1);if(!r)throw new Error("invalid hiddenDays");this.isHiddenDayHash=n}trimHiddenDays(e){let{start:n,end:r}=e;return n&&(n=this.skipHiddenDays(n)),r&&(r=this.skipHiddenDays(r,-1,!0)),n==null||r==null||n<r?{start:n,end:r}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,n=1,r=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(r?n:0)+7)%7];)e=H(e,n);return e}}function En(t,e,n,r){return{instanceId:me(),defId:t,range:e,forcedStartTzo:n??null,forcedEndTzo:r??null}}function Mo(t,e,n,r){for(let i=0;i<r.length;i+=1){let s=r[i].parse(t,n);if(s){let{allDay:l}=t;return l==null&&(l=e,l==null&&(l=s.allDayGuess,l==null&&(l=!1))),{allDay:l,duration:s.duration,typeData:s.typeData,typeId:i}}}return null}function pe(t,e,n){let{dateEnv:r,pluginHooks:i,options:s}=n,{defs:l,instances:o}=t;o=ge(o,a=>!l[a.defId].recurringDef);for(let a in l){let d=l[a];if(d.recurringDef){let{duration:c}=d.recurringDef;c||(c=d.allDay?s.defaultAllDayEventDuration:s.defaultTimedEventDuration);let g=ko(d,c,e,r,i.recurringTypes);for(let h of g){let u=En(a,{start:h,end:r.add(h,c)});o[u.instanceId]=u}}}return{defs:l,instances:o}}function ko(t,e,n,r,i){let l=i[t.recurringDef.typeId].expand(t.recurringDef.typeData,{start:r.subtract(n.start,e),end:n.end},r);return t.allDay&&(l=l.map(I)),l}const nt={id:String,groupId:String,title:String,url:String,interactive:Boolean},Ci={start:p,end:p,date:p,allDay:Boolean},No=Object.assign(Object.assign(Object.assign({},nt),Ci),{extendedProps:p});function Ri(t,e,n,r,i=Sn(n),s,l){let{refined:o,extra:a}=_i(t,n,i),d=Ho(e,n),c=Mo(o,d,n.dateEnv,n.pluginHooks.recurringTypes);if(c){let h=$t(o,a,e?e.sourceId:"",c.allDay,!!c.duration,n,s);return h.recurringDef={typeId:c.typeId,typeData:c.typeData,duration:c.duration},{def:h,instance:null}}let g=Oo(o,d,n,r);if(g){let h=$t(o,a,e?e.sourceId:"",g.allDay,g.hasEnd,n,s),u=En(h.defId,g.range,g.forcedStartTzo,g.forcedEndTzo);return l&&h.publicId&&l[h.publicId]&&(u.instanceId=l[h.publicId]),{def:h,instance:u}}return null}function _i(t,e,n=Sn(e)){return gn(t,n)}function Sn(t){return Object.assign(Object.assign(Object.assign({},ft),No),t.pluginHooks.eventRefiners)}function $t(t,e,n,r,i,s,l){let o={title:t.title||"",groupId:t.groupId||"",publicId:t.id||"",url:t.url||"",recurringDef:null,defId:(l&&t.id?l[t.id]:"")||me(),sourceId:n,allDay:r,hasEnd:i,interactive:t.interactive,ui:ht(t,s),extendedProps:Object.assign(Object.assign({},t.extendedProps||{}),e)};for(let a of s.pluginHooks.eventDefMemberAdders)Object.assign(o,a(t));return Object.freeze(o.ui.classNames),Object.freeze(o.extendedProps),o}function Oo(t,e,n,r){let{allDay:i}=t,s,l=null,o=!1,a,d=null,c=t.start!=null?t.start:t.date;if(s=n.dateEnv.createMarkerMeta(c),s)l=s.marker;else if(!r)return null;return t.end!=null&&(a=n.dateEnv.createMarkerMeta(t.end)),i==null&&(e!=null?i=e:i=(!s||s.isTimeUnspecified)&&(!a||a.isTimeUnspecified)),i&&l&&(l=I(l)),a&&(d=a.marker,i&&(d=I(d)),l&&d<=l&&(d=null)),d?o=!0:r||(o=n.options.forceEventDuration||!1,d=n.dateEnv.add(l,i?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration)),{allDay:i,hasEnd:o,range:{start:l,end:d},forcedStartTzo:s?s.forcedTzo:null,forcedEndTzo:a?a.forcedTzo:null}}function Ho(t,e){let n=null;return t&&(n=t.defaultAllDay),n==null&&(n=e.options.defaultAllDay),n}function Le(t,e,n,r,i,s){let l=F(),o=Sn(n);for(let a of t){let d=Ri(a,e,n,r,o,i,s);d&&Xt(d,l)}return l}function Xt(t,e=F()){return e.defs[t.def.defId]=t.def,t.instance&&(e.instances[t.instance.instanceId]=t.instance),e}function An(t,e){let n=t.instances[e];if(n){let r=t.defs[n.defId],i=St(t,s=>Po(r,s));return i.defs[r.defId]=r,i.instances[n.instanceId]=n,i}return F()}function Po(t,e){return!!(t.groupId&&t.groupId===e.groupId)}function F(){return{defs:{},instances:{}}}function Dn(t,e){return{defs:Object.assign(Object.assign({},t.defs),e.defs),instances:Object.assign(Object.assign({},t.instances),e.instances)}}function St(t,e){let n=ge(t.defs,e),r=ge(t.instances,i=>n[i.defId]);return{defs:n,instances:r}}function Bo(t,e){let{defs:n,instances:r}=t,i={},s={};for(let l in n)e.defs[l]||(i[l]=n[l]);for(let l in r)!e.instances[l]&&i[r[l].defId]&&(s[l]=r[l]);return{defs:i,instances:s}}function Lo(t,e){return Array.isArray(t)?Le(t,null,e,!0):typeof t=="object"&&t?Le([t],null,e,!0):t!=null?String(t):null}function Sr(t){return Array.isArray(t)?t:typeof t=="string"?t.split(/\s+/):[]}const ft={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:p,overlap:p,allow:p,className:Sr,classNames:Sr,color:String,backgroundColor:String,borderColor:String,textColor:String},Uo={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function ht(t,e){let n=Lo(t.constraint,e);return{display:t.display||null,startEditable:t.startEditable!=null?t.startEditable:t.editable,durationEditable:t.durationEditable!=null?t.durationEditable:t.editable,constraints:n!=null?[n]:[],overlap:t.overlap!=null?t.overlap:null,allows:t.allow!=null?[t.allow]:[],backgroundColor:t.backgroundColor||t.color||"",borderColor:t.borderColor||t.color||"",textColor:t.textColor||"",classNames:(t.className||[]).concat(t.classNames||[])}}function Ti(t){return t.reduce(jo,Uo)}function jo(t,e){return{display:e.display!=null?e.display:t.display,startEditable:e.startEditable!=null?e.startEditable:t.startEditable,durationEditable:e.durationEditable!=null?e.durationEditable:t.durationEditable,constraints:t.constraints.concat(e.constraints),overlap:typeof e.overlap=="boolean"?e.overlap:t.overlap,allows:t.allows.concat(e.allows),backgroundColor:e.backgroundColor||t.backgroundColor,borderColor:e.borderColor||t.borderColor,textColor:e.textColor||t.textColor,classNames:t.classNames.concat(e.classNames)}}const Fo={id:String,defaultAllDay:Boolean,url:String,format:String,events:p,eventDataTransform:p,success:p,failure:p};function xi(t,e,n=Ii(e)){let r;if(typeof t=="string"?r={url:t}:typeof t=="function"||Array.isArray(t)?r={events:t}:typeof t=="object"&&t&&(r=t),r){let{refined:i,extra:s}=gn(r,n),l=zo(i,e);if(l)return{_raw:t,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:me(),sourceDefId:l.sourceDefId,meta:l.meta,ui:ht(i,e),extendedProps:s}}return null}function Ii(t){return Object.assign(Object.assign(Object.assign({},ft),Fo),t.pluginHooks.eventSourceRefiners)}function zo(t,e){let n=e.pluginHooks.eventSourceDefs;for(let r=n.length-1;r>=0;r-=1){let s=n[r].parseMeta(t);if(s)return{sourceDefId:r,meta:s}}return null}function Wo(t,e,n,r,i){switch(e.type){case"RECEIVE_EVENTS":return Vo(t,n[e.sourceId],e.fetchId,e.fetchRange,e.rawEvents,i);case"RESET_RAW_EVENTS":return Go(t,n[e.sourceId],e.rawEvents,r.activeRange,i);case"ADD_EVENTS":return qo(t,e.eventStore,r?r.activeRange:null,i);case"RESET_EVENTS":return e.eventStore;case"MERGE_EVENTS":return Dn(t,e.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return r?pe(t,r.activeRange,i):t;case"REMOVE_EVENTS":return Bo(t,e.eventStore);case"REMOVE_EVENT_SOURCE":return ki(t,e.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return St(t,s=>!s.sourceId);case"REMOVE_ALL_EVENTS":return F();default:return t}}function Vo(t,e,n,r,i,s){if(e&&n===e.latestFetchId){let l=Le(Mi(i,e,s),e,s);return r&&(l=pe(l,r,s)),Dn(ki(t,e.sourceId),l)}return t}function Go(t,e,n,r,i){const{defIdMap:s,instanceIdMap:l}=Yo(t);let o=Le(Mi(n,e,i),e,i,!1,s,l);return pe(o,r,i)}function Mi(t,e,n){let r=n.options.eventDataTransform,i=e?e.eventDataTransform:null;return i&&(t=Ar(t,i)),r&&(t=Ar(t,r)),t}function Ar(t,e){let n;if(!e)n=t;else{n=[];for(let r of t){let i=e(r);i?n.push(i):i==null&&n.push(r)}}return n}function qo(t,e,n,r){return n&&(e=pe(e,n,r)),Dn(t,e)}function Dr(t,e,n){let{defs:r}=t,i=$(t.instances,s=>r[s.defId].allDay?s:Object.assign(Object.assign({},s),{range:{start:n.createMarker(e.toDate(s.range.start,s.forcedStartTzo)),end:n.createMarker(e.toDate(s.range.end,s.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:s.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:s.forcedEndTzo}));return{defs:r,instances:i}}function ki(t,e){return St(t,n=>n.sourceId!==e)}function Qo(t,e){return{defs:t.defs,instances:ge(t.instances,n=>!e[n.instanceId])}}function Yo(t){const{defs:e,instances:n}=t,r={},i={};for(let s in e){const l=e[s],{publicId:o}=l;o&&(r[o]=s)}for(let s in n){const l=n[s],o=e[l.defId],{publicId:a}=o;a&&(i[a]=s)}return{defIdMap:r,instanceIdMap:i}}class At{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,n){Zo(this.handlers,e,n)}off(e,n){$o(this.handlers,e,n)}trigger(e,...n){let r=this.handlers[e]||[],i=this.options&&this.options[e],s=[].concat(i||[],r);for(let l of s)l.apply(this.thisContext,n)}hasHandlers(e){return!!(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}function Zo(t,e,n){(t[e]||(t[e]=[])).push(n)}function $o(t,e,n){n?t[e]&&(t[e]=t[e].filter(r=>r!==n)):delete t[e]}const Xo={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function Jo(t,e){return Le(Ko(t),null,e)}function Ko(t){let e;return t===!0?e=[{}]:Array.isArray(t)?e=t.filter(n=>n.daysOfWeek):typeof t=="object"&&t?e=[t]:e=[],e=e.map(n=>Object.assign(Object.assign({},Xo),n)),e}function Ni(t,e,n){n.emitter.trigger("select",Object.assign(Object.assign({},wn(t,n)),{jsEvent:e?e.origEvent:null,view:n.viewApi||n.calendarApi.view}))}function ea(t,e){e.emitter.trigger("unselect",{jsEvent:t?t.origEvent:null,view:e.viewApi||e.calendarApi.view})}function wn(t,e){let n={};for(let r of e.pluginHooks.dateSpanTransforms)Object.assign(n,r(t,e));return Object.assign(n,ga(t,e.dateEnv)),n}function wr(t,e,n){let{dateEnv:r,options:i}=n,s=e;return t?(s=I(s),s=r.add(s,i.defaultAllDayEventDuration)):s=r.add(s,i.defaultTimedEventDuration),s}function Cn(t,e,n,r){let i=gt(t.defs,e),s=F();for(let l in t.defs){let o=t.defs[l];s.defs[l]=ta(o,i[l],n,r)}for(let l in t.instances){let o=t.instances[l],a=s.defs[o.defId];s.instances[l]=na(o,a,i[o.defId],n,r)}return s}function ta(t,e,n,r){let i=n.standardProps||{};i.hasEnd==null&&e.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);let s=Object.assign(Object.assign(Object.assign({},t),i),{ui:Object.assign(Object.assign({},t.ui),i.ui)});n.extendedProps&&(s.extendedProps=Object.assign(Object.assign({},s.extendedProps),n.extendedProps));for(let l of r.pluginHooks.eventDefMutationAppliers)l(s,n,r);return!s.hasEnd&&r.options.forceEventDuration&&(s.hasEnd=!0),s}function na(t,e,n,r,i){let{dateEnv:s}=i,l=r.standardProps&&r.standardProps.allDay===!0,o=r.standardProps&&r.standardProps.hasEnd===!1,a=Object.assign({},t);return l&&(a.range=Di(a.range)),r.datesDelta&&n.startEditable&&(a.range={start:s.add(a.range.start,r.datesDelta),end:s.add(a.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(a.range={start:s.add(a.range.start,r.startDelta),end:a.range.end}),r.endDelta&&n.durationEditable&&(a.range={start:a.range.start,end:s.add(a.range.end,r.endDelta)}),o&&(a.range={start:a.range.start,end:wr(e.allDay,a.range.start,i)}),e.allDay&&(a.range={start:I(a.range.start),end:I(a.range.end)}),a.range.end<a.range.start&&(a.range.end=wr(e.allDay,a.range.start,i)),a}class ye{constructor(e,n){this.context=e,this.internalEventSource=n}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class N{constructor(e,n,r){this._context=e,this._def=n,this._instance=r||null}setProp(e,n){if(e in Ci)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if(e==="id")n=nt[e](n),this.mutate({standardProps:{publicId:n}});else if(e in nt)n=nt[e](n),this.mutate({standardProps:{[e]:n}});else if(e in ft){let r=ft[e](n);e==="color"?r={backgroundColor:n,borderColor:n}:e==="editable"?r={startEditable:n,durationEditable:n}:r={[e]:n},this.mutate({standardProps:{ui:r}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,n){this.mutate({extendedProps:{[e]:n}})}setStart(e,n={}){let{dateEnv:r}=this._context,i=r.createMarker(e);if(i&&this._instance){let s=this._instance.range,l=be(s.start,i,r,n.granularity);n.maintainDuration?this.mutate({datesDelta:l}):this.mutate({startDelta:l})}}setEnd(e,n={}){let{dateEnv:r}=this._context,i;if(!(e!=null&&(i=r.createMarker(e),!i))&&this._instance)if(i){let s=be(this._instance.range.end,i,r,n.granularity);this.mutate({endDelta:s})}else this.mutate({standardProps:{hasEnd:!1}})}setDates(e,n,r={}){let{dateEnv:i}=this._context,s={allDay:r.allDay},l=i.createMarker(e),o;if(l&&!(n!=null&&(o=i.createMarker(n),!o))&&this._instance){let a=this._instance.range;r.allDay===!0&&(a=Di(a));let d=be(a.start,l,i,r.granularity);if(o){let c=be(a.end,o,i,r.granularity);wl(d,c)?this.mutate({datesDelta:d,standardProps:s}):this.mutate({startDelta:d,endDelta:c,standardProps:s})}else s.hasEnd=!1,this.mutate({datesDelta:d,standardProps:s})}}moveStart(e){let n=C(e);n&&this.mutate({startDelta:n})}moveEnd(e){let n=C(e);n&&this.mutate({endDelta:n})}moveDates(e){let n=C(e);n&&this.mutate({datesDelta:n})}setAllDay(e,n={}){let r={allDay:e},{maintainDuration:i}=n;i==null&&(i=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(r.hasEnd=i),this.mutate({standardProps:r})}formatRange(e){let{dateEnv:n}=this._context,r=this._instance,i=M(e);return this._def.hasEnd?n.formatRange(r.range.start,r.range.end,i,{forcedStartTzo:r.forcedStartTzo,forcedEndTzo:r.forcedEndTzo}):n.format(r.range.start,i,{forcedTzo:r.forcedStartTzo})}mutate(e){let n=this._instance;if(n){let r=this._def,i=this._context,{eventStore:s}=i.getCurrentData(),l=An(s,n.instanceId);l=Cn(l,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,i);let a=new N(i,r,n);this._def=l.defs[r.defId],this._instance=l.instances[n.instanceId],i.dispatch({type:"MERGE_EVENTS",eventStore:l}),i.emitter.trigger("eventChange",{oldEvent:a,event:this,relatedEvents:fe(l,i,n),revert(){i.dispatch({type:"RESET_EVENTS",eventStore:s})}})}}remove(){let e=this._context,n=Oi(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:n}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:n})}})}get source(){let{sourceId:e}=this._def;return e?new ye(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let n=this._def,{ui:r}=n,{startStr:i,endStr:s}=this,l={allDay:n.allDay};return n.title&&(l.title=n.title),i&&(l.start=i),s&&(l.end=s),n.publicId&&(l.id=n.publicId),n.groupId&&(l.groupId=n.groupId),n.url&&(l.url=n.url),r.display&&r.display!=="auto"&&(l.display=r.display),e.collapseColor&&r.backgroundColor&&r.backgroundColor===r.borderColor?l.color=r.backgroundColor:(r.backgroundColor&&(l.backgroundColor=r.backgroundColor),r.borderColor&&(l.borderColor=r.borderColor)),r.textColor&&(l.textColor=r.textColor),r.classNames.length&&(l.classNames=r.classNames),Object.keys(n.extendedProps).length&&(e.collapseExtendedProps?Object.assign(l,n.extendedProps):l.extendedProps=n.extendedProps),l}toJSON(){return this.toPlainObject()}}function Oi(t){let e=t._def,n=t._instance;return{defs:{[e.defId]:e},instances:n?{[n.instanceId]:n}:{}}}function fe(t,e,n){let{defs:r,instances:i}=t,s=[],l=n?n.instanceId:"";for(let o in i){let a=i[o],d=r[a.defId];a.instanceId!==l&&s.push(new N(e,d,a))}return s}function Jt(t,e,n,r){let i={},s={},l={},o=[],a=[],d=gt(t.defs,e);for(let c in t.defs){let g=t.defs[c];d[g.defId].display==="inverse-background"&&(g.groupId?(i[g.groupId]=[],l[g.groupId]||(l[g.groupId]=g)):s[c]=[])}for(let c in t.instances){let g=t.instances[c],h=t.defs[g.defId],u=d[h.defId],m=g.range,v=!h.allDay&&r?bn(m,r):m,y=le(v,n);y&&(u.display==="inverse-background"?h.groupId?i[h.groupId].push(y):s[g.defId].push(y):u.display!=="none"&&(u.display==="background"?o:a).push({def:h,ui:u,instance:g,range:y,isStart:v.start&&v.start.valueOf()===y.start.valueOf(),isEnd:v.end&&v.end.valueOf()===y.end.valueOf()}))}for(let c in i){let g=i[c],h=Er(g,n);for(let u of h){let m=l[c],v=d[m.defId];o.push({def:m,ui:v,instance:null,range:u,isStart:!1,isEnd:!1})}}for(let c in s){let g=s[c],h=Er(g,n);for(let u of h)o.push({def:t.defs[c],ui:d[c],instance:null,range:u,isStart:!1,isEnd:!1})}return{bg:o,fg:a}}function ra(t){return t.ui.display==="background"||t.ui.display==="inverse-background"}function Cr(t,e){t.fcSeg=e}function we(t){return t.fcSeg||t.parentNode.fcSeg||null}function gt(t,e){return $(t,n=>Hi(n,e))}function Hi(t,e){let n=[];return e[""]&&n.push(e[""]),e[t.defId]&&n.push(e[t.defId]),n.push(t.ui),Ti(n)}function Rn(t,e){let n=t.map(ia);return n.sort((r,i)=>vl(r,i,e)),n.map(r=>r._seg)}function ia(t){let{eventRange:e}=t,n=e.def,r=e.instance?e.instance.range:e.range,i=r.start?r.start.valueOf():0,s=r.end?r.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},n.extendedProps),n),{id:n.publicId,start:i,end:s,duration:s-i,allDay:Number(n.allDay),_seg:t})}function sa(t,e){let{pluginHooks:n}=e,r=n.isDraggableTransformers,{def:i,ui:s}=t.eventRange,l=s.startEditable;for(let o of r)l=o(l,i,s,e);return l}function la(t,e){return t.isStart&&t.eventRange.ui.durationEditable&&e.options.eventResizableFromStart}function oa(t,e){return t.isEnd&&t.eventRange.ui.durationEditable}function Oe(t,e,n,r,i,s,l){let{dateEnv:o,options:a}=n,{displayEventTime:d,displayEventEnd:c}=a,g=t.eventRange.def,h=t.eventRange.instance;d==null&&(d=r!==!1),c==null&&(c=i!==!1);let u=h.range.start,m=h.range.end,v=s||t.start||t.eventRange.range.start,y=l||t.end||t.eventRange.range.end,b=I(u).valueOf()===I(v).valueOf(),E=I(se(m,-1)).valueOf()===I(se(y,-1)).valueOf();return d&&!g.allDay&&(b||E)?(v=b?u:v,y=E?m:y,c&&g.hasEnd?o.formatRange(v,y,e,{forcedStartTzo:s?null:h.forcedStartTzo,forcedEndTzo:l?null:h.forcedEndTzo}):o.format(v,e,{forcedTzo:s?null:h.forcedStartTzo})):""}function Z(t,e,n){let r=t.eventRange.range;return{isPast:r.end<=(n||e.start),isFuture:r.start>=(n||e.end),isToday:e&&Y(e,r.start)}}function aa(t){let e=["fc-event"];return t.isMirror&&e.push("fc-event-mirror"),t.isDraggable&&e.push("fc-event-draggable"),(t.isStartResizable||t.isEndResizable)&&e.push("fc-event-resizable"),t.isDragging&&e.push("fc-event-dragging"),t.isResizing&&e.push("fc-event-resizing"),t.isSelected&&e.push("fc-event-selected"),t.isStart&&e.push("fc-event-start"),t.isEnd&&e.push("fc-event-end"),t.isPast&&e.push("fc-event-past"),t.isToday&&e.push("fc-event-today"),t.isFuture&&e.push("fc-event-future"),e}function Pi(t){return t.instance?t.instance.instanceId:`${t.def.defId}:${t.range.start.toISOString()}`}function _n(t,e){let{def:n,instance:r}=t.eventRange,{url:i}=n;if(i)return{href:i};let{emitter:s,options:l}=e,{eventInteractive:o}=l;return o==null&&(o=n.interactive,o==null&&(o=!!s.hasHandlers("eventClick"))),o?gi(a=>{s.trigger("eventClick",{el:a.target,event:new N(e,n,r),jsEvent:a,view:e.viewApi})}):{}}const ca={start:p,end:p,allDay:Boolean};function da(t,e,n){let r=ua(t,e),{range:i}=r;if(!i.start)return null;if(!i.end){if(n==null)return null;i.end=e.add(i.start,n)}return r}function ua(t,e){let{refined:n,extra:r}=gn(t,ca),i=n.start?e.createMarkerMeta(n.start):null,s=n.end?e.createMarkerMeta(n.end):null,{allDay:l}=n;return l==null&&(l=i&&i.isTimeUnspecified&&(!s||s.isTimeUnspecified)),Object.assign({range:{start:i?i.marker:null,end:s?s.marker:null},allDay:l},r)}function fa(t,e){return Ro(t.range,e.range)&&t.allDay===e.allDay&&ha(t,e)}function ha(t,e){for(let n in e)if(n!=="range"&&n!=="allDay"&&t[n]!==e[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}function ga(t,e){return Object.assign(Object.assign({},Li(t.range,e,t.allDay)),{allDay:t.allDay})}function Bi(t,e,n){return Object.assign(Object.assign({},Li(t,e,n)),{timeZone:e.timeZone})}function Li(t,e,n){return{start:e.toDate(t.start),end:e.toDate(t.end),startStr:e.formatIso(t.start,{omitTime:n}),endStr:e.formatIso(t.end,{omitTime:n})}}function pa(t,e,n){let r=_i({editable:!1},n),i=$t(r.refined,r.extra,"",t.allDay,!0,n);return{def:i,ui:Hi(i,e),instance:En(i.defId,t.range),range:t.range,isStart:!0,isEnd:!0}}function ma(t,e,n){let r=!1,i=function(o){r||(r=!0,e(o))},s=function(o){r||(r=!0,n(o))},l=t(i,s);l&&typeof l.then=="function"&&l.then(i,s)}class Rr extends Error{constructor(e,n){super(e),this.response=n}}function va(t,e,n){t=t.toUpperCase();const r={method:t};return t==="GET"?e+=(e.indexOf("?")===-1?"?":"&")+new URLSearchParams(n):(r.body=new URLSearchParams(n),r.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(e,r).then(i=>{if(i.ok)return i.json().then(s=>[s,i],()=>{throw new Rr("Failure parsing JSON",i)});throw new Rr("Request failed",i)})}let Ot;function Ui(){return Ot==null&&(Ot=ya()),Ot}function ya(){if(typeof document>"u")return!0;let t=document.createElement("div");t.style.position="absolute",t.style.top="0px",t.style.left="0px",t.innerHTML="<table><tr><td><div></div></td></tr></table>",t.querySelector("table").style.height="100px",t.querySelector("div").style.height="100%",document.body.appendChild(t);let n=t.querySelector("div").offsetHeight>0;return document.body.removeChild(t),n}class ba extends _{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{ut(()=>{this.setState({forPrint:!0})})},this.handleAfterPrint=()=>{ut(()=>{this.setState({forPrint:!1})})}}render(){let{props:e}=this,{options:n}=e,{forPrint:r}=this.state,i=r||n.height==="auto"||n.contentHeight==="auto",s=!i&&n.height!=null?n.height:"",l=["fc",r?"fc-media-print":"fc-media-screen",`fc-direction-${n.direction}`,e.theme.getClass("root")];return Ui()||l.push("fc-liquid-hack"),e.children(l,s,i,r)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}class _e{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}function Ea(t,e){return{component:t,el:e.el,useEventCenter:e.useEventCenter!=null?e.useEventCenter:!0,isHitComboAllowed:e.isHitComboAllowed||null}}function Tn(t){return{[t.component.uid]:t}}const Kt={};class Sa{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,n){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:n})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,n){let{currentDataManager:r}=this;r.currentCalendarOptionsRefiners[e]?r.emitter.on(e,n):console.warn(`Unknown listener name '${e}'`)}off(e,n){this.currentDataManager.emitter.off(e,n)}trigger(e,...n){this.currentDataManager.emitter.trigger(e,...n)}changeView(e,n){this.batchRendering(()=>{if(this.unselect(),n)if(n.start&&n.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:n});else{let{dateEnv:r}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:r.createMarker(n)})}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})}zoomTo(e,n){let r=this.getCurrentData(),i;n=n||"day",i=r.viewSpecs[n]||this.getUnitViewSpec(n),this.unselect(),i?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:i.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let{viewSpecs:n,toolbarConfig:r}=this.getCurrentData(),i=[].concat(r.header?r.header.viewsWithButtons:[],r.footer?r.footer.viewsWithButtons:[]),s,l;for(let o in n)i.push(o);for(s=0;s<i.length;s+=1)if(l=n[i[s]],l&&l.singleUnit===e)return l;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:Fe(e.calendarOptions.now,e.dateEnv)})}gotoDate(e){let n=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:n.dateEnv.createMarker(e)})}incrementDate(e){let n=this.getCurrentData(),r=C(e);r&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:n.dateEnv.add(n.currentDate,r)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,n){let{dateEnv:r}=this.getCurrentData();return r.format(r.createMarker(e),M(n))}formatRange(e,n,r){let{dateEnv:i}=this.getCurrentData();return i.formatRange(i.createMarker(e),i.createMarker(n),M(r),r)}formatIso(e,n){let{dateEnv:r}=this.getCurrentData();return r.formatIso(r.createMarker(e),{omitTime:n})}select(e,n){let r;n==null?e.start!=null?r=e:r={start:e,end:null}:r={start:e,end:n};let i=this.getCurrentData(),s=da(r,i.dateEnv,C({days:1}));s&&(this.dispatch({type:"SELECT_DATES",selection:s}),Ni(s,null,i))}unselect(e){let n=this.getCurrentData();n.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),ea(e,n))}addEvent(e,n){if(e instanceof N){let l=e._def,o=e._instance;return this.getCurrentData().eventStore.defs[l.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:Xt({def:l,instance:o})}),this.triggerEventAdd(e)),e}let r=this.getCurrentData(),i;if(n instanceof ye)i=n.internalEventSource;else if(typeof n=="boolean")n&&([i]=mn(r.eventSources));else if(n!=null){let l=this.getEventSourceById(n);if(!l)return console.warn(`Could not find an event source with ID "${n}"`),null;i=l.internalEventSource}let s=Ri(e,i,r,!1);if(s){let l=new N(r,s.def,s.def.recurringDef?null:s.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:Xt(s)}),this.triggerEventAdd(l),l}return null}triggerEventAdd(e){let{emitter:n}=this.getCurrentData();n.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:Oi(e)})}})}getEventById(e){let n=this.getCurrentData(),{defs:r,instances:i}=n.eventStore;e=String(e);for(let s in r){let l=r[s];if(l.publicId===e){if(l.recurringDef)return new N(n,l,null);for(let o in i){let a=i[o];if(a.defId===l.defId)return new N(n,l,a)}}}return null}getEvents(){let e=this.getCurrentData();return fe(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),n=e.eventSources,r=[];for(let i in n)r.push(new ye(e,n[i]));return r}getEventSourceById(e){let n=this.getCurrentData(),r=n.eventSources;e=String(e);for(let i in r)if(r[i].publicId===e)return new ye(n,r[i]);return null}addEventSource(e){let n=this.getCurrentData();if(e instanceof ye)return n.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let r=xi(e,n);return r?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[r]}),new ye(n,r)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let n=C(e);n&&this.trigger("_scrollRequest",{time:n})}}function Aa(t,e){return t.left>=e.left&&t.left<e.right&&t.top>=e.top&&t.top<e.bottom}function ji(t,e){let n={left:Math.max(t.left,e.left),right:Math.min(t.right,e.right),top:Math.max(t.top,e.top),bottom:Math.min(t.bottom,e.bottom)};return n.left<n.right&&n.top<n.bottom?n:!1}function Da(t,e){return{left:Math.min(Math.max(t.left,e.left),e.right),top:Math.min(Math.max(t.top,e.top),e.bottom)}}function wa(t){return{left:(t.left+t.right)/2,top:(t.top+t.bottom)/2}}function Ca(t,e){return{left:t.left-e.left,top:t.top-e.top}}const Ht=F();class Ra{constructor(){this.getKeysForEventDefs=S(this._getKeysForEventDefs),this.splitDateSelection=S(this._splitDateSpan),this.splitEventStore=S(this._splitEventStore),this.splitIndividualUi=S(this._splitIndividualUi),this.splitEventDrag=S(this._splitInteraction),this.splitEventResize=S(this._splitInteraction),this.eventUiBuilders={}}splitProps(e){let n=this.getKeyInfo(e),r=this.getKeysForEventDefs(e.eventStore),i=this.splitDateSelection(e.dateSelection),s=this.splitIndividualUi(e.eventUiBases,r),l=this.splitEventStore(e.eventStore,r),o=this.splitEventDrag(e.eventDrag),a=this.splitEventResize(e.eventResize),d={};this.eventUiBuilders=$(n,(c,g)=>this.eventUiBuilders[g]||S(_a));for(let c in n){let g=n[c],h=l[c]||Ht,u=this.eventUiBuilders[c];d[c]={businessHours:g.businessHours||e.businessHours,dateSelection:i[c]||null,eventStore:h,eventUiBases:u(e.eventUiBases[""],g.ui,s[c]),eventSelection:h.instances[e.eventSelection]?e.eventSelection:"",eventDrag:o[c]||null,eventResize:a[c]||null}}return d}_splitDateSpan(e){let n={};if(e){let r=this.getKeysForDateSpan(e);for(let i of r)n[i]=e}return n}_getKeysForEventDefs(e){return $(e.defs,n=>this.getKeysForEventDef(n))}_splitEventStore(e,n){let{defs:r,instances:i}=e,s={};for(let l in r)for(let o of n[l])s[o]||(s[o]=F()),s[o].defs[l]=r[l];for(let l in i){let o=i[l];for(let a of n[o.defId])s[a]&&(s[a].instances[l]=o)}return s}_splitIndividualUi(e,n){let r={};for(let i in e)if(i)for(let s of n[i])r[s]||(r[s]={}),r[s][i]=e[i];return r}_splitInteraction(e){let n={};if(e){let r=this._splitEventStore(e.affectedEvents,this._getKeysForEventDefs(e.affectedEvents)),i=this._getKeysForEventDefs(e.mutatedEvents),s=this._splitEventStore(e.mutatedEvents,i),l=o=>{n[o]||(n[o]={affectedEvents:r[o]||Ht,mutatedEvents:s[o]||Ht,isEvent:e.isEvent})};for(let o in r)l(o);for(let o in s)l(o)}return n}}function _a(t,e,n){let r=[];t&&r.push(t),e&&r.push(e);let i={"":Ti(r)};return n&&Object.assign(i,n),i}function xn(t,e,n,r){return{dow:t.getUTCDay(),isDisabled:!!(r&&!Y(r.activeRange,t)),isOther:!!(r&&!Y(r.currentRange,t)),isToday:!!(e&&Y(e,t)),isPast:!!(n?t<n:e&&t<e.start),isFuture:!!(n?t>n:e&&t>=e.end)}}function Dt(t,e){let n=["fc-day",`fc-day-${xl[t.dow]}`];return t.isDisabled?n.push("fc-day-disabled"):(t.isToday&&(n.push("fc-day-today"),n.push(e.getClass("today"))),t.isPast&&n.push("fc-day-past"),t.isFuture&&n.push("fc-day-future"),t.isOther&&n.push("fc-day-other")),n}const Ta=M({year:"numeric",month:"long",day:"numeric"}),xa=M({week:"long"});function Ce(t,e,n="day",r=!0){const{dateEnv:i,options:s,calendarApi:l}=t;let o=i.format(e,n==="week"?xa:Ta);if(s.navLinks){let a=i.toDate(e);const d=c=>{let g=n==="day"?s.navLinkDayClick:n==="week"?s.navLinkWeekClick:null;typeof g=="function"?g.call(l,i.toDate(e),c):(typeof g=="string"&&(n=g),l.zoomTo(e,n))};return Object.assign({title:ke(s.navLinkHint,[o,a],o),"data-navlink":""},r?hi(d):{onClick:d})}return{"aria-label":o}}let Pt=null;function Ia(){return Pt===null&&(Pt=Ma()),Pt}function Ma(){let t=document.createElement("div");Me(t,{position:"absolute",top:-1e3,left:0,border:0,padding:0,overflow:"scroll",direction:"rtl"}),t.innerHTML="<div></div>",document.body.appendChild(t);let n=t.firstChild.getBoundingClientRect().left>t.getBoundingClientRect().left;return cn(t),n}let Bt;function ka(){return Bt||(Bt=Na()),Bt}function Na(){let t=document.createElement("div");t.style.overflow="scroll",t.style.position="absolute",t.style.top="-9999px",t.style.left="-9999px",document.body.appendChild(t);let e=Fi(t);return document.body.removeChild(t),e}function Fi(t){return{x:t.offsetHeight-t.clientHeight,y:t.offsetWidth-t.clientWidth}}function Oa(t,e=!1){let n=window.getComputedStyle(t),r=parseInt(n.borderLeftWidth,10)||0,i=parseInt(n.borderRightWidth,10)||0,s=parseInt(n.borderTopWidth,10)||0,l=parseInt(n.borderBottomWidth,10)||0,o=Fi(t),a=o.y-r-i,d=o.x-s-l,c={borderLeft:r,borderRight:i,borderTop:s,borderBottom:l,scrollbarBottom:d,scrollbarLeft:0,scrollbarRight:0};return Ia()&&n.direction==="rtl"?c.scrollbarLeft=a:c.scrollbarRight=a,e&&(c.paddingLeft=parseInt(n.paddingLeft,10)||0,c.paddingRight=parseInt(n.paddingRight,10)||0,c.paddingTop=parseInt(n.paddingTop,10)||0,c.paddingBottom=parseInt(n.paddingBottom,10)||0),c}function Ha(t,e=!1,n){let r=In(t),i=Oa(t,e),s={left:r.left+i.borderLeft+i.scrollbarLeft,right:r.right-i.borderRight-i.scrollbarRight,top:r.top+i.borderTop,bottom:r.bottom-i.borderBottom-i.scrollbarBottom};return e&&(s.left+=i.paddingLeft,s.right-=i.paddingRight,s.top+=i.paddingTop,s.bottom-=i.paddingBottom),s}function In(t){let e=t.getBoundingClientRect();return{left:e.left+window.scrollX,top:e.top+window.scrollY,right:e.right+window.scrollX,bottom:e.bottom+window.scrollY}}function Pa(t){let e=zi(t),n=t.getBoundingClientRect();for(let r of e){let i=ji(n,r.getBoundingClientRect());if(i)n=i;else return null}return n}function zi(t){let e=[];for(;t instanceof HTMLElement;){let n=window.getComputedStyle(t);if(n.position==="fixed")break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&e.push(t),t=t.parentNode}return e}class Re{constructor(e,n,r,i){this.els=n;let s=this.originClientRect=e.getBoundingClientRect();r&&this.buildElHorizontals(s.left),i&&this.buildElVerticals(s.top)}buildElHorizontals(e){let n=[],r=[];for(let i of this.els){let s=i.getBoundingClientRect();n.push(s.left-e),r.push(s.right-e)}this.lefts=n,this.rights=r}buildElVerticals(e){let n=[],r=[];for(let i of this.els){let s=i.getBoundingClientRect();n.push(s.top-e),r.push(s.bottom-e)}this.tops=n,this.bottoms=r}leftToIndex(e){let{lefts:n,rights:r}=this,i=n.length,s;for(s=0;s<i;s+=1)if(e>=n[s]&&e<r[s])return s}topToIndex(e){let{tops:n,bottoms:r}=this,i=n.length,s;for(s=0;s<i;s+=1)if(e>=n[s]&&e<r[s])return s}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return Qe(this.tops||[],e.tops||[])&&Qe(this.bottoms||[],e.bottoms||[])&&Qe(this.lefts||[],e.lefts||[])&&Qe(this.rights||[],e.rights||[])}}function Qe(t,e){const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(Math.round(t[r])!==Math.round(e[r]))return!1;return!0}class Mn{getMaxScrollTop(){return this.getScrollHeight()-this.getClientHeight()}getMaxScrollLeft(){return this.getScrollWidth()-this.getClientWidth()}canScrollVertically(){return this.getMaxScrollTop()>0}canScrollHorizontally(){return this.getMaxScrollLeft()>0}canScrollUp(){return this.getScrollTop()>0}canScrollDown(){return this.getScrollTop()<this.getMaxScrollTop()}canScrollLeft(){return this.getScrollLeft()>0}canScrollRight(){return this.getScrollLeft()<this.getMaxScrollLeft()}}class Ba extends Mn{constructor(e){super(),this.el=e}getScrollTop(){return this.el.scrollTop}getScrollLeft(){return this.el.scrollLeft}setScrollTop(e){this.el.scrollTop=e}setScrollLeft(e){this.el.scrollLeft=e}getScrollWidth(){return this.el.scrollWidth}getScrollHeight(){return this.el.scrollHeight}getClientHeight(){return this.el.clientHeight}getClientWidth(){return this.el.clientWidth}}class La extends Mn{getScrollTop(){return window.scrollY}getScrollLeft(){return window.scrollX}setScrollTop(e){window.scroll(window.scrollX,e)}setScrollLeft(e){window.scroll(e,window.scrollY)}getScrollWidth(){return document.documentElement.scrollWidth}getScrollHeight(){return document.documentElement.scrollHeight}getClientHeight(){return document.documentElement.clientHeight}getClientWidth(){return document.documentElement.clientWidth}}class W extends _{constructor(){super(...arguments),this.uid=me()}prepareHits(){}queryHit(e,n,r,i){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!P(e,".fc-event-mirror")}isValidDateDownEl(e){return!P(e,".fc-event:not(.fc-bg-event)")&&!P(e,".fc-more-link")&&!P(e,"a[data-navlink]")&&!P(e,".fc-popover")}}class Wi{constructor(e=n=>n.thickness||1){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let n=[];for(let r of e)this.insertEntry(r,n);return n}insertEntry(e,n){let r=this.findInsertion(e);this.isInsertionValid(r,e)?this.insertEntryAt(e,r):this.handleInvalidInsertion(r,e,n)}isInsertionValid(e,n){return(this.maxCoord===-1||e.levelCoord+this.getEntryThickness(n)<=this.maxCoord)&&(this.maxStackCnt===-1||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,n,r){if(this.allowReslicing&&e.touchingEntry){const i=Object.assign(Object.assign({},n),{span:kn(n.span,e.touchingEntry.span)});r.push(i),this.splitEntry(n,e.touchingEntry,r)}else r.push(n)}splitEntry(e,n,r){let i=e.span,s=n.span;i.start<s.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.start,end:s.start}},r),i.end>s.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:s.end,end:i.end}},r)}insertEntryAt(e,n){let{entriesByLevel:r,levelCoords:i}=this;n.lateral===-1?(Lt(i,n.level,n.levelCoord),Lt(r,n.level,[e])):Lt(r[n.level],n.lateral,e),this.stackCnts[he(e)]=n.stackCnt}findInsertion(e){let{levelCoords:n,entriesByLevel:r,strictOrder:i,stackCnts:s}=this,l=n.length,o=0,a=-1,d=-1,c=null,g=0;for(let m=0;m<l;m+=1){const v=n[m];if(!i&&v>=o+this.getEntryThickness(e))break;let y=r[m],b,E=tn(y,e.span.start,en),D=E[0]+E[1];for(;(b=y[D])&&b.span.start<e.span.end;){let w=v+this.getEntryThickness(b);w>o&&(o=w,c=b,a=m,d=D),w===o&&(g=Math.max(g,s[he(b)]+1)),D+=1}}let h=0;if(c)for(h=a+1;h<l&&n[h]<o;)h+=1;let u=-1;return h<l&&n[h]===o&&(u=tn(r[h],e.span.end,en)[0]),{touchingLevel:a,touchingLateral:d,touchingEntry:c,stackCnt:g,levelCoord:o,level:h,lateral:u}}toRects(){let{entriesByLevel:e,levelCoords:n}=this,r=e.length,i=[];for(let s=0;s<r;s+=1){let l=e[s],o=n[s];for(let a of l)i.push(Object.assign(Object.assign({},a),{thickness:this.getEntryThickness(a),levelCoord:o}))}return i}}function en(t){return t.span.end}function he(t){return t.index+":"+t.span.start}function Ua(t){let e=[];for(let n of t){let r=[],i={span:n.span,entries:[n]};for(let s of e)kn(s.span,i.span)?i={entries:s.entries.concat(i.entries),span:ja(s.span,i.span)}:r.push(s);r.push(i),e=r}return e}function ja(t,e){return{start:Math.min(t.start,e.start),end:Math.max(t.end,e.end)}}function kn(t,e){let n=Math.max(t.start,e.start),r=Math.min(t.end,e.end);return n<r?{start:n,end:r}:null}function Lt(t,e,n){t.splice(e,0,n)}function tn(t,e,n){let r=0,i=t.length;if(!i||e<n(t[r]))return[0,0];if(e>n(t[i-1]))return[i,0];for(;r<i;){let s=Math.floor(r+(i-r)/2),l=n(t[s]);if(e<l)i=s;else if(e>l)r=s+1;else return[s,1]}return[r,0]}class Fa{constructor(e,n){this.emitter=new At}destroy(){}setMirrorIsVisible(e){}setMirrorNeedsRevert(e){}setAutoScrollEnabled(e){}}const Nn={};function za(t,e){return!t||e>10?M({weekday:"short"}):e>1?M({weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}):M({weekday:"long"})}const Vi="fc-col-header-cell";function Gi(t){return t.text}class Wa extends _{render(){let{dateEnv:e,options:n,theme:r,viewApi:i}=this.context,{props:s}=this,{date:l,dateProfile:o}=s,a=xn(l,s.todayRange,null,o),d=[Vi].concat(Dt(a,r)),c=e.format(l,s.dayHeaderFormat),g=!a.isDisabled&&s.colCnt>1?Ce(this.context,l):{},h=Object.assign(Object.assign(Object.assign({date:e.toDate(l),view:i},s.extraRenderProps),{text:c}),a);return f(B,{elTag:"th",elClasses:d,elAttrs:Object.assign({role:"columnheader",colSpan:s.colSpan,"data-date":a.isDisabled?void 0:Ue(l)},s.extraDataAttrs),renderProps:h,generatorName:"dayHeaderContent",customGenerator:n.dayHeaderContent,defaultGenerator:Gi,classNameGenerator:n.dayHeaderClassNames,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},u=>f("div",{className:"fc-scrollgrid-sync-inner"},!a.isDisabled&&f(u,{elTag:"a",elAttrs:g,elClasses:["fc-col-header-cell-cushion",s.isSticky&&"fc-sticky"]})))}}const Va=M({weekday:"long"});class Ga extends _{render(){let{props:e}=this,{dateEnv:n,theme:r,viewApi:i,options:s}=this.context,l=H(new Date(2592e5),e.dow),o={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},a=n.format(l,e.dayHeaderFormat),d=Object.assign(Object.assign(Object.assign(Object.assign({date:l},o),{view:i}),e.extraRenderProps),{text:a});return f(B,{elTag:"th",elClasses:[Vi,...Dt(o,r),...e.extraClassNames||[]],elAttrs:Object.assign({role:"columnheader",colSpan:e.colSpan},e.extraDataAttrs),renderProps:d,generatorName:"dayHeaderContent",customGenerator:s.dayHeaderContent,defaultGenerator:Gi,classNameGenerator:s.dayHeaderClassNames,didMount:s.dayHeaderDidMount,willUnmount:s.dayHeaderWillUnmount},c=>f("div",{className:"fc-scrollgrid-sync-inner"},f(c,{elTag:"a",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"],elAttrs:{"aria-label":n.format(l,Va)}})))}}class Te extends j{constructor(e,n){super(e,n),this.initialNowDate=Fe(n.options.now,n.dateEnv),this.initialNowQueriedMs=new Date().valueOf(),this.state=this.computeTiming().currentState}render(){let{props:e,state:n}=this;return e.children(n.nowDate,n.todayRange)}componentDidMount(){this.setTimeout()}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout()}computeTiming(){let{props:e,context:n}=this,r=se(this.initialNowDate,new Date().valueOf()-this.initialNowQueriedMs),i=n.dateEnv.startOf(r,e.unit),s=n.dateEnv.add(i,C(1,e.unit)),l=s.valueOf()-r.valueOf();return l=Math.min(1e3*60*60*24,l),{currentState:{nowDate:i,todayRange:_r(i)},nextState:{nowDate:s,todayRange:_r(s)},waitMs:l}}setTimeout(){let{nextState:e,waitMs:n}=this.computeTiming();this.timeoutId=setTimeout(()=>{this.setState(e,()=>{this.setTimeout()})},n)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}Te.contextType=J;function _r(t){let e=I(t),n=H(e,1);return{start:e,end:n}}class qi extends _{constructor(){super(...arguments),this.createDayHeaderFormatter=S(qa)}render(){let{context:e}=this,{dates:n,dateProfile:r,datesRepDistinctDays:i,renderIntro:s}=this.props,l=this.createDayHeaderFormatter(e.options.dayHeaderFormat,i,n.length);return f(Te,{unit:"day"},(o,a)=>f("tr",{role:"row"},s&&s("day"),n.map(d=>i?f(Wa,{key:d.toISOString(),date:d,dateProfile:r,todayRange:a,colCnt:n.length,dayHeaderFormat:l}):f(Ga,{key:d.getUTCDay(),dow:d.getUTCDay(),dayHeaderFormat:l}))))}}function qa(t,e,n){return t||za(e,n)}class Qi{constructor(e,n){let r=e.start,{end:i}=e,s=[],l=[],o=-1;for(;r<i;)n.isHiddenDay(r)?s.push(o+.5):(o+=1,s.push(o),l.push(r)),r=H(r,1);this.dates=l,this.indices=s,this.cnt=l.length}sliceRange(e){let n=this.getDateDayIndex(e.start),r=this.getDateDayIndex(H(e.end,-1)),i=Math.max(0,n),s=Math.min(this.cnt-1,r);return i=Math.ceil(i),s=Math.floor(s),i<=s?{firstIndex:i,lastIndex:s,isStart:n===i,isEnd:r===s}:null}getDateDayIndex(e){let{indices:n}=this,r=Math.floor(oe(this.dates[0],e));return r<0?n[0]-1:r>=n.length?n[n.length-1]+1:n[r]}}class Yi{constructor(e,n){let{dates:r}=e,i,s,l;if(n){for(s=r[0].getUTCDay(),i=1;i<r.length&&r[i].getUTCDay()!==s;i+=1);l=Math.ceil(r.length/i)}else l=1,i=r.length;this.rowCnt=l,this.colCnt=i,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}buildCells(){let e=[];for(let n=0;n<this.rowCnt;n+=1){let r=[];for(let i=0;i<this.colCnt;i+=1)r.push(this.buildCell(n,i));e.push(r)}return e}buildCell(e,n){let r=this.daySeries.dates[e*this.colCnt+n];return{key:r.toISOString(),date:r}}buildHeaderDates(){let e=[];for(let n=0;n<this.colCnt;n+=1)e.push(this.cells[0][n].date);return e}sliceRange(e){let{colCnt:n}=this,r=this.daySeries.sliceRange(e),i=[];if(r){let{firstIndex:s,lastIndex:l}=r,o=s;for(;o<=l;){let a=Math.floor(o/n),d=Math.min((a+1)*n,l+1);i.push({row:a,firstCol:o%n,lastCol:(d-1)%n,isStart:r.isStart&&o===s,isEnd:r.isEnd&&d-1===l}),o=d}}return i}}class Zi{constructor(){this.sliceBusinessHours=S(this._sliceBusinessHours),this.sliceDateSelection=S(this._sliceDateSpan),this.sliceEventStore=S(this._sliceEventStore),this.sliceEventDrag=S(this._sliceInteraction),this.sliceEventResize=S(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,n,r,i,...s){let{eventUiBases:l}=e,o=this.sliceEventStore(e.eventStore,l,n,r,...s);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,n,r,l,i,...s),businessHourSegs:this.sliceBusinessHours(e.businessHours,n,r,i,...s),fgEventSegs:o.fg,bgEventSegs:o.bg,eventDrag:this.sliceEventDrag(e.eventDrag,l,n,r,...s),eventResize:this.sliceEventResize(e.eventResize,l,n,r,...s),eventSelection:e.eventSelection}}sliceNowDate(e,n,r,i,...s){return this._sliceDateSpan({range:{start:e,end:se(e,1)},allDay:!1},n,r,{},i,...s)}_sliceBusinessHours(e,n,r,i,...s){return e?this._sliceEventStore(pe(e,Ye(n,!!r),i),{},n,r,...s).bg:[]}_sliceEventStore(e,n,r,i,...s){if(e){let l=Jt(e,n,Ye(r,!!i),i);return{bg:this.sliceEventRanges(l.bg,s),fg:this.sliceEventRanges(l.fg,s)}}return{bg:[],fg:[]}}_sliceInteraction(e,n,r,i,...s){if(!e)return null;let l=Jt(e.mutatedEvents,n,Ye(r,!!i),i);return{segs:this.sliceEventRanges(l.fg,s),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,n,r,i,s,...l){if(!e)return[];let o=Ye(n,!!r),a=le(e.range,o);if(a){e=Object.assign(Object.assign({},e),{range:a});let d=pa(e,i,s),c=this.sliceRange(e.range,...l);for(let g of c)g.eventRange=d;return c}return[]}sliceEventRanges(e,n){let r=[];for(let i of e)r.push(...this.sliceEventRange(i,n));return r}sliceEventRange(e,n){let r=e.range;this.forceDayIfListItem&&e.ui.display==="list-item"&&(r={start:r.start,end:H(r.start,1)});let i=this.sliceRange(r,...n);for(let s of i)s.eventRange=e,s.isStart=e.isStart&&s.isStart,s.isEnd=e.isEnd&&s.isEnd;return i}}function Ye(t,e){let n=t.activeRange;return e?n:{start:se(n.start,t.slotMinTime.milliseconds),end:se(n.end,t.slotMaxTime.milliseconds-864e5)}}function $i(t,e,n){let{instances:r}=t.mutatedEvents;for(let i in r)if(!Et(e.validRange,r[i].range))return!1;return Xi({eventDrag:t},n)}function Qa(t,e,n){return Et(e.validRange,t.range)?Xi({dateSelection:t},n):!1}function Xi(t,e){let n=e.getCurrentData(),r=Object.assign({businessHours:n.businessHours,dateSelection:"",eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:"",eventDrag:null,eventResize:null},t);return(e.pluginHooks.isPropsValid||Ya)(r,e)}function Ya(t,e,n={},r){return!(t.eventDrag&&!Za(t,e,n,r)||t.dateSelection&&!$a(t,e,n,r))}function Za(t,e,n,r){let i=e.getCurrentData(),s=t.eventDrag,l=s.mutatedEvents,o=l.defs,a=l.instances,d=gt(o,s.isEvent?t.eventUiBases:{"":i.selectionConfig});r&&(d=$(d,r));let c=Qo(t.eventStore,s.affectedEvents.instances),g=c.defs,h=c.instances,u=gt(g,t.eventUiBases);for(let m in a){let v=a[m],y=v.range,b=d[v.defId],E=o[v.defId];if(!Ji(b.constraints,y,c,t.businessHours,e))return!1;let{eventOverlap:D}=e.options,w=typeof D=="function"?D:null;for(let T in h){let k=h[T];if(yn(y,k.range)&&(u[k.defId].overlap===!1&&s.isEvent||b.overlap===!1||w&&!w(new N(e,g[k.defId],k),new N(e,E,v))))return!1}let O=i.eventStore;for(let T of b.allows){let k=Object.assign(Object.assign({},n),{range:v.range,allDay:E.allDay}),R=O.defs[E.defId],K=O.instances[m],xe;if(R?xe=new N(e,R,K):xe=new N(e,E),!T(wn(k,e),xe))return!1}}return!0}function $a(t,e,n,r){let i=t.eventStore,s=i.defs,l=i.instances,o=t.dateSelection,a=o.range,{selectionConfig:d}=e.getCurrentData();if(r&&(d=r(d)),!Ji(d.constraints,a,i,t.businessHours,e))return!1;let{selectOverlap:c}=e.options,g=typeof c=="function"?c:null;for(let h in l){let u=l[h];if(yn(a,u.range)&&(d.overlap===!1||g&&!g(new N(e,s[u.defId],u),null)))return!1}for(let h of d.allows){let u=Object.assign(Object.assign({},n),o);if(!h(wn(u,e),null))return!1}return!0}function Ji(t,e,n,r,i){for(let s of t)if(!Ja(Xa(s,e,n,r,i),e))return!1;return!0}function Xa(t,e,n,r,i){return t==="businessHours"?Ut(pe(r,e,i)):typeof t=="string"?Ut(St(n,s=>s.groupId===t)):typeof t=="object"&&t?Ut(pe(t,e,i)):[]}function Ut(t){let{instances:e}=t,n=[];for(let r in e)n.push(e[r].range);return n}function Ja(t,e){for(let n of t)if(Et(n,e))return!0;return!1}const Ze=/^(visible|hidden)$/;class Ki extends _{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,X(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:n,liquidIsAbsolute:r}=e,i=n&&r,s=["fc-scroller"];return n&&(r?s.push("fc-scroller-liquid-absolute"):s.push("fc-scroller-liquid")),f("div",{ref:this.handleEl,className:s.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:i&&-(e.overcomeLeft||0)||"",right:i&&-(e.overcomeRight||0)||"",bottom:i&&-(e.overcomeBottom||0)||"",marginLeft:!i&&-(e.overcomeLeft||0)||"",marginRight:!i&&-(e.overcomeRight||0)||"",marginBottom:!i&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(Ze.test(this.props.overflowX))return!1;let{el:e}=this,n=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:r}=e;for(let i=0;i<r.length;i+=1)if(r[i].getBoundingClientRect().width>n)return!0;return!1}needsYScrolling(){if(Ze.test(this.props.overflowY))return!1;let{el:e}=this,n=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:r}=e;for(let i=0;i<r.length;i+=1)if(r[i].getBoundingClientRect().height>n)return!0;return!1}getXScrollbarWidth(){return Ze.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return Ze.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class Q{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(n,r)=>{let{depths:i,currentMap:s}=this,l=!1,o=!1;n!==null?(l=r in s,s[r]=n,i[r]=(i[r]||0)+1,o=!0):(i[r]-=1,i[r]||(delete s[r],delete this.callbackMap[r],l=!0)),this.masterCallback&&(l&&this.masterCallback(null,String(r)),o&&this.masterCallback(n,String(r)))}}createRef(e){let n=this.callbackMap[e];return n||(n=this.callbackMap[e]=r=>{this.handleValue(r,String(e))}),n}collect(e,n,r){return ao(this.currentMap,e,n,r)}getAll(){return mn(this.currentMap)}}function Ka(t){let e=ol(t,".fc-scrollgrid-shrink"),n=0;for(let r of e)n=Math.max(n,Sl(r));return Math.ceil(n)}function es(t,e){return t.liquid&&e.liquid}function ec(t,e){return e.maxHeight!=null||es(t,e)}function tc(t,e,n,r){let{expandRows:i}=n;return typeof e.content=="function"?e.content(n):f("table",{role:"presentation",className:[e.tableClassName,t.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:i?n.clientHeight:""}},n.tableColGroupNode,f(r?"thead":"tbody",{role:"presentation"},typeof e.rowContent=="function"?e.rowContent(n):e.rowContent))}function nc(t,e){return ie(t,e,G)}function rc(t,e){let n=[];for(let r of t){let i=r.span||1;for(let s=0;s<i;s+=1)n.push(f("col",{style:{width:r.width==="shrink"?ic(e):r.width||"",minWidth:r.minWidth||""}}))}return f("colgroup",{},...n)}function ic(t){return t??4}function sc(t){for(let e of t)if(e.width==="shrink")return!0;return!1}function lc(t,e){let n=["fc-scrollgrid",e.theme.getClass("table")];return t&&n.push("fc-scrollgrid-liquid"),n}function oc(t,e){let n=["fc-scrollgrid-section",`fc-scrollgrid-section-${t.type}`,t.className];return e&&t.liquid&&t.maxHeight==null&&n.push("fc-scrollgrid-section-liquid"),t.isSticky&&n.push("fc-scrollgrid-section-sticky"),n}function nn(t){return f("div",{className:"fc-scrollgrid-sticky-shim",style:{width:t.clientWidth,minWidth:t.tableMinWidth}})}function pt(t){let{stickyHeaderDates:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}function ts(t){let{stickyFooterScrollbar:e}=t;return(e==null||e==="auto")&&(e=t.height==="auto"||t.viewHeight==="auto"),e}class On extends _{constructor(){super(...arguments),this.processCols=S(e=>e,nc),this.renderMicroColGroup=S(rc),this.scrollerRefs=new Q,this.scrollerElRefs=new Q(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){let{props:e,state:n,context:r}=this,i=e.sections||[],s=this.processCols(e.cols),l=this.renderMicroColGroup(s,n.shrinkWidth),o=lc(e.liquid,r);e.collapsibleWidth&&o.push("fc-scrollgrid-collapsible");let a=i.length,d=0,c,g=[],h=[],u=[];for(;d<a&&(c=i[d]).type==="header";)g.push(this.renderSection(c,l,!0)),d+=1;for(;d<a&&(c=i[d]).type==="body";)h.push(this.renderSection(c,l,!1)),d+=1;for(;d<a&&(c=i[d]).type==="footer";)u.push(this.renderSection(c,l,!0)),d+=1;let m=!Ui();const v={role:"rowgroup"};return f("table",{role:"grid",className:o.join(" "),style:{height:e.height}},!!(!m&&g.length)&&f("thead",v,...g),!!(!m&&h.length)&&f("tbody",v,...h),!!(!m&&u.length)&&f("tfoot",v,...u),m&&f("tbody",v,...g,...h,...u))}renderSection(e,n,r){return"outerContent"in e?f(x,{key:e.key},e.outerContent):f("tr",{key:e.key,role:"presentation",className:oc(e,this.props.liquid).join(" ")},this.renderChunkTd(e,n,e.chunk,r))}renderChunkTd(e,n,r,i){if("outerContent"in r)return r.outerContent;let{props:s}=this,{forceYScrollbars:l,scrollerClientWidths:o,scrollerClientHeights:a}=this.state,d=ec(s,e),c=es(s,e),g=s.liquid?l?"scroll":d?"auto":"hidden":"visible",h=e.key,u=tc(e,r,{tableColGroupNode:n,tableMinWidth:"",clientWidth:!s.collapsibleWidth&&o[h]!==void 0?o[h]:null,clientHeight:a[h]!==void 0?a[h]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},i);return f(i?"th":"td",{ref:r.elRef,role:"presentation"},f("div",{className:`fc-scroller-harness${c?" fc-scroller-harness-liquid":""}`},f(Ki,{ref:this.scrollerRefs.createRef(h),elRef:this.scrollerElRefs.createRef(h),overflowY:g,overflowX:s.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:c,liquidIsAbsolute:!0},u)))}_handleScrollerEl(e,n){let r=ac(this.props.sections,n);r&&X(r.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return sc(this.props.cols)?Ka(this.scrollerElRefs.getAll()):0}computeScrollerDims(){let e=ka(),{scrollerRefs:n,scrollerElRefs:r}=this,i=!1,s={},l={};for(let o in n.currentMap){let a=n.currentMap[o];if(a&&a.needsYScrolling()){i=!0;break}}for(let o of this.props.sections){let a=o.key,d=r.currentMap[a];if(d){let c=d.parentNode;s[a]=Math.floor(c.getBoundingClientRect().width-(i?e.y:0)),l[a]=Math.floor(c.getBoundingClientRect().height)}}return{forceYScrollbars:i,scrollerClientWidths:s,scrollerClientHeights:l}}}On.addStateEquality({scrollerClientWidths:G,scrollerClientHeights:G});function ac(t,e){for(let n of t)if(n.key===e)return n;return null}class wt extends _{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e&&Cr(e,this.props.seg)}}render(){const{props:e,context:n}=this,{options:r}=n,{seg:i}=e,{eventRange:s}=i,{ui:l}=s,o={event:new N(n,s.def,s.instance),view:n.viewApi,timeText:e.timeText,textColor:l.textColor,backgroundColor:l.backgroundColor,borderColor:l.borderColor,isDraggable:!e.disableDragging&&sa(i,n),isStartResizable:!e.disableResizing&&la(i,n),isEndResizable:!e.disableResizing&&oa(i),isMirror:!!(e.isDragging||e.isResizing||e.isDateSelecting),isStart:!!i.isStart,isEnd:!!i.isEnd,isPast:!!e.isPast,isFuture:!!e.isFuture,isToday:!!e.isToday,isSelected:!!e.isSelected,isDragging:!!e.isDragging,isResizing:!!e.isResizing};return f(B,Object.assign({},e,{elRef:this.handleEl,elClasses:[...aa(o),...i.eventRange.ui.classNames,...e.elClasses||[]],renderProps:o,generatorName:"eventContent",customGenerator:r.eventContent,defaultGenerator:e.defaultGenerator,classNameGenerator:r.eventClassNames,didMount:r.eventDidMount,willUnmount:r.eventWillUnmount}))}componentDidUpdate(e){this.el&&this.props.seg!==e.seg&&Cr(this.el,this.props.seg)}}class ns extends _{render(){let{props:e,context:n}=this,{options:r}=n,{seg:i}=e,{ui:s}=i.eventRange,l=r.eventTimeFormat||e.defaultTimeFormat,o=Oe(i,l,n,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return f(wt,Object.assign({},e,{elTag:"a",elStyle:{borderColor:s.borderColor,backgroundColor:s.backgroundColor},elAttrs:_n(i,n),defaultGenerator:cc,timeText:o}),(a,d)=>f(x,null,f(a,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:d.textColor}}),!!d.isStartResizable&&f("div",{className:"fc-event-resizer fc-event-resizer-start"}),!!d.isEndResizable&&f("div",{className:"fc-event-resizer fc-event-resizer-end"})))}}function cc(t){return f("div",{className:"fc-event-main-frame"},t.timeText&&f("div",{className:"fc-event-time"},t.timeText),f("div",{className:"fc-event-title-container"},f("div",{className:"fc-event-title fc-sticky"},t.event.title||f(x,null," "))))}const Hn=t=>f(J.Consumer,null,e=>{let{options:n}=e,r={isAxis:t.isAxis,date:e.dateEnv.toDate(t.date),view:e.viewApi};return f(B,Object.assign({},t,{elTag:t.elTag||"div",renderProps:r,generatorName:"nowIndicatorContent",customGenerator:n.nowIndicatorContent,classNameGenerator:n.nowIndicatorClassNames,didMount:n.nowIndicatorDidMount,willUnmount:n.nowIndicatorWillUnmount}))}),dc=M({day:"numeric"});class Pn extends _{constructor(){super(...arguments),this.refineRenderProps=tt(uc)}render(){let{props:e,context:n}=this,{options:r}=n,i=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:n.viewApi,dateEnv:n.dateEnv,monthStartFormat:r.monthStartFormat});return f(B,Object.assign({},e,{elClasses:[...Dt(i,n.theme),...e.elClasses||[]],elAttrs:Object.assign(Object.assign({},e.elAttrs),i.isDisabled?{}:{"data-date":Ue(e.date)}),renderProps:i,generatorName:"dayCellContent",customGenerator:r.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:i.isDisabled?void 0:r.dayCellClassNames,didMount:r.dayCellDidMount,willUnmount:r.dayCellWillUnmount}))}}function Bn(t){return!!(t.dayCellContent||Zt("dayCellContent",t))}function uc(t){let{date:e,dateEnv:n,dateProfile:r,isMonthStart:i}=t,s=xn(e,t.todayRange,null,r),l=t.showDayNumber?n.format(e,i?t.monthStartFormat:dc):"";return Object.assign(Object.assign(Object.assign({date:n.toDate(e),view:t.viewApi},s),{isMonthStart:i,dayNumberText:l}),t.extraRenderProps)}class rs extends _{render(){let{props:e}=this,{seg:n}=e;return f(wt,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:n.eventRange.ui.backgroundColor},defaultGenerator:fc,seg:n,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}}function fc(t){let{title:e}=t.event;return e&&f("div",{className:"fc-event-title"},t.event.title)}function is(t){return f("div",{className:`fc-${t}`})}const ss=t=>f(J.Consumer,null,e=>{let{dateEnv:n,options:r}=e,{date:i}=t,s=r.weekNumberFormat||t.defaultFormat,l=n.computeWeekNumber(i),o=n.format(i,s);return f(B,Object.assign({},t,{renderProps:{num:l,text:o,date:i},generatorName:"weekNumberContent",customGenerator:r.weekNumberContent,defaultGenerator:hc,classNameGenerator:r.weekNumberClassNames,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount}))});function hc(t){return t.text}const jt=10;class gc extends _{constructor(){super(...arguments),this.state={titleId:re()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&X(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{const n=ui(e);this.rootEl.contains(n)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{e.key==="Escape"&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:n}=this.context,{props:r,state:i}=this,s=["fc-popover",e.getClass("popover")].concat(r.extraClassNames||[]);return Qs(f("div",Object.assign({},r.extraAttrs,{id:r.id,className:s.join(" "),"aria-labelledby":i.titleId,ref:this.handleRootEl}),f("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},f("span",{className:"fc-popover-title",id:i.titleId},r.title),f("span",{className:"fc-popover-close "+e.getIconClass("close"),title:n.closeHint,onClick:this.handleCloseClick})),f("div",{className:"fc-popover-body "+e.getClass("popoverContent")},r.children)),r.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:n,alignGridTop:r}=this.props,{rootEl:i}=this,s=Pa(n);if(s){let l=i.getBoundingClientRect(),o=r?P(n,".fc-scrollgrid").getBoundingClientRect().top:s.top,a=e?s.right-l.width:s.left;o=Math.max(o,jt),a=Math.min(a,document.documentElement.clientWidth-jt-l.width),a=Math.max(a,jt);let d=i.offsetParent.getBoundingClientRect();Me(i,{top:o-d.top,left:a-d.left})}}}class pc extends W{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:n}=this.context,{props:r}=this,{startDate:i,todayRange:s,dateProfile:l}=r,o=n.format(i,e.dayPopoverFormat);return f(Pn,{elRef:this.handleRootEl,date:i,dateProfile:l,todayRange:s},(a,d,c)=>f(gc,{elRef:c.ref,id:r.id,title:o,extraClassNames:["fc-more-popover"].concat(c.className||[]),extraAttrs:c,parentEl:r.parentEl,alignmentEl:r.alignmentEl,alignGridTop:r.alignGridTop,onClose:r.onClose},Bn(e)&&f(a,{elTag:"div",elClasses:["fc-more-popover-misc"]}),r.children))}queryHit(e,n,r,i){let{rootEl:s,props:l}=this;return e>=0&&e<r&&n>=0&&n<i?{dateProfile:l.dateProfile,dateSpan:Object.assign({allDay:!l.forceTimed,range:{start:l.startDate,end:l.endDate}},l.extraDateSpan),dayEl:s,rect:{left:0,top:0,right:r,bottom:i},layer:1}:null}}class ls extends _{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:re()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&X(this.props.elRef,e)},this.handleClick=e=>{let{props:n,context:r}=this,{moreLinkClick:i}=r.options,s=Tr(n).start;function l(o){let{def:a,instance:d,range:c}=o.eventRange;return{event:new N(r,a,d),start:r.dateEnv.toDate(c.start),end:r.dateEnv.toDate(c.end),isStart:o.isStart,isEnd:o.isEnd}}typeof i=="function"&&(i=i({date:s,allDay:!!n.allDayDate,allSegs:n.allSegs.map(l),hiddenSegs:n.hiddenSegs.map(l),jsEvent:e,view:r.viewApi})),!i||i==="popover"?this.setState({isPopoverOpen:!0}):typeof i=="string"&&r.calendarApi.zoomTo(s,i)},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:n}=this;return f(J.Consumer,null,r=>{let{viewApi:i,options:s,calendarApi:l}=r,{moreLinkText:o}=s,{moreCnt:a}=e,d=Tr(e),c=typeof o=="function"?o.call(l,a):`+${a} ${o}`,g=ke(s.moreLinkHint,[a],c),h={num:a,shortText:`+${a}`,text:c,view:i};return f(x,null,!!e.moreCnt&&f(B,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),hi(this.handleClick)),{title:g,"aria-expanded":n.isPopoverOpen,"aria-controls":n.isPopoverOpen?n.popoverId:""}),renderProps:h,generatorName:"moreLinkContent",customGenerator:s.moreLinkContent,defaultGenerator:e.defaultGenerator||mc,classNameGenerator:s.moreLinkClassNames,didMount:s.moreLinkDidMount,willUnmount:s.moreLinkWillUnmount},e.children),n.isPopoverOpen&&f(pc,{id:n.popoverId,startDate:d.start,endDate:d.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))})}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=P(this.linkEl,".fc-view-harness"))}}function mc(t){return t.text}function Tr(t){if(t.allDayDate)return{start:t.allDayDate,end:H(t.allDayDate,1)};let{hiddenSegs:e}=t;return{start:os(e),end:yc(e)}}function os(t){return t.reduce(vc).eventRange.range.start}function vc(t,e){return t.eventRange.range.start<e.eventRange.range.start?t:e}function yc(t){return t.reduce(bc).eventRange.range.end}function bc(t,e){return t.eventRange.range.end>e.eventRange.range.end?t:e}const Ec=[],as={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},cs=Object.assign(Object.assign({},as),{buttonHints:{prev:"Previous $0",next:"Next $0",today(t,e){return e==="day"?"Today":`This ${t}`}},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint(t){return`Show ${t} more event${t===1?"":"s"}`}});function Sc(t){let e=t.length>0?t[0].code:"en",n=Ec.concat(t),r={en:cs};for(let i of n)r[i.code]=i;return{map:r,defaultCode:e}}function ds(t,e){return typeof t=="object"&&!Array.isArray(t)?us(t.code,[t.code],t):Ac(t,e)}function Ac(t,e){let n=[].concat(t||[]),r=Dc(n,e)||cs;return us(t,n,r)}function Dc(t,e){for(let n=0;n<t.length;n+=1){let r=t[n].toLocaleLowerCase().split("-");for(let i=r.length;i>0;i-=1){let s=r.slice(0,i).join("-");if(e[s])return e[s]}}return null}function us(t,e,n){let r=pn([as,n],["buttonText"]);delete r.code;let{week:i}=r;return delete r.week,{codeArg:t,codes:e,week:i,simpleNumberFormat:new Intl.NumberFormat(t),options:r}}function q(t){return{id:me(),name:t.name,premiumReleaseDate:t.premiumReleaseDate?new Date(t.premiumReleaseDate):void 0,deps:t.deps||[],reducers:t.reducers||[],isLoadingFuncs:t.isLoadingFuncs||[],contextInit:[].concat(t.contextInit||[]),eventRefiners:t.eventRefiners||{},eventDefMemberAdders:t.eventDefMemberAdders||[],eventSourceRefiners:t.eventSourceRefiners||{},isDraggableTransformers:t.isDraggableTransformers||[],eventDragMutationMassagers:t.eventDragMutationMassagers||[],eventDefMutationAppliers:t.eventDefMutationAppliers||[],dateSelectionTransformers:t.dateSelectionTransformers||[],datePointTransforms:t.datePointTransforms||[],dateSpanTransforms:t.dateSpanTransforms||[],views:t.views||{},viewPropsTransformers:t.viewPropsTransformers||[],isPropsValid:t.isPropsValid||null,externalDefTransforms:t.externalDefTransforms||[],viewContainerAppends:t.viewContainerAppends||[],eventDropTransformers:t.eventDropTransformers||[],componentInteractions:t.componentInteractions||[],calendarInteractions:t.calendarInteractions||[],themeClasses:t.themeClasses||{},eventSourceDefs:t.eventSourceDefs||[],cmdFormatter:t.cmdFormatter,recurringTypes:t.recurringTypes||[],namedTimeZonedImpl:t.namedTimeZonedImpl,initialView:t.initialView||"",elementDraggingImpl:t.elementDraggingImpl,optionChangeHandlers:t.optionChangeHandlers||{},scrollGridImpl:t.scrollGridImpl||null,listenerRefiners:t.listenerRefiners||{},optionRefiners:t.optionRefiners||{},propSetHandlers:t.propSetHandlers||{}}}function wc(t,e){let n={},r={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(s){for(let l of s){const o=l.name,a=n[o];a===void 0?(n[o]=l.id,i(l.deps),r=Rc(r,l)):a!==l.id&&console.warn(`Duplicate plugin '${o}'`)}}return t&&i(t),i(e),r}function Cc(){let t=[],e=[],n;return(r,i)=>((!n||!ie(r,t)||!ie(i,e))&&(n=wc(r,i)),t=r,e=i,n)}function Rc(t,e){return{premiumReleaseDate:_c(t.premiumReleaseDate,e.premiumReleaseDate),reducers:t.reducers.concat(e.reducers),isLoadingFuncs:t.isLoadingFuncs.concat(e.isLoadingFuncs),contextInit:t.contextInit.concat(e.contextInit),eventRefiners:Object.assign(Object.assign({},t.eventRefiners),e.eventRefiners),eventDefMemberAdders:t.eventDefMemberAdders.concat(e.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},t.eventSourceRefiners),e.eventSourceRefiners),isDraggableTransformers:t.isDraggableTransformers.concat(e.isDraggableTransformers),eventDragMutationMassagers:t.eventDragMutationMassagers.concat(e.eventDragMutationMassagers),eventDefMutationAppliers:t.eventDefMutationAppliers.concat(e.eventDefMutationAppliers),dateSelectionTransformers:t.dateSelectionTransformers.concat(e.dateSelectionTransformers),datePointTransforms:t.datePointTransforms.concat(e.datePointTransforms),dateSpanTransforms:t.dateSpanTransforms.concat(e.dateSpanTransforms),views:Object.assign(Object.assign({},t.views),e.views),viewPropsTransformers:t.viewPropsTransformers.concat(e.viewPropsTransformers),isPropsValid:e.isPropsValid||t.isPropsValid,externalDefTransforms:t.externalDefTransforms.concat(e.externalDefTransforms),viewContainerAppends:t.viewContainerAppends.concat(e.viewContainerAppends),eventDropTransformers:t.eventDropTransformers.concat(e.eventDropTransformers),calendarInteractions:t.calendarInteractions.concat(e.calendarInteractions),componentInteractions:t.componentInteractions.concat(e.componentInteractions),themeClasses:Object.assign(Object.assign({},t.themeClasses),e.themeClasses),eventSourceDefs:t.eventSourceDefs.concat(e.eventSourceDefs),cmdFormatter:e.cmdFormatter||t.cmdFormatter,recurringTypes:t.recurringTypes.concat(e.recurringTypes),namedTimeZonedImpl:e.namedTimeZonedImpl||t.namedTimeZonedImpl,initialView:t.initialView||e.initialView,elementDraggingImpl:t.elementDraggingImpl||e.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},t.optionChangeHandlers),e.optionChangeHandlers),scrollGridImpl:e.scrollGridImpl||t.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},t.listenerRefiners),e.listenerRefiners),optionRefiners:Object.assign(Object.assign({},t.optionRefiners),e.optionRefiners),propSetHandlers:Object.assign(Object.assign({},t.propSetHandlers),e.propSetHandlers)}}function _c(t,e){return t===void 0?e:e===void 0?t:new Date(Math.max(t.valueOf(),e.valueOf()))}class ae extends je{}ae.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"};ae.prototype.baseIconClass="fc-icon";ae.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"};ae.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"};ae.prototype.iconOverrideOption="buttonIcons";ae.prototype.iconOverrideCustomButtonOption="icon";ae.prototype.iconOverridePrefix="fc-icon-";function Tc(t,e){let n={},r;for(r in t)rn(r,n,t,e);for(r in e)rn(r,n,t,e);return n}function rn(t,e,n,r){if(e[t])return e[t];let i=xc(t,e,n,r);return i&&(e[t]=i),i}function xc(t,e,n,r){let i=n[t],s=r[t],l=c=>i&&i[c]!==null?i[c]:s&&s[c]!==null?s[c]:null,o=l("component"),a=l("superType"),d=null;if(a){if(a===t)throw new Error("Can't have a custom view type that references itself");d=rn(a,e,n,r)}return!o&&d&&(o=d.component),o?{type:t,component:o,defaults:Object.assign(Object.assign({},d?d.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},d?d.overrides:{}),s?s.rawOptions:{})}:null}function xr(t){return $(t,Ic)}function Ic(t){let e=typeof t=="function"?{component:t}:t,{component:n}=e;return e.content?n=Ir(e):n&&!(n.prototype instanceof _)&&(n=Ir(Object.assign(Object.assign({},e),{content:n}))),{superType:e.type,component:n,rawOptions:e}}function Ir(t){return e=>f(J.Consumer,null,n=>f(B,{elTag:"div",elClasses:Ai(n.viewSpec),renderProps:Object.assign(Object.assign({},e),{nextDayThreshold:n.options.nextDayThreshold}),generatorName:void 0,customGenerator:t.content,classNameGenerator:t.classNames,didMount:t.didMount,willUnmount:t.willUnmount}))}function Mc(t,e,n,r){let i=xr(t),s=xr(e.views),l=Tc(i,s);return $(l,o=>kc(o,s,e,n,r))}function kc(t,e,n,r,i){let s=t.overrides.duration||t.defaults.duration||r.duration||n.duration,l=null,o="",a="",d={};if(s&&(l=Nc(s),l)){let h=Qt(l);o=h.unit,h.value===1&&(a=o,d=e[o]?e[o].rawOptions:{})}let c=h=>{let u=h.buttonText||{},m=t.defaults.buttonTextKey;return m!=null&&u[m]!=null?u[m]:u[t.type]!=null?u[t.type]:u[a]!=null?u[a]:null},g=h=>{let u=h.buttonHints||{},m=t.defaults.buttonTextKey;return m!=null&&u[m]!=null?u[m]:u[t.type]!=null?u[t.type]:u[a]!=null?u[a]:null};return{type:t.type,component:t.component,duration:l,durationUnit:o,singleUnit:a,optionDefaults:t.defaults,optionOverrides:Object.assign(Object.assign({},d),t.overrides),buttonTextOverride:c(r)||c(n)||t.overrides.buttonText,buttonTextDefault:c(i)||t.defaults.buttonText||c(Ne)||t.type,buttonTitleOverride:g(r)||g(n)||t.overrides.buttonHint,buttonTitleDefault:g(i)||t.defaults.buttonHint||g(Ne)}}let Mr={};function Nc(t){let e=JSON.stringify(t),n=Mr[e];return n===void 0&&(n=C(t),Mr[e]=n),n}function Oc(t,e){switch(e.type){case"CHANGE_VIEW_TYPE":t=e.viewType}return t}function Hc(t,e){switch(e.type){case"SET_OPTION":return Object.assign(Object.assign({},t),{[e.optionName]:e.rawOptionValue});default:return t}}function Pc(t,e,n,r){let i;switch(e.type){case"CHANGE_VIEW_TYPE":return r.build(e.dateMarker||n);case"CHANGE_DATE":return r.build(e.dateMarker);case"PREV":if(i=r.buildPrev(t,n),i.isValid)return i;break;case"NEXT":if(i=r.buildNext(t,n),i.isValid)return i;break}return t}function Bc(t,e,n){let r=e?e.activeRange:null;return hs({},Vc(t,n),r,n)}function Lc(t,e,n,r){let i=n?n.activeRange:null;switch(e.type){case"ADD_EVENT_SOURCES":return hs(t,e.sources,i,r);case"REMOVE_EVENT_SOURCE":return jc(t,e.sourceId);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":return n?gs(t,i,r):t;case"FETCH_EVENT_SOURCES":return Ln(t,e.sourceIds?vi(e.sourceIds):ps(t,r),i,e.isRefetch||!1,r);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":return Wc(t,e.sourceId,e.fetchId,e.fetchRange);case"REMOVE_ALL_EVENT_SOURCES":return{};default:return t}}function Uc(t,e,n){let r=e?e.activeRange:null;return Ln(t,ps(t,n),r,!0,n)}function fs(t){for(let e in t)if(t[e].isFetching)return!0;return!1}function hs(t,e,n,r){let i={};for(let s of e)i[s.sourceId]=s;return n&&(i=gs(i,n,r)),Object.assign(Object.assign({},t),i)}function jc(t,e){return ge(t,n=>n.sourceId!==e)}function gs(t,e,n){return Ln(t,ge(t,r=>Fc(r,e,n)),e,!1,n)}function Fc(t,e,n){return ms(t,n)?!n.options.lazyFetching||!t.fetchRange||t.isFetching||e.start<t.fetchRange.start||e.end>t.fetchRange.end:!t.latestFetchId}function Ln(t,e,n,r,i){let s={};for(let l in t){let o=t[l];e[l]?s[l]=zc(o,n,r,i):s[l]=o}return s}function zc(t,e,n,r){let{options:i,calendarApi:s}=r,l=r.pluginHooks.eventSourceDefs[t.sourceDefId],o=me();return l.fetch({eventSource:t,range:e,isRefetch:n,context:r},a=>{let{rawEvents:d}=a;i.eventSourceSuccess&&(d=i.eventSourceSuccess.call(s,d,a.response)||d),t.success&&(d=t.success.call(s,d,a.response)||d),r.dispatch({type:"RECEIVE_EVENTS",sourceId:t.sourceId,fetchId:o,fetchRange:e,rawEvents:d})},a=>{let d=!1;i.eventSourceFailure&&(i.eventSourceFailure.call(s,a),d=!0),t.failure&&(t.failure(a),d=!0),d||console.warn(a.message,a),r.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:t.sourceId,fetchId:o,fetchRange:e,error:a})}),Object.assign(Object.assign({},t),{isFetching:!0,latestFetchId:o})}function Wc(t,e,n,r){let i=t[e];return i&&n===i.latestFetchId?Object.assign(Object.assign({},t),{[e]:Object.assign(Object.assign({},i),{isFetching:!1,fetchRange:r})}):t}function ps(t,e){return ge(t,n=>ms(n,e))}function Vc(t,e){let n=Ii(e),r=[].concat(t.eventSources||[]),i=[];t.initialEvents&&r.unshift(t.initialEvents),t.events&&r.unshift(t.events);for(let s of r){let l=xi(s,e,n);l&&i.push(l)}return i}function ms(t,e){return!e.pluginHooks.eventSourceDefs[t.sourceDefId].ignoreRange}function Gc(t,e){switch(e.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return e.selection;default:return t}}function qc(t,e){switch(e.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return e.eventInstanceId;default:return t}}function Qc(t,e){let n;switch(e.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return n=e.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return t}}function Yc(t,e){let n;switch(e.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return n=e.state,{affectedEvents:n.affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return t}}function Zc(t,e,n,r,i){let s=t.headerToolbar?kr(t.headerToolbar,t,e,n,r,i):null,l=t.footerToolbar?kr(t.footerToolbar,t,e,n,r,i):null;return{header:s,footer:l}}function kr(t,e,n,r,i,s){let l={},o=[],a=!1;for(let d in t){let c=t[d],g=$c(c,e,n,r,i,s);l[d]=g.widgets,o.push(...g.viewsWithButtons),a=a||g.hasTitle}return{sectionWidgets:l,viewsWithButtons:o,hasTitle:a}}function $c(t,e,n,r,i,s){let l=e.direction==="rtl",o=e.customButtons||{},a=n.buttonText||{},d=e.buttonText||{},c=n.buttonHints||{},g=e.buttonHints||{},h=t?t.split(" "):[],u=[],m=!1;return{widgets:h.map(y=>y.split(",").map(b=>{if(b==="title")return m=!0,{buttonName:b};let E,D,w,O,T,k;if(E=o[b])w=R=>{E.click&&E.click.call(R.target,R,R.target)},(O=r.getCustomButtonIconClass(E))||(O=r.getIconClass(b,l))||(T=E.text),k=E.hint||E.text;else if(D=i[b]){u.push(b),w=()=>{s.changeView(b)},(T=D.buttonTextOverride)||(O=r.getIconClass(b,l))||(T=D.buttonTextDefault);let R=D.buttonTextOverride||D.buttonTextDefault;k=ke(D.buttonTitleOverride||D.buttonTitleDefault||e.viewHint,[R,b],R)}else if(s[b])if(w=()=>{s[b]()},(T=a[b])||(O=r.getIconClass(b,l))||(T=d[b]),b==="prevYear"||b==="nextYear"){let R=b==="prevYear"?"prev":"next";k=ke(c[R]||g[R],[d.year||"year","year"],d[b])}else k=R=>ke(c[b]||g[b],[d[R]||R,R],d[b]);return{buttonName:b,buttonClick:w,buttonIcon:O,buttonText:T,buttonHint:k}})),viewsWithButtons:u,hasTitle:m}}class Xc{constructor(e,n,r){this.type=e,this.getCurrentData=n,this.dateEnv=r}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}let Jc={ignoreRange:!0,parseMeta(t){return Array.isArray(t.events)?t.events:null},fetch(t,e){e({rawEvents:t.eventSource.meta})}};const Kc=q({name:"array-event-source",eventSourceDefs:[Jc]});let ed={parseMeta(t){return typeof t.events=="function"?t.events:null},fetch(t,e,n){const{dateEnv:r}=t.context,i=t.eventSource.meta;ma(i.bind(null,Bi(t.range,r)),s=>e({rawEvents:s}),n)}};const td=q({name:"func-event-source",eventSourceDefs:[ed]}),nd={method:String,extraParams:p,startParam:String,endParam:String,timeZoneParam:String};let rd={parseMeta(t){return t.url&&(t.format==="json"||!t.format)?{url:t.url,format:"json",method:(t.method||"GET").toUpperCase(),extraParams:t.extraParams,startParam:t.startParam,endParam:t.endParam,timeZoneParam:t.timeZoneParam}:null},fetch(t,e,n){const{meta:r}=t.eventSource,i=sd(r,t.range,t.context);va(r.method,r.url,i).then(([s,l])=>{e({rawEvents:s,response:l})},n)}};const id=q({name:"json-event-source",eventSourceRefiners:nd,eventSourceDefs:[rd]});function sd(t,e,n){let{dateEnv:r,options:i}=n,s,l,o,a,d={};return s=t.startParam,s==null&&(s=i.startParam),l=t.endParam,l==null&&(l=i.endParam),o=t.timeZoneParam,o==null&&(o=i.timeZoneParam),typeof t.extraParams=="function"?a=t.extraParams():a=t.extraParams||{},Object.assign(d,a),d[s]=r.formatIso(e.start),d[l]=r.formatIso(e.end),r.timeZone!=="local"&&(d[o]=r.timeZone),d}const ld={daysOfWeek:p,startTime:C,endTime:C,duration:C,startRecur:p,endRecur:p};let od={parse(t,e){if(t.daysOfWeek||t.startTime||t.endTime||t.startRecur||t.endRecur){let n={daysOfWeek:t.daysOfWeek||null,startTime:t.startTime||null,endTime:t.endTime||null,startRecur:t.startRecur?e.createMarker(t.startRecur):null,endRecur:t.endRecur?e.createMarker(t.endRecur):null},r;return t.duration&&(r=t.duration),!r&&t.startTime&&t.endTime&&(r=Cl(t.endTime,t.startTime)),{allDayGuess:!t.startTime&&!t.endTime,duration:r,typeData:n}}return null},expand(t,e,n){let r=le(e,{start:t.startRecur,end:t.endRecur});return r?cd(t.daysOfWeek,t.startTime,r,n):[]}};const ad=q({name:"simple-recurring-event",recurringTypes:[od],eventRefiners:ld});function cd(t,e,n,r){let i=t?vi(t):null,s=I(n.start),l=n.end,o=[];for(;s<l;){let a;(!i||i[s.getUTCDay()])&&(e?a=r.add(s,e):a=s,o.push(a)),s=H(s,1)}return o}const dd=q({name:"change-handler",optionChangeHandlers:{events(t,e){Nr([t],e)},eventSources:Nr}});function Nr(t,e){let n=mn(e.getCurrentData().eventSources);if(n.length===1&&t.length===1&&Array.isArray(n[0]._raw)&&Array.isArray(t[0])){e.dispatch({type:"RESET_RAW_EVENTS",sourceId:n[0].sourceId,rawEvents:t[0]});return}let r=[];for(let i of t){let s=!1;for(let l=0;l<n.length;l+=1)if(n[l]._raw===i){n.splice(l,1),s=!0;break}s||r.push(i)}for(let i of n)e.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:i.sourceId});for(let i of r)e.calendarApi.addEventSource(i)}function ud(t,e){e.emitter.trigger("datesSet",Object.assign(Object.assign({},Bi(t.activeRange,e.dateEnv)),{view:e.viewApi}))}function fd(t,e){let{emitter:n}=e;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",fe(t,e))}const hd=[Kc,td,id,ad,dd,q({name:"misc",isLoadingFuncs:[t=>fs(t.eventSources)],propSetHandlers:{dateProfile:ud,eventStore:fd}})];class gd{constructor(e,n){this.runTaskOption=e,this.drainedOption=n,this.queue=[],this.delayedRunner=new an(this.drain.bind(this))}request(e,n){this.queue.push(e),this.delayedRunner.request(n)}pause(e){this.delayedRunner.pause(e)}resume(e,n){this.delayedRunner.resume(e,n)}drain(){let{queue:e}=this;for(;e.length;){let n=[],r;for(;r=e.shift();)this.runTask(r),n.push(r);this.drained(n)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function pd(t,e,n){let r;return/^(year|month)$/.test(t.currentRangeUnit)?r=t.currentRange:r=t.activeRange,n.formatRange(r.start,r.end,M(e.titleFormat||md(t)),{isEndExclusive:t.isRangeAllDay,defaultSeparator:e.titleRangeSeparator})}function md(t){let{currentRangeUnit:e}=t;if(e==="year")return{year:"numeric"};if(e==="month")return{year:"numeric",month:"long"};let n=ot(t.currentRange.start,t.currentRange.end);return n!==null&&n>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}class vd{constructor(e){this.computeCurrentViewData=S(this._computeCurrentViewData),this.organizeRawLocales=S(Sc),this.buildLocale=S(ds),this.buildPluginHooks=Cc(),this.buildDateEnv=S(yd),this.buildTheme=S(bd),this.parseToolbars=S(Zc),this.buildViewSpecs=S(Mc),this.buildDateProfileGenerator=tt(Ed),this.buildViewApi=S(Sd),this.buildViewUiProps=tt(wd),this.buildEventUiBySource=S(Ad,G),this.buildEventUiBases=S(Dd),this.parseContextBusinessHours=tt(Cd),this.buildTitle=S(pd),this.emitter=new At,this.actionRunner=new gd(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=h=>{this.actionRunner.request(h)},this.props=e,this.actionRunner.pause();let n={},r=this.computeOptionsData(e.optionOverrides,n,e.calendarApi),i=r.calendarOptions.initialView||r.pluginHooks.initialView,s=this.computeCurrentViewData(i,r,e.optionOverrides,n);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(s.options);let l=Io(r.calendarOptions,r.dateEnv),o=s.dateProfileGenerator.build(l);Y(o.activeRange,l)||(l=o.currentRange.start);let a={dateEnv:r.dateEnv,options:r.calendarOptions,pluginHooks:r.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData};for(let h of r.pluginHooks.contextInit)h(a);let d=Bc(r.calendarOptions,o,a),c={dynamicOptionOverrides:n,currentViewType:i,currentDate:l,dateProfile:o,businessHours:this.parseContextBusinessHours(a),eventSources:d,eventUiBases:{},eventStore:F(),renderableEventStore:F(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(a).selectionConfig},g=Object.assign(Object.assign({},a),c);for(let h of r.pluginHooks.reducers)Object.assign(c,h(null,null,g));Ft(c,a)&&this.emitter.trigger("loading",!0),this.state=c,this.updateData(),this.actionRunner.resume()}resetOptions(e,n){let{props:r}=this;n===void 0?r.optionOverrides=e:(r.optionOverrides=Object.assign(Object.assign({},r.optionOverrides||{}),e),this.optionsForRefining.push(...n)),(n===void 0||n.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){let{props:n,state:r,emitter:i}=this,s=Hc(r.dynamicOptionOverrides,e),l=this.computeOptionsData(n.optionOverrides,s,n.calendarApi),o=Oc(r.currentViewType,e),a=this.computeCurrentViewData(o,l,n.optionOverrides,s);n.calendarApi.currentDataManager=this,i.setThisContext(n.calendarApi),i.setOptions(a.options);let d={dateEnv:l.dateEnv,options:l.calendarOptions,pluginHooks:l.pluginHooks,calendarApi:n.calendarApi,dispatch:this.dispatch,emitter:i,getCurrentData:this.getCurrentData},{currentDate:c,dateProfile:g}=r;this.data&&this.data.dateProfileGenerator!==a.dateProfileGenerator&&(g=a.dateProfileGenerator.build(c)),c=xo(c,e),g=Pc(g,e,c,a.dateProfileGenerator),(e.type==="PREV"||e.type==="NEXT"||!Y(g.currentRange,c))&&(c=g.currentRange.start);let h=Lc(r.eventSources,e,g,d),u=Wo(r.eventStore,e,h,g,d),v=fs(h)&&!a.options.progressiveEventRendering&&r.renderableEventStore||u,{eventUiSingleBase:y,selectionConfig:b}=this.buildViewUiProps(d),E=this.buildEventUiBySource(h),D=this.buildEventUiBases(v.defs,y,E),w={dynamicOptionOverrides:s,currentViewType:o,currentDate:c,dateProfile:g,eventSources:h,eventStore:u,renderableEventStore:v,selectionConfig:b,eventUiBases:D,businessHours:this.parseContextBusinessHours(d),dateSelection:Gc(r.dateSelection,e),eventSelection:qc(r.eventSelection,e),eventDrag:Qc(r.eventDrag,e),eventResize:Yc(r.eventResize,e)},O=Object.assign(Object.assign({},d),w);for(let R of l.pluginHooks.reducers)Object.assign(w,R(r,e,O));let T=Ft(r,d),k=Ft(w,d);!T&&k?i.trigger("loading",!0):T&&!k&&i.trigger("loading",!1),this.state=w,n.onAction&&n.onAction(e)}updateData(){let{props:e,state:n}=this,r=this.data,i=this.computeOptionsData(e.optionOverrides,n.dynamicOptionOverrides,e.calendarApi),s=this.computeCurrentViewData(n.currentViewType,i,e.optionOverrides,n.dynamicOptionOverrides),l=this.data=Object.assign(Object.assign(Object.assign({viewTitle:this.buildTitle(n.dateProfile,s.options,i.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},i),s),n),o=i.pluginHooks.optionChangeHandlers,a=r&&r.calendarOptions,d=i.calendarOptions;if(a&&a!==d){a.timeZone!==d.timeZone&&(n.eventSources=l.eventSources=Uc(l.eventSources,n.dateProfile,l),n.eventStore=l.eventStore=Dr(l.eventStore,r.dateEnv,l.dateEnv),n.renderableEventStore=l.renderableEventStore=Dr(l.renderableEventStore,r.dateEnv,l.dateEnv));for(let c in o)(this.optionsForHandling.indexOf(c)!==-1||a[c]!==d[c])&&o[c](d[c],l)}this.optionsForHandling=[],e.onData&&e.onData(l)}computeOptionsData(e,n,r){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&n===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:i,pluginHooks:s,localeDefaults:l,availableLocaleData:o,extra:a}=this.processRawCalendarOptions(e,n);Or(a);let d=this.buildDateEnv(i.timeZone,i.locale,i.weekNumberCalculation,i.firstDay,i.weekText,s,o,i.defaultRangeSeparator),c=this.buildViewSpecs(s.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,l),g=this.buildTheme(i,s),h=this.parseToolbars(i,this.stableOptionOverrides,g,c,r);return this.stableCalendarOptionsData={calendarOptions:i,pluginHooks:s,dateEnv:d,viewSpecs:c,theme:g,toolbarConfig:h,localeDefaults:l,availableRawLocales:o.map}}processRawCalendarOptions(e,n){let{locales:r,locale:i}=kt([Ne,e,n]),s=this.organizeRawLocales(r),l=s.map,o=this.buildLocale(i||s.defaultCode,l).options,a=this.buildPluginHooks(e.plugins||[],hd),d=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},vr),yr),br),a.listenerRefiners),a.optionRefiners),c={},g=kt([Ne,o,e,n]),h={},u=this.currentCalendarOptionsInput,m=this.currentCalendarOptionsRefined,v=!1;for(let y in g)this.optionsForRefining.indexOf(y)===-1&&(g[y]===u[y]||ce[y]&&y in u&&ce[y](u[y],g[y]))?h[y]=m[y]:d[y]?(h[y]=d[y](g[y]),v=!0):c[y]=u[y];return v&&(this.currentCalendarOptionsInput=g,this.currentCalendarOptionsRefined=h,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=n),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:a,availableLocaleData:s,localeDefaults:o,extra:c}}_computeCurrentViewData(e,n,r,i){let s=n.viewSpecs[e];if(!s)throw new Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:l,extra:o}=this.processRawViewOptions(s,n.pluginHooks,n.localeDefaults,r,i);Or(o);let a=this.buildDateProfileGenerator({dateProfileGeneratorClass:s.optionDefaults.dateProfileGeneratorClass,duration:s.duration,durationUnit:s.durationUnit,usesMinMaxTime:s.optionDefaults.usesMinMaxTime,dateEnv:n.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:l.slotMinTime,slotMaxTime:l.slotMaxTime,showNonCurrentDates:l.showNonCurrentDates,dayCount:l.dayCount,dateAlignment:l.dateAlignment,dateIncrement:l.dateIncrement,hiddenDays:l.hiddenDays,weekends:l.weekends,nowInput:l.now,validRangeInput:l.validRange,visibleRangeInput:l.visibleRange,fixedWeekCount:l.fixedWeekCount}),d=this.buildViewApi(e,this.getCurrentData,n.dateEnv);return{viewSpec:s,options:l,dateProfileGenerator:a,viewApi:d}}processRawViewOptions(e,n,r,i,s){let l=kt([Ne,e.optionDefaults,r,i,e.optionOverrides,s]),o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},vr),yr),br),io),n.listenerRefiners),n.optionRefiners),a={},d=this.currentViewOptionsInput,c=this.currentViewOptionsRefined,g=!1,h={};for(let u in l)l[u]===d[u]||ce[u]&&ce[u](l[u],d[u])?a[u]=c[u]:(l[u]===this.currentCalendarOptionsInput[u]||ce[u]&&ce[u](l[u],this.currentCalendarOptionsInput[u])?u in this.currentCalendarOptionsRefined&&(a[u]=this.currentCalendarOptionsRefined[u]):o[u]?a[u]=o[u](l[u]):h[u]=l[u],g=!0);return g&&(this.currentViewOptionsInput=l,this.currentViewOptionsRefined=a),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:h}}}function yd(t,e,n,r,i,s,l,o){let a=ds(e||l.defaultCode,l.map);return new po({calendarSystem:"gregory",timeZone:t,namedTimeZoneImpl:s.namedTimeZonedImpl,locale:a,weekNumberCalculation:n,firstDay:r,weekText:i,cmdFormatter:s.cmdFormatter,defaultSeparator:o})}function bd(t,e){let n=e.themeClasses[t.themeSystem]||ae;return new n(t)}function Ed(t){let e=t.dateProfileGeneratorClass||wi;return new e(t)}function Sd(t,e,n){return new Xc(t,e,n)}function Ad(t){return $(t,e=>e.ui)}function Dd(t,e,n){let r={"":e};for(let i in t){let s=t[i];s.sourceId&&n[s.sourceId]&&(r[i]=n[s.sourceId])}return r}function wd(t){let{options:e}=t;return{eventUiSingleBase:ht({display:e.eventDisplay,editable:e.editable,startEditable:e.eventStartEditable,durationEditable:e.eventDurationEditable,constraint:e.eventConstraint,overlap:typeof e.eventOverlap=="boolean"?e.eventOverlap:void 0,allow:e.eventAllow,backgroundColor:e.eventBackgroundColor,borderColor:e.eventBorderColor,textColor:e.eventTextColor,color:e.eventColor},t),selectionConfig:ht({constraint:e.selectConstraint,overlap:typeof e.selectOverlap=="boolean"?e.selectOverlap:void 0,allow:e.selectAllow},t)}}function Ft(t,e){for(let n of e.pluginHooks.isLoadingFuncs)if(n(t))return!0;return!1}function Cd(t){return Jo(t.options.businessHours,t)}function Or(t,e){for(let n in t)console.warn(`Unknown option '${n}'`)}class Rd extends _{render(){let e=this.props.widgetGroups.map(n=>this.renderWidgetGroup(n));return f("div",{className:"fc-toolbar-chunk"},...e)}renderWidgetGroup(e){let{props:n}=this,{theme:r}=this.context,i=[],s=!0;for(let l of e){let{buttonName:o,buttonClick:a,buttonText:d,buttonIcon:c,buttonHint:g}=l;if(o==="title")s=!1,i.push(f("h2",{className:"fc-toolbar-title",id:n.titleId},n.title));else{let h=o===n.activeButton,u=!n.isTodayEnabled&&o==="today"||!n.isPrevEnabled&&o==="prev"||!n.isNextEnabled&&o==="next",m=[`fc-${o}-button`,r.getClass("button")];h&&m.push(r.getClass("buttonActive")),i.push(f("button",{type:"button",title:typeof g=="function"?g(n.navUnit):g,disabled:u,"aria-pressed":h,className:m.join(" "),onClick:a},d||(c?f("span",{className:c,role:"img"}):"")))}}if(i.length>1){let l=s&&r.getClass("buttonGroup")||"";return f("div",{className:l},...i)}return i[0]}}class Hr extends _{render(){let{model:e,extraClassName:n}=this.props,r=!1,i,s,l=e.sectionWidgets,o=l.center;return l.left?(r=!0,i=l.left):i=l.start,l.right?(r=!0,s=l.right):s=l.end,f("div",{className:[n||"","fc-toolbar",r?"fc-toolbar-ltr":""].join(" ")},this.renderSection("start",i||[]),this.renderSection("center",o||[]),this.renderSection("end",s||[]))}renderSection(e,n){let{props:r}=this;return f(Rd,{key:e,widgetGroups:n,title:r.title,navUnit:r.navUnit,activeButton:r.activeButton,isTodayEnabled:r.isTodayEnabled,isPrevEnabled:r.isPrevEnabled,isNextEnabled:r.isNextEnabled,titleId:r.titleId})}}class _d extends _{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,X(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:n}=this,{aspectRatio:r}=e,i=["fc-view-harness",r||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],s="",l="";return r?n.availableWidth!==null?s=n.availableWidth/r:l=`${1/r*100}%`:s=e.height||"",f("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:i.join(" "),style:{height:s,paddingBottom:l}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class Td extends _e{constructor(e){super(e),this.handleSegClick=(n,r)=>{let{component:i}=this,{context:s}=i,l=we(r);if(l&&i.isValidSegDownEl(n.target)){let o=P(n.target,".fc-event-forced-url"),a=o?o.querySelector("a[href]").href:"";s.emitter.trigger("eventClick",{el:r,event:new N(i.context,l.eventRange.def,l.eventRange.instance),jsEvent:n,view:s.viewApi}),a&&!n.defaultPrevented&&(window.location.href=a)}},this.destroy=fi(e.el,"click",".fc-event",this.handleSegClick)}}class xd extends _e{constructor(e){super(e),this.handleEventElRemove=n=>{n===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(n,r)=>{we(r)&&(this.currentSegEl=r,this.triggerEvent("eventMouseEnter",n,r))},this.handleSegLeave=(n,r)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",n,r))},this.removeHoverListeners=dl(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,n,r){let{component:i}=this,{context:s}=i,l=we(r);(!n||i.isValidSegDownEl(n.target))&&s.emitter.trigger(e,{el:r,event:new N(s,l.eventRange.def,l.eventRange.instance),jsEvent:n,view:s.viewApi})}}class Id extends ve{constructor(){super(...arguments),this.buildViewContext=S(yo),this.buildViewPropTransformers=S(kd),this.buildToolbarProps=S(Md),this.headerRef=L(),this.footerRef=L(),this.interactionsStore={},this.state={viewLabelId:re()},this.registerInteractiveComponent=(e,n)=>{let r=Ea(e,n),l=[Td,xd].concat(this.props.pluginHooks.componentInteractions).map(o=>new o(r));this.interactionsStore[e.uid]=l,Kt[e.uid]=r},this.unregisterInteractiveComponent=e=>{let n=this.interactionsStore[e.uid];if(n){for(let r of n)r.destroy();delete this.interactionsStore[e.uid]}delete Kt[e.uid]},this.resizeRunner=new an(()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})}),this.handleWindowResize=e=>{let{options:n}=this.props;n.handleWindowResize&&e.target===window&&this.resizeRunner.request(n.windowResizeDelay)}}render(){let{props:e}=this,{toolbarConfig:n,options:r}=e,i=this.buildToolbarProps(e.viewSpec,e.dateProfile,e.dateProfileGenerator,e.currentDate,Fe(e.options.now,e.dateEnv),e.viewTitle),s=!1,l="",o;e.isHeightAuto||e.forPrint?l="":r.height!=null?s=!0:r.contentHeight!=null?l=r.contentHeight:o=Math.max(r.aspectRatio,.5);let a=this.buildViewContext(e.viewSpec,e.viewApi,e.options,e.dateProfileGenerator,e.dateEnv,e.theme,e.pluginHooks,e.dispatch,e.getCurrentData,e.emitter,e.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),d=n.header&&n.header.hasTitle?this.state.viewLabelId:void 0;return f(J.Provider,{value:a},n.header&&f(Hr,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:d},i)),f(_d,{liquid:s,height:l,aspectRatio:o,labeledById:d},this.renderView(e),this.buildAppendContent()),n.footer&&f(Hr,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},i)))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(r=>new r(e)),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:n}=e.pluginHooks;for(let r in n)n[r](e[r],e)}componentDidUpdate(e){let{props:n}=this,{propSetHandlers:r}=n.pluginHooks;for(let i in r)n[i]!==e[i]&&r[i](n[i],n)}componentWillUnmount(){window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear();for(let e of this.calendarInteractions)e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this,n=e.pluginHooks.viewContainerAppends.map(r=>r(e));return f(x,{},...n)}renderView(e){let{pluginHooks:n}=e,{viewSpec:r}=e,i={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint},s=this.buildViewPropTransformers(n.viewPropsTransformers);for(let o of s)Object.assign(i,o.transform(i,e));let l=r.component;return f(l,Object.assign({},i))}}function Md(t,e,n,r,i,s){let l=n.build(i,void 0,!1),o=n.buildPrev(e,r,!1),a=n.buildNext(e,r,!1);return{title:s,activeButton:t.type,navUnit:t.singleUnit,isTodayEnabled:l.isValid&&!Y(e.currentRange,i),isPrevEnabled:o.isValid,isNextEnabled:a.isValid}}function kd(t){return t.map(e=>new e)}class Nd extends Sa{constructor(e,n={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=r=>{switch(r.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=r=>{this.currentData=r,this.renderRunner.request(r.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:r}=this;ut(()=>{Pe(f(ba,{options:r.calendarOptions,theme:r.theme,emitter:r.emitter},(i,s,l,o)=>(this.setClassNames(i),this.setHeight(s),f(Si.Provider,{value:this.customContentRenderId},f(Id,Object.assign({isHeightAuto:l,forPrint:o},r))))),this.el)})}else this.isRendered&&(this.isRendered=!1,Pe(null,this.el),this.setClassNames([]),this.setHeight(""))},tl(e),this.el=e,this.renderRunner=new an(this.handleRenderRequest),new vd({optionOverrides:n,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){ut(()=>{super.updateSize()})}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,n){this.currentDataManager.resetOptions(e,n)}setClassNames(e){if(!ie(e,this.currentClassNames)){let{classList:n}=this.el;for(let r of this.currentClassNames)n.remove(r);for(let r of e)n.add(r);this.currentClassNames=e}}setHeight(e){di(this.el,"height",e)}}class Od extends W{constructor(){super(...arguments),this.headerElRef=L()}renderSimpleLayout(e,n){let{props:r,context:i}=this,s=[],l=pt(i.options);return e&&s.push({type:"header",key:"header",isSticky:l,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),s.push({type:"body",key:"body",liquid:!0,chunk:{content:n}}),f(Be,{elClasses:["fc-daygrid"],viewSpec:i.viewSpec},f(On,{liquid:!r.isHeightAuto&&!r.forPrint,collapsibleWidth:r.forPrint,cols:[],sections:s}))}renderHScrollLayout(e,n,r,i){let s=this.context.pluginHooks.scrollGridImpl;if(!s)throw new Error("No ScrollGrid implementation");let{props:l,context:o}=this,a=!l.forPrint&&pt(o.options),d=!l.forPrint&&ts(o.options),c=[];return e&&c.push({type:"header",key:"header",isSticky:a,chunks:[{key:"main",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),c.push({type:"body",key:"body",liquid:!0,chunks:[{key:"main",content:n}]}),d&&c.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"main",content:nn}]}),f(Be,{elClasses:["fc-daygrid"],viewSpec:o.viewSpec},f(s,{liquid:!l.isHeightAuto&&!l.forPrint,forPrint:l.forPrint,collapsibleWidth:l.forPrint,colGroups:[{cols:[{span:r,minWidth:i}]}],sections:c}))}}function $e(t,e){let n=[];for(let r=0;r<e;r+=1)n[r]=[];for(let r of t)n[r.row].push(r);return n}function Xe(t,e){let n=[];for(let r=0;r<e;r+=1)n[r]=[];for(let r of t)n[r.firstCol].push(r);return n}function Pr(t,e){let n=[];if(t){for(let r=0;r<e;r+=1)n[r]={affectedInstances:t.affectedInstances,isEvent:t.isEvent,segs:[]};for(let r of t.segs)n[r.row].segs.push(r)}else for(let r=0;r<e;r+=1)n[r]=null;return n}const vs=M({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function ys(t){let{display:e}=t.eventRange.ui;return e==="list-item"||e==="auto"&&!t.eventRange.def.allDay&&t.firstCol===t.lastCol&&t.isStart&&t.isEnd}class bs extends _{render(){let{props:e}=this;return f(ns,Object.assign({},e,{elClasses:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:vs,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))}}class Es extends _{render(){let{props:e,context:n}=this,{options:r}=n,{seg:i}=e,s=r.eventTimeFormat||vs,l=Oe(i,s,n,!0,e.defaultDisplayEventEnd);return f(wt,Object.assign({},e,{elTag:"a",elClasses:["fc-daygrid-event","fc-daygrid-dot-event"],elAttrs:_n(e.seg,n),defaultGenerator:Hd,timeText:l,isResizing:!1,isDateSelecting:!1}))}}function Hd(t){return f(x,null,f("div",{className:"fc-daygrid-event-dot",style:{borderColor:t.borderColor||t.backgroundColor}}),t.timeText&&f("div",{className:"fc-event-time"},t.timeText),f("div",{className:"fc-event-title"},t.event.title||f(x,null," ")))}class Pd extends _{constructor(){super(...arguments),this.compileSegs=S(Bd)}render(){let{props:e}=this,{allSegs:n,invisibleSegs:r}=this.compileSegs(e.singlePlacements);return f(ls,{elClasses:["fc-daygrid-more-link"],dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:n,hiddenSegs:r,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:()=>{let i=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return f(x,null,n.map(s=>{let l=s.eventRange.instance.instanceId;return f("div",{className:"fc-daygrid-event-harness",key:l,style:{visibility:i[l]?"hidden":""}},ys(s)?f(Es,Object.assign({seg:s,isDragging:!1,isSelected:l===e.eventSelection,defaultDisplayEventEnd:!1},Z(s,e.todayRange))):f(bs,Object.assign({seg:s,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:l===e.eventSelection,defaultDisplayEventEnd:!1},Z(s,e.todayRange))))}))}})}}function Bd(t){let e=[],n=[];for(let r of t)e.push(r.seg),r.isVisible||n.push(r.seg);return{allSegs:e,invisibleSegs:n}}const Ld=M({week:"narrow"});class Ud extends W{constructor(){super(...arguments),this.rootElRef=L(),this.state={dayNumberId:re()},this.handleRootEl=e=>{X(this.rootElRef,e),X(this.props.elRef,e)}}render(){let{context:e,props:n,state:r,rootElRef:i}=this,{options:s,dateEnv:l}=e,{date:o,dateProfile:a}=n;const d=n.showDayNumber&&Fd(o,a.currentRange,l);return f(Pn,{elTag:"td",elRef:this.handleRootEl,elClasses:["fc-daygrid-day",...n.extraClassNames||[]],elAttrs:Object.assign(Object.assign(Object.assign({},n.extraDataAttrs),n.showDayNumber?{"aria-labelledby":r.dayNumberId}:{}),{role:"gridcell"}),defaultGenerator:jd,date:o,dateProfile:a,todayRange:n.todayRange,showDayNumber:n.showDayNumber,isMonthStart:d,extraRenderProps:n.extraRenderProps},(c,g)=>f("div",{ref:n.innerElRef,className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",style:{minHeight:n.minHeight}},n.showWeekNumber&&f(ss,{elTag:"a",elClasses:["fc-daygrid-week-number"],elAttrs:Ce(e,o,"week"),date:o,defaultFormat:Ld}),!g.isDisabled&&(n.showDayNumber||Bn(s)||n.forceDayTop)?f("div",{className:"fc-daygrid-day-top"},f(c,{elTag:"a",elClasses:["fc-daygrid-day-number",d&&"fc-daygrid-month-start"],elAttrs:Object.assign(Object.assign({},Ce(e,o)),{id:r.dayNumberId})})):n.showDayNumber?f("div",{className:"fc-daygrid-day-top",style:{visibility:"hidden"}},f("a",{className:"fc-daygrid-day-number"}," ")):void 0,f("div",{className:"fc-daygrid-day-events",ref:n.fgContentElRef},n.fgContent,f("div",{className:"fc-daygrid-day-bottom",style:{marginTop:n.moreMarginTop}},f(Pd,{allDayDate:o,singlePlacements:n.singlePlacements,moreCnt:n.moreCnt,alignmentElRef:i,alignGridTop:!n.showDayNumber,extraDateSpan:n.extraDateSpan,dateProfile:n.dateProfile,eventSelection:n.eventSelection,eventDrag:n.eventDrag,eventResize:n.eventResize,todayRange:n.todayRange}))),f("div",{className:"fc-daygrid-day-bg"},n.bgContent)))}}function jd(t){return t.dayNumberText||f(x,null," ")}function Fd(t,e,n){const{start:r,end:i}=e,s=se(i,-1),l=n.getYear(r),o=n.getMonth(r),a=n.getYear(s),d=n.getMonth(s);return!(l===a&&o===d)&&(t.valueOf()===r.valueOf()||n.getDay(t)===1&&t.valueOf()<i.valueOf())}function Ss(t){return t.eventRange.instance.instanceId+":"+t.firstCol}function As(t){return Ss(t)+":"+t.lastCol}function zd(t,e,n,r,i,s,l){let o=new Gd(b=>{let E=t[b.index].eventRange.instance.instanceId+":"+b.span.start+":"+(b.span.end-1);return i[E]||1});o.allowReslicing=!0,o.strictOrder=r,e===!0||n===!0?(o.maxCoord=s,o.hiddenConsumes=!0):typeof e=="number"?o.maxStackCnt=e:typeof n=="number"&&(o.maxStackCnt=n,o.hiddenConsumes=!0);let a=[],d=[];for(let b=0;b<t.length;b+=1){let E=t[b],D=As(E);i[D]!=null?a.push({index:b,span:{start:E.firstCol,end:E.lastCol+1}}):d.push(E)}let c=o.addSegs(a),g=o.toRects(),{singleColPlacements:h,multiColPlacements:u,leftoverMargins:m}=Wd(g,t,l),v=[],y=[];for(let b of d){u[b.firstCol].push({seg:b,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let E=b.firstCol;E<=b.lastCol;E+=1)h[E].push({seg:Ae(b,E,E+1,l),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let b=0;b<l.length;b+=1)v.push(0);for(let b of c){let E=t[b.index],D=b.span;u[D.start].push({seg:Ae(E,D.start,D.end,l),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let w=D.start;w<D.end;w+=1)v[w]+=1,h[w].push({seg:Ae(E,w,w+1,l),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let b=0;b<l.length;b+=1)y.push(m[b]);return{singleColPlacements:h,multiColPlacements:u,moreCnts:v,moreMarginTops:y}}function Wd(t,e,n){let r=Vd(t,n.length),i=[],s=[],l=[];for(let o=0;o<n.length;o+=1){let a=r[o],d=[],c=0,g=0;for(let u of a){let m=e[u.index];d.push({seg:Ae(m,o,o+1,n),isVisible:!0,isAbsolute:!1,absoluteTop:u.levelCoord,marginTop:u.levelCoord-c}),c=u.levelCoord+u.thickness}let h=[];c=0,g=0;for(let u of a){let m=e[u.index],v=u.span.end-u.span.start>1,y=u.span.start===o;g+=u.levelCoord-c,c=u.levelCoord+u.thickness,v?(g+=u.thickness,y&&h.push({seg:Ae(m,u.span.start,u.span.end,n),isVisible:!0,isAbsolute:!0,absoluteTop:u.levelCoord,marginTop:0})):y&&(h.push({seg:Ae(m,u.span.start,u.span.end,n),isVisible:!0,isAbsolute:!1,absoluteTop:u.levelCoord,marginTop:g}),g=0)}i.push(d),s.push(h),l.push(g)}return{singleColPlacements:i,multiColPlacements:s,leftoverMargins:l}}function Vd(t,e){let n=[];for(let r=0;r<e;r+=1)n.push([]);for(let r of t)for(let i=r.span.start;i<r.span.end;i+=1)n[i].push(r);return n}function Ae(t,e,n,r){if(t.firstCol===e&&t.lastCol===n-1)return t;let i=t.eventRange,s=i.range,l=le(s,{start:r[e].date,end:H(r[n-1].date,1)});return Object.assign(Object.assign({},t),{firstCol:e,lastCol:n-1,eventRange:{def:i.def,ui:Object.assign(Object.assign({},i.ui),{durationEditable:!1}),instance:i.instance,range:l},isStart:t.isStart&&l.start.valueOf()===s.start.valueOf(),isEnd:t.isEnd&&l.end.valueOf()===s.end.valueOf()})}class Gd extends Wi{constructor(){super(...arguments),this.hiddenConsumes=!1,this.forceHidden={}}addSegs(e){const n=super.addSegs(e),{entriesByLevel:r}=this,i=s=>!this.forceHidden[he(s)];for(let s=0;s<r.length;s+=1)r[s]=r[s].filter(i);return n}handleInvalidInsertion(e,n,r){const{entriesByLevel:i,forceHidden:s}=this,{touchingEntry:l,touchingLevel:o,touchingLateral:a}=e;if(this.hiddenConsumes&&l){const d=he(l);if(!s[d])if(this.allowReslicing){const c=Object.assign(Object.assign({},l),{span:kn(l.span,n.span)}),g=he(c);s[g]=!0,i[o][a]=c,r.push(c),this.splitEntry(l,n,r)}else s[d]=!0,r.push(l)}super.handleInvalidInsertion(e,n,r)}}class Ds extends W{constructor(){super(...arguments),this.cellElRefs=new Q,this.frameElRefs=new Q,this.fgElRefs=new Q,this.segHarnessRefs=new Q,this.rootElRef=L(),this.state={framePositions:null,maxContentHeight:null,segHeights:{}},this.handleResize=e=>{e&&this.updateSizing(!0)}}render(){let{props:e,state:n,context:r}=this,{options:i}=r,s=e.cells.length,l=Xe(e.businessHourSegs,s),o=Xe(e.bgEventSegs,s),a=Xe(this.getHighlightSegs(),s),d=Xe(this.getMirrorSegs(),s),{singleColPlacements:c,multiColPlacements:g,moreCnts:h,moreMarginTops:u}=zd(Rn(e.fgEventSegs,i.eventOrder),e.dayMaxEvents,e.dayMaxEventRows,i.eventOrderStrict,n.segHeights,n.maxContentHeight,e.cells),m=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{};return f("tr",{ref:this.rootElRef,role:"row"},e.renderIntro&&e.renderIntro(),e.cells.map((v,y)=>{let b=this.renderFgSegs(y,e.forPrint?c[y]:g[y],e.todayRange,m),E=this.renderFgSegs(y,qd(d[y],g),e.todayRange,{},!!e.eventDrag,!!e.eventResize,!1);return f(Ud,{key:v.key,elRef:this.cellElRefs.createRef(v.key),innerElRef:this.frameElRefs.createRef(v.key),dateProfile:e.dateProfile,date:v.date,showDayNumber:e.showDayNumbers,showWeekNumber:e.showWeekNumbers&&y===0,forceDayTop:e.showWeekNumbers,todayRange:e.todayRange,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,extraRenderProps:v.extraRenderProps,extraDataAttrs:v.extraDataAttrs,extraClassNames:v.extraClassNames,extraDateSpan:v.extraDateSpan,moreCnt:h[y],moreMarginTop:u[y],singlePlacements:c[y],fgContentElRef:this.fgElRefs.createRef(v.key),fgContent:f(x,null,f(x,null,b),f(x,null,E)),bgContent:f(x,null,this.renderFillSegs(a[y],"highlight"),this.renderFillSegs(l[y],"non-business"),this.renderFillSegs(o[y],"bg-event")),minHeight:e.cellMinHeight})}))}componentDidMount(){this.updateSizing(!0),this.context.addResizeHandler(this.handleResize)}componentDidUpdate(e,n){let r=this.props;this.updateSizing(!G(e,r))}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}getHighlightSegs(){let{props:e}=this;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs}getMirrorSegs(){let{props:e}=this;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]}renderFgSegs(e,n,r,i,s,l,o){let{context:a}=this,{eventSelection:d}=this.props,{framePositions:c}=this.state,g=this.props.cells.length===1,h=s||l||o,u=[];if(c)for(let m of n){let{seg:v}=m,{instanceId:y}=v.eventRange.instance,b=m.isVisible&&!i[y],E=m.isAbsolute,D="",w="";E&&(a.isRtl?(w=0,D=c.lefts[v.lastCol]-c.lefts[v.firstCol]):(D=0,w=c.rights[v.firstCol]-c.rights[v.lastCol])),u.push(f("div",{className:"fc-daygrid-event-harness"+(E?" fc-daygrid-event-harness-abs":""),key:Ss(v),ref:h?null:this.segHarnessRefs.createRef(As(v)),style:{visibility:b?"":"hidden",marginTop:E?"":m.marginTop,top:E?m.absoluteTop:"",left:D,right:w}},ys(v)?f(Es,Object.assign({seg:v,isDragging:s,isSelected:y===d,defaultDisplayEventEnd:g},Z(v,r))):f(bs,Object.assign({seg:v,isDragging:s,isResizing:l,isDateSelecting:o,isSelected:y===d,defaultDisplayEventEnd:g},Z(v,r)))))}return u}renderFillSegs(e,n){let{isRtl:r}=this.context,{todayRange:i}=this.props,{framePositions:s}=this.state,l=[];if(s)for(let o of e){let a=r?{right:0,left:s.lefts[o.lastCol]-s.lefts[o.firstCol]}:{left:0,right:s.rights[o.firstCol]-s.rights[o.lastCol]};l.push(f("div",{key:Pi(o.eventRange),className:"fc-daygrid-bg-harness",style:a},n==="bg-event"?f(rs,Object.assign({seg:o},Z(o,i))):is(n)))}return f(x,{},...l)}updateSizing(e){let{props:n,state:r,frameElRefs:i}=this;if(!n.forPrint&&n.clientWidth!==null){if(e){let a=n.cells.map(d=>i.currentMap[d.key]);if(a.length){let d=this.rootElRef.current,c=new Re(d,a,!0,!1);(!r.framePositions||!r.framePositions.similarTo(c))&&this.setState({framePositions:new Re(d,a,!0,!1)})}}const s=this.state.segHeights,l=this.querySegHeights(),o=n.dayMaxEvents===!0||n.dayMaxEventRows===!0;this.safeSetState({segHeights:Object.assign(Object.assign({},s),l),maxContentHeight:o?this.computeMaxContentHeight():null})}}querySegHeights(){let e=this.segHarnessRefs.currentMap,n={};for(let r in e){let i=Math.round(e[r].getBoundingClientRect().height);n[r]=Math.max(n[r]||0,i)}return n}computeMaxContentHeight(){let e=this.props.cells[0].key,n=this.cellElRefs.currentMap[e],r=this.fgElRefs.currentMap[e];return n.getBoundingClientRect().bottom-r.getBoundingClientRect().top}getCellEls(){let e=this.cellElRefs.currentMap;return this.props.cells.map(n=>e[n.key])}}Ds.addStateEquality({segHeights:G});function qd(t,e){if(!t.length)return[];let n=Qd(e);return t.map(r=>({seg:r,isVisible:!0,isAbsolute:!0,absoluteTop:n[r.eventRange.instance.instanceId],marginTop:0}))}function Qd(t){let e={};for(let n of t)for(let r of n)e[r.seg.eventRange.instance.instanceId]=r.absoluteTop;return e}class Yd extends W{constructor(){super(...arguments),this.splitBusinessHourSegs=S($e),this.splitBgEventSegs=S($e),this.splitFgEventSegs=S($e),this.splitDateSelectionSegs=S($e),this.splitEventDrag=S(Pr),this.splitEventResize=S(Pr),this.rowRefs=new Q}render(){let{props:e,context:n}=this,r=e.cells.length,i=this.splitBusinessHourSegs(e.businessHourSegs,r),s=this.splitBgEventSegs(e.bgEventSegs,r),l=this.splitFgEventSegs(e.fgEventSegs,r),o=this.splitDateSelectionSegs(e.dateSelectionSegs,r),a=this.splitEventDrag(e.eventDrag,r),d=this.splitEventResize(e.eventResize,r),c=r>=7&&e.clientWidth?e.clientWidth/n.options.aspectRatio/6:null;return f(Te,{unit:"day"},(g,h)=>f(x,null,e.cells.map((u,m)=>f(Ds,{ref:this.rowRefs.createRef(m),key:u.length?u[0].date.toISOString():m,showDayNumbers:r>1,showWeekNumbers:e.showWeekNumbers,todayRange:h,dateProfile:e.dateProfile,cells:u,renderIntro:e.renderRowIntro,businessHourSegs:i[m],eventSelection:e.eventSelection,bgEventSegs:s[m].filter(Zd),fgEventSegs:l[m],dateSelectionSegs:o[m],eventDrag:a[m],eventResize:d[m],dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,clientWidth:e.clientWidth,clientHeight:e.clientHeight,cellMinHeight:c,forPrint:e.forPrint}))))}componentDidMount(){this.registerInteractiveComponent()}componentDidUpdate(){this.registerInteractiveComponent()}registerInteractiveComponent(){if(!this.rootEl){const e=this.rowRefs.currentMap[0].getCellEls()[0],n=e?e.closest(".fc-daygrid-body"):null;n&&(this.rootEl=n,this.context.registerInteractiveComponent(this,{el:n,isHitComboAllowed:this.props.isHitComboAllowed}))}}componentWillUnmount(){this.rootEl&&(this.context.unregisterInteractiveComponent(this),this.rootEl=null)}prepareHits(){this.rowPositions=new Re(this.rootEl,this.rowRefs.collect().map(e=>e.getCellEls()[0]),!1,!0),this.colPositions=new Re(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)}queryHit(e,n){let{colPositions:r,rowPositions:i}=this,s=r.leftToIndex(e),l=i.topToIndex(n);if(l!=null&&s!=null){let o=this.props.cells[l][s];return{dateProfile:this.props.dateProfile,dateSpan:Object.assign({range:this.getCellRange(l,s),allDay:!0},o.extraDateSpan),dayEl:this.getCellEl(l,s),rect:{left:r.lefts[s],right:r.rights[s],top:i.tops[l],bottom:i.bottoms[l]},layer:0}}return null}getCellEl(e,n){return this.rowRefs.currentMap[e].getCellEls()[n]}getCellRange(e,n){let r=this.props.cells[e][n].date,i=H(r,1);return{start:r,end:i}}}function Zd(t){return t.eventRange.def.allDay}class $d extends W{constructor(){super(...arguments),this.elRef=L(),this.needsScrollReset=!1}render(){let{props:e}=this,{dayMaxEventRows:n,dayMaxEvents:r,expandRows:i}=e,s=r===!0||n===!0;s&&!i&&(s=!1,n=null,r=null);let l=["fc-daygrid-body",s?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",i?"":"fc-daygrid-body-natural"];return f("div",{ref:this.elRef,className:l.join(" "),style:{width:e.clientWidth,minWidth:e.tableMinWidth}},f("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:e.clientWidth,minWidth:e.tableMinWidth,height:i?e.clientHeight:""}},e.colGroupNode,f("tbody",{role:"presentation"},f(Yd,{dateProfile:e.dateProfile,cells:e.cells,renderRowIntro:e.renderRowIntro,showWeekNumbers:e.showWeekNumbers,clientWidth:e.clientWidth,clientHeight:e.clientHeight,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,dayMaxEvents:r,dayMaxEventRows:n,forPrint:e.forPrint,isHitComboAllowed:e.isHitComboAllowed}))))}componentDidMount(){this.requestScrollReset()}componentDidUpdate(e){e.dateProfile!==this.props.dateProfile?this.requestScrollReset():this.flushScrollReset()}requestScrollReset(){this.needsScrollReset=!0,this.flushScrollReset()}flushScrollReset(){if(this.needsScrollReset&&this.props.clientWidth){const e=Xd(this.elRef.current,this.props.dateProfile);if(e){const n=e.closest(".fc-daygrid-body"),r=n.closest(".fc-scroller"),i=e.getBoundingClientRect().top-n.getBoundingClientRect().top;r.scrollTop=i?i+1:0}this.needsScrollReset=!1}}}function Xd(t,e){let n;return e.currentRangeUnit.match(/year|month/)&&(n=t.querySelector(`[data-date="${Fl(e.currentDate)}-01"]`)),n||(n=t.querySelector(`[data-date="${Ue(e.currentDate)}"]`)),n}class Jd extends Zi{constructor(){super(...arguments),this.forceDayIfListItem=!0}sliceRange(e,n){return n.sliceRange(e)}}class ws extends W{constructor(){super(...arguments),this.slicer=new Jd,this.tableRef=L()}render(){let{props:e,context:n}=this;return f($d,Object.assign({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,n,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))}}class Kd extends Od{constructor(){super(...arguments),this.buildDayTableModel=S(eu),this.headerRef=L(),this.tableRef=L()}render(){let{options:e,dateProfileGenerator:n}=this.context,{props:r}=this,i=this.buildDayTableModel(r.dateProfile,n),s=e.dayHeaders&&f(qi,{ref:this.headerRef,dateProfile:r.dateProfile,dates:i.headerDates,datesRepDistinctDays:i.rowCnt===1}),l=o=>f(ws,{ref:this.tableRef,dateProfile:r.dateProfile,dayTableModel:i,businessHours:r.businessHours,dateSelection:r.dateSelection,eventStore:r.eventStore,eventUiBases:r.eventUiBases,eventSelection:r.eventSelection,eventDrag:r.eventDrag,eventResize:r.eventResize,nextDayThreshold:e.nextDayThreshold,colGroupNode:o.tableColGroupNode,tableMinWidth:o.tableMinWidth,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.weekNumbers,expandRows:!r.isHeightAuto,headerAlignElRef:this.headerElRef,clientWidth:o.clientWidth,clientHeight:o.clientHeight,forPrint:r.forPrint});return e.dayMinWidth?this.renderHScrollLayout(s,l,i.colCnt,e.dayMinWidth):this.renderSimpleLayout(s,l)}}function eu(t,e){let n=new Qi(t.renderRange,e);return new Yi(n,/year|month|week/.test(t.currentRangeUnit))}class tu extends wi{buildRenderRange(e,n,r){let i=super.buildRenderRange(e,n,r),{props:s}=this;return nu({currentRange:i,snapToWeek:/^(year|month)$/.test(n),fixedWeekCount:s.fixedWeekCount,dateEnv:s.dateEnv})}}function nu(t){let{dateEnv:e,currentRange:n}=t,{start:r,end:i}=n,s;if(t.snapToWeek&&(r=e.startOfWeek(r),s=e.startOfWeek(i),s.valueOf()!==i.valueOf()&&(i=ur(s,1))),t.fixedWeekCount){let l=e.startOfWeek(e.startOfMonth(H(n.end,-1))),o=Math.ceil(Il(l,i));i=ur(i,6-o)}return{start:r,end:i}}var ru=':root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:"";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:"";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}';yt(ru);var iu=q({name:"@fullcalendar/daygrid",initialView:"dayGridMonth",views:{dayGrid:{component:Kd,dateProfileGeneratorClass:tu},dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},fixedWeekCount:!0},dayGridYear:{type:"dayGrid",duration:{years:1}}}});Nn.touchMouseIgnoreWait=500;let sn=0,mt=0,ln=!1;class Cs{constructor(e){this.subjectEl=null,this.selector="",this.handleSelector="",this.shouldIgnoreMove=!1,this.shouldWatchScroll=!0,this.isDragging=!1,this.isTouchDragging=!1,this.wasTouchScroll=!1,this.handleMouseDown=n=>{if(!this.shouldIgnoreMouse()&&su(n)&&this.tryStart(n)){let r=this.createEventFromMouse(n,!0);this.emitter.trigger("pointerdown",r),this.initScrollWatch(r),this.shouldIgnoreMove||document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseup",this.handleMouseUp)}},this.handleMouseMove=n=>{let r=this.createEventFromMouse(n);this.recordCoords(r),this.emitter.trigger("pointermove",r)},this.handleMouseUp=n=>{document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("mouseup",this.handleMouseUp),this.emitter.trigger("pointerup",this.createEventFromMouse(n)),this.cleanup()},this.handleTouchStart=n=>{if(this.tryStart(n)){this.isTouchDragging=!0;let r=this.createEventFromTouch(n,!0);this.emitter.trigger("pointerdown",r),this.initScrollWatch(r);let i=n.target;this.shouldIgnoreMove||i.addEventListener("touchmove",this.handleTouchMove),i.addEventListener("touchend",this.handleTouchEnd),i.addEventListener("touchcancel",this.handleTouchEnd),window.addEventListener("scroll",this.handleTouchScroll,!0)}},this.handleTouchMove=n=>{let r=this.createEventFromTouch(n);this.recordCoords(r),this.emitter.trigger("pointermove",r)},this.handleTouchEnd=n=>{if(this.isDragging){let r=n.target;r.removeEventListener("touchmove",this.handleTouchMove),r.removeEventListener("touchend",this.handleTouchEnd),r.removeEventListener("touchcancel",this.handleTouchEnd),window.removeEventListener("scroll",this.handleTouchScroll,!0),this.emitter.trigger("pointerup",this.createEventFromTouch(n)),this.cleanup(),this.isTouchDragging=!1,lu()}},this.handleTouchScroll=()=>{this.wasTouchScroll=!0},this.handleScroll=n=>{if(!this.shouldIgnoreMove){let r=window.scrollX-this.prevScrollX+this.prevPageX,i=window.scrollY-this.prevScrollY+this.prevPageY;this.emitter.trigger("pointermove",{origEvent:n,isTouch:this.isTouchDragging,subjectEl:this.subjectEl,pageX:r,pageY:i,deltaX:r-this.origPageX,deltaY:i-this.origPageY})}},this.containerEl=e,this.emitter=new At,e.addEventListener("mousedown",this.handleMouseDown),e.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),ou()}destroy(){this.containerEl.removeEventListener("mousedown",this.handleMouseDown),this.containerEl.removeEventListener("touchstart",this.handleTouchStart,{passive:!0}),au()}tryStart(e){let n=this.querySubjectEl(e),r=e.target;return n&&(!this.handleSelector||P(r,this.handleSelector))?(this.subjectEl=n,this.isDragging=!0,this.wasTouchScroll=!1,!0):!1}cleanup(){ln=!1,this.isDragging=!1,this.subjectEl=null,this.destroyScrollWatch()}querySubjectEl(e){return this.selector?P(e.target,this.selector):this.containerEl}shouldIgnoreMouse(){return sn||this.isTouchDragging}cancelTouchScroll(){this.isDragging&&(ln=!0)}initScrollWatch(e){this.shouldWatchScroll&&(this.recordCoords(e),window.addEventListener("scroll",this.handleScroll,!0))}recordCoords(e){this.shouldWatchScroll&&(this.prevPageX=e.pageX,this.prevPageY=e.pageY,this.prevScrollX=window.scrollX,this.prevScrollY=window.scrollY)}destroyScrollWatch(){this.shouldWatchScroll&&window.removeEventListener("scroll",this.handleScroll,!0)}createEventFromMouse(e,n){let r=0,i=0;return n?(this.origPageX=e.pageX,this.origPageY=e.pageY):(r=e.pageX-this.origPageX,i=e.pageY-this.origPageY),{origEvent:e,isTouch:!1,subjectEl:this.subjectEl,pageX:e.pageX,pageY:e.pageY,deltaX:r,deltaY:i}}createEventFromTouch(e,n){let r=e.touches,i,s,l=0,o=0;return r&&r.length?(i=r[0].pageX,s=r[0].pageY):(i=e.pageX,s=e.pageY),n?(this.origPageX=i,this.origPageY=s):(l=i-this.origPageX,o=s-this.origPageY),{origEvent:e,isTouch:!0,subjectEl:this.subjectEl,pageX:i,pageY:s,deltaX:l,deltaY:o}}}function su(t){return t.button===0&&!t.ctrlKey}function lu(){sn+=1,setTimeout(()=>{sn-=1},Nn.touchMouseIgnoreWait)}function ou(){mt+=1,mt===1&&window.addEventListener("touchmove",Rs,{passive:!1})}function au(){mt-=1,mt||window.removeEventListener("touchmove",Rs,{passive:!1})}function Rs(t){ln&&t.preventDefault()}class cu{constructor(){this.isVisible=!1,this.sourceEl=null,this.mirrorEl=null,this.sourceElRect=null,this.parentNode=document.body,this.zIndex=9999,this.revertDuration=0}start(e,n,r){this.sourceEl=e,this.sourceElRect=this.sourceEl.getBoundingClientRect(),this.origScreenX=n-window.scrollX,this.origScreenY=r-window.scrollY,this.deltaX=0,this.deltaY=0,this.updateElPosition()}handleMove(e,n){this.deltaX=e-window.scrollX-this.origScreenX,this.deltaY=n-window.scrollY-this.origScreenY,this.updateElPosition()}setIsVisible(e){e?this.isVisible||(this.mirrorEl&&(this.mirrorEl.style.display=""),this.isVisible=e,this.updateElPosition()):this.isVisible&&(this.mirrorEl&&(this.mirrorEl.style.display="none"),this.isVisible=e)}stop(e,n){let r=()=>{this.cleanup(),n()};e&&this.mirrorEl&&this.isVisible&&this.revertDuration&&(this.deltaX||this.deltaY)?this.doRevertAnimation(r,this.revertDuration):setTimeout(r,0)}doRevertAnimation(e,n){let r=this.mirrorEl,i=this.sourceEl.getBoundingClientRect();r.style.transition="top "+n+"ms,left "+n+"ms",Me(r,{left:i.left,top:i.top}),ul(r,()=>{r.style.transition="",e()})}cleanup(){this.mirrorEl&&(cn(this.mirrorEl),this.mirrorEl=null),this.sourceEl=null}updateElPosition(){this.sourceEl&&this.isVisible&&Me(this.getMirrorEl(),{left:this.sourceElRect.left+this.deltaX,top:this.sourceElRect.top+this.deltaY})}getMirrorEl(){let e=this.sourceElRect,n=this.mirrorEl;return n||(n=this.mirrorEl=this.sourceEl.cloneNode(!0),n.style.userSelect="none",n.style.webkitUserSelect="none",n.style.pointerEvents="none",n.classList.add("fc-event-dragging"),Me(n,{position:"fixed",zIndex:this.zIndex,visibility:"",boxSizing:"border-box",width:e.right-e.left,height:e.bottom-e.top,right:"auto",bottom:"auto",margin:0}),this.parentNode.appendChild(n)),n}}class _s extends Mn{constructor(e,n){super(),this.handleScroll=()=>{this.scrollTop=this.scrollController.getScrollTop(),this.scrollLeft=this.scrollController.getScrollLeft(),this.handleScrollChange()},this.scrollController=e,this.doesListening=n,this.scrollTop=this.origScrollTop=e.getScrollTop(),this.scrollLeft=this.origScrollLeft=e.getScrollLeft(),this.scrollWidth=e.getScrollWidth(),this.scrollHeight=e.getScrollHeight(),this.clientWidth=e.getClientWidth(),this.clientHeight=e.getClientHeight(),this.clientRect=this.computeClientRect(),this.doesListening&&this.getEventTarget().addEventListener("scroll",this.handleScroll)}destroy(){this.doesListening&&this.getEventTarget().removeEventListener("scroll",this.handleScroll)}getScrollTop(){return this.scrollTop}getScrollLeft(){return this.scrollLeft}setScrollTop(e){this.scrollController.setScrollTop(e),this.doesListening||(this.scrollTop=Math.max(Math.min(e,this.getMaxScrollTop()),0),this.handleScrollChange())}setScrollLeft(e){this.scrollController.setScrollLeft(e),this.doesListening||(this.scrollLeft=Math.max(Math.min(e,this.getMaxScrollLeft()),0),this.handleScrollChange())}getClientWidth(){return this.clientWidth}getClientHeight(){return this.clientHeight}getScrollWidth(){return this.scrollWidth}getScrollHeight(){return this.scrollHeight}handleScrollChange(){}}class Ts extends _s{constructor(e,n){super(new Ba(e),n)}getEventTarget(){return this.scrollController.el}computeClientRect(){return Ha(this.scrollController.el)}}class du extends _s{constructor(e){super(new La,e)}getEventTarget(){return window}computeClientRect(){return{left:this.scrollLeft,right:this.scrollLeft+this.clientWidth,top:this.scrollTop,bottom:this.scrollTop+this.clientHeight}}handleScrollChange(){this.clientRect=this.computeClientRect()}}const Br=typeof performance=="function"?performance.now:Date.now;class uu{constructor(){this.isEnabled=!0,this.scrollQuery=[window,".fc-scroller"],this.edgeThreshold=50,this.maxVelocity=300,this.pointerScreenX=null,this.pointerScreenY=null,this.isAnimating=!1,this.scrollCaches=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.animate=()=>{if(this.isAnimating){let e=this.computeBestEdge(this.pointerScreenX+window.scrollX,this.pointerScreenY+window.scrollY);if(e){let n=Br();this.handleSide(e,(n-this.msSinceRequest)/1e3),this.requestAnimation(n)}else this.isAnimating=!1}}}start(e,n,r){this.isEnabled&&(this.scrollCaches=this.buildCaches(r),this.pointerScreenX=null,this.pointerScreenY=null,this.everMovedUp=!1,this.everMovedDown=!1,this.everMovedLeft=!1,this.everMovedRight=!1,this.handleMove(e,n))}handleMove(e,n){if(this.isEnabled){let r=e-window.scrollX,i=n-window.scrollY,s=this.pointerScreenY===null?0:i-this.pointerScreenY,l=this.pointerScreenX===null?0:r-this.pointerScreenX;s<0?this.everMovedUp=!0:s>0&&(this.everMovedDown=!0),l<0?this.everMovedLeft=!0:l>0&&(this.everMovedRight=!0),this.pointerScreenX=r,this.pointerScreenY=i,this.isAnimating||(this.isAnimating=!0,this.requestAnimation(Br()))}}stop(){if(this.isEnabled){this.isAnimating=!1;for(let e of this.scrollCaches)e.destroy();this.scrollCaches=null}}requestAnimation(e){this.msSinceRequest=e,requestAnimationFrame(this.animate)}handleSide(e,n){let{scrollCache:r}=e,{edgeThreshold:i}=this,s=i-e.distance,l=s*s/(i*i)*this.maxVelocity*n,o=1;switch(e.name){case"left":o=-1;case"right":r.setScrollLeft(r.getScrollLeft()+l*o);break;case"top":o=-1;case"bottom":r.setScrollTop(r.getScrollTop()+l*o);break}}computeBestEdge(e,n){let{edgeThreshold:r}=this,i=null,s=this.scrollCaches||[];for(let l of s){let o=l.clientRect,a=e-o.left,d=o.right-e,c=n-o.top,g=o.bottom-n;a>=0&&d>=0&&c>=0&&g>=0&&(c<=r&&this.everMovedUp&&l.canScrollUp()&&(!i||i.distance>c)&&(i={scrollCache:l,name:"top",distance:c}),g<=r&&this.everMovedDown&&l.canScrollDown()&&(!i||i.distance>g)&&(i={scrollCache:l,name:"bottom",distance:g}),a<=r&&this.everMovedLeft&&l.canScrollLeft()&&(!i||i.distance>a)&&(i={scrollCache:l,name:"left",distance:a}),d<=r&&this.everMovedRight&&l.canScrollRight()&&(!i||i.distance>d)&&(i={scrollCache:l,name:"right",distance:d}))}return i}buildCaches(e){return this.queryScrollEls(e).map(n=>n===window?new du(!1):new Ts(n,!1))}queryScrollEls(e){let n=[];for(let r of this.scrollQuery)typeof r=="object"?n.push(r):n.push(...Array.prototype.slice.call(e.getRootNode().querySelectorAll(r)));return n}}class ze extends Fa{constructor(e,n){super(e),this.containerEl=e,this.delay=null,this.minDistance=0,this.touchScrollAllowed=!0,this.mirrorNeedsRevert=!1,this.isInteracting=!1,this.isDragging=!1,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,this.delayTimeoutId=null,this.onPointerDown=i=>{this.isDragging||(this.isInteracting=!0,this.isDelayEnded=!1,this.isDistanceSurpassed=!1,fl(document.body),gl(document.body),i.isTouch||i.origEvent.preventDefault(),this.emitter.trigger("pointerdown",i),this.isInteracting&&!this.pointer.shouldIgnoreMove&&(this.mirror.setIsVisible(!1),this.mirror.start(i.subjectEl,i.pageX,i.pageY),this.startDelay(i),this.minDistance||this.handleDistanceSurpassed(i)))},this.onPointerMove=i=>{if(this.isInteracting){if(this.emitter.trigger("pointermove",i),!this.isDistanceSurpassed){let s=this.minDistance,l,{deltaX:o,deltaY:a}=i;l=o*o+a*a,l>=s*s&&this.handleDistanceSurpassed(i)}this.isDragging&&(i.origEvent.type!=="scroll"&&(this.mirror.handleMove(i.pageX,i.pageY),this.autoScroller.handleMove(i.pageX,i.pageY)),this.emitter.trigger("dragmove",i))}},this.onPointerUp=i=>{this.isInteracting&&(this.isInteracting=!1,hl(document.body),pl(document.body),this.emitter.trigger("pointerup",i),this.isDragging&&(this.autoScroller.stop(),this.tryStopDrag(i)),this.delayTimeoutId&&(clearTimeout(this.delayTimeoutId),this.delayTimeoutId=null))};let r=this.pointer=new Cs(e);r.emitter.on("pointerdown",this.onPointerDown),r.emitter.on("pointermove",this.onPointerMove),r.emitter.on("pointerup",this.onPointerUp),n&&(r.selector=n),this.mirror=new cu,this.autoScroller=new uu}destroy(){this.pointer.destroy(),this.onPointerUp({})}startDelay(e){typeof this.delay=="number"?this.delayTimeoutId=setTimeout(()=>{this.delayTimeoutId=null,this.handleDelayEnd(e)},this.delay):this.handleDelayEnd(e)}handleDelayEnd(e){this.isDelayEnded=!0,this.tryStartDrag(e)}handleDistanceSurpassed(e){this.isDistanceSurpassed=!0,this.tryStartDrag(e)}tryStartDrag(e){this.isDelayEnded&&this.isDistanceSurpassed&&(!this.pointer.wasTouchScroll||this.touchScrollAllowed)&&(this.isDragging=!0,this.mirrorNeedsRevert=!1,this.autoScroller.start(e.pageX,e.pageY,this.containerEl),this.emitter.trigger("dragstart",e),this.touchScrollAllowed===!1&&this.pointer.cancelTouchScroll())}tryStopDrag(e){this.mirror.stop(this.mirrorNeedsRevert,this.stopDrag.bind(this,e))}stopDrag(e){this.isDragging=!1,this.emitter.trigger("dragend",e)}setIgnoreMove(e){this.pointer.shouldIgnoreMove=e}setMirrorIsVisible(e){this.mirror.setIsVisible(e)}setMirrorNeedsRevert(e){this.mirrorNeedsRevert=e}setAutoScrollEnabled(e){this.autoScroller.isEnabled=e}}class fu{constructor(e){this.el=e,this.origRect=In(e),this.scrollCaches=zi(e).map(n=>new Ts(n,!0))}destroy(){for(let e of this.scrollCaches)e.destroy()}computeLeft(){let e=this.origRect.left;for(let n of this.scrollCaches)e+=n.origScrollLeft-n.getScrollLeft();return e}computeTop(){let e=this.origRect.top;for(let n of this.scrollCaches)e+=n.origScrollTop-n.getScrollTop();return e}isWithinClipping(e,n){let r={left:e,top:n};for(let i of this.scrollCaches)if(!hu(i.getEventTarget())&&!Aa(r,i.clientRect))return!1;return!0}}function hu(t){let e=t.tagName;return e==="HTML"||e==="BODY"}class Ct{constructor(e,n){this.useSubjectCenter=!1,this.requireInitial=!0,this.disablePointCheck=!1,this.initialHit=null,this.movingHit=null,this.finalHit=null,this.handlePointerDown=r=>{let{dragging:i}=this;this.initialHit=null,this.movingHit=null,this.finalHit=null,this.prepareHits(),this.processFirstCoord(r),this.initialHit||!this.requireInitial?(i.setIgnoreMove(!1),this.emitter.trigger("pointerdown",r)):i.setIgnoreMove(!0)},this.handleDragStart=r=>{this.emitter.trigger("dragstart",r),this.handleMove(r,!0)},this.handleDragMove=r=>{this.emitter.trigger("dragmove",r),this.handleMove(r)},this.handlePointerUp=r=>{this.releaseHits(),this.emitter.trigger("pointerup",r)},this.handleDragEnd=r=>{this.movingHit&&this.emitter.trigger("hitupdate",null,!0,r),this.finalHit=this.movingHit,this.movingHit=null,this.emitter.trigger("dragend",r)},this.droppableStore=n,e.emitter.on("pointerdown",this.handlePointerDown),e.emitter.on("dragstart",this.handleDragStart),e.emitter.on("dragmove",this.handleDragMove),e.emitter.on("pointerup",this.handlePointerUp),e.emitter.on("dragend",this.handleDragEnd),this.dragging=e,this.emitter=new At}processFirstCoord(e){let n={left:e.pageX,top:e.pageY},r=n,i=e.subjectEl,s;i instanceof HTMLElement&&(s=In(i),r=Da(r,s));let l=this.initialHit=this.queryHitForOffset(r.left,r.top);if(l){if(this.useSubjectCenter&&s){let o=ji(s,l.rect);o&&(r=wa(o))}this.coordAdjust=Ca(r,n)}else this.coordAdjust={left:0,top:0}}handleMove(e,n){let r=this.queryHitForOffset(e.pageX+this.coordAdjust.left,e.pageY+this.coordAdjust.top);(n||!Rt(this.movingHit,r))&&(this.movingHit=r,this.emitter.trigger("hitupdate",r,!1,e))}prepareHits(){this.offsetTrackers=$(this.droppableStore,e=>(e.component.prepareHits(),new fu(e.el)))}releaseHits(){let{offsetTrackers:e}=this;for(let n in e)e[n].destroy();this.offsetTrackers={}}queryHitForOffset(e,n){let{droppableStore:r,offsetTrackers:i}=this,s=null;for(let l in r){let o=r[l].component,a=i[l];if(a&&a.isWithinClipping(e,n)){let d=a.computeLeft(),c=a.computeTop(),g=e-d,h=n-c,{origRect:u}=a,m=u.right-u.left,v=u.bottom-u.top;if(g>=0&&g<m&&h>=0&&h<v){let y=o.queryHit(g,h,m,v);y&&Et(y.dateProfile.activeRange,y.dateSpan.range)&&(this.disablePointCheck||a.el.contains(document.elementFromPoint(g+d-window.scrollX,h+c-window.scrollY)))&&(!s||y.layer>s.layer)&&(y.componentId=l,y.context=o.context,y.rect.left+=d,y.rect.right+=d,y.rect.top+=c,y.rect.bottom+=c,s=y)}}}return s}}function Rt(t,e){return!t&&!e?!0:!!t!=!!e?!1:fa(t.dateSpan,e.dateSpan)}function xs(t,e){let n={};for(let r of e.pluginHooks.datePointTransforms)Object.assign(n,r(t,e));return Object.assign(n,gu(t,e.dateEnv)),n}function gu(t,e){return{date:e.toDate(t.range.start),dateStr:e.formatIso(t.range.start,{omitTime:t.allDay}),allDay:t.allDay}}class pu extends _e{constructor(e){super(e),this.handlePointerDown=r=>{let{dragging:i}=this,s=r.origEvent.target;i.setIgnoreMove(!this.component.isValidDateDownEl(s))},this.handleDragEnd=r=>{let{component:i}=this,{pointer:s}=this.dragging;if(!s.wasTouchScroll){let{initialHit:l,finalHit:o}=this.hitDragging;if(l&&o&&Rt(l,o)){let{context:a}=i,d=Object.assign(Object.assign({},xs(l.dateSpan,a)),{dayEl:l.dayEl,jsEvent:r.origEvent,view:a.viewApi||a.calendarApi.view});a.emitter.trigger("dateClick",d)}}},this.dragging=new ze(e.el),this.dragging.autoScroller.isEnabled=!1;let n=this.hitDragging=new Ct(this.dragging,Tn(e));n.emitter.on("pointerdown",this.handlePointerDown),n.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}}class mu extends _e{constructor(e){super(e),this.dragSelection=null,this.handlePointerDown=l=>{let{component:o,dragging:a}=this,{options:d}=o.context,c=d.selectable&&o.isValidDateDownEl(l.origEvent.target);a.setIgnoreMove(!c),a.delay=l.isTouch?vu(o):null},this.handleDragStart=l=>{this.component.context.calendarApi.unselect(l)},this.handleHitUpdate=(l,o)=>{let{context:a}=this.component,d=null,c=!1;if(l){let g=this.hitDragging.initialHit;l.componentId===g.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(g,l)||(d=yu(g,l,a.pluginHooks.dateSelectionTransformers)),(!d||!Qa(d,l.dateProfile,a))&&(c=!0,d=null)}d?a.dispatch({type:"SELECT_DATES",selection:d}):o||a.dispatch({type:"UNSELECT_DATES"}),c?dn():un(),o||(this.dragSelection=d)},this.handlePointerUp=l=>{this.dragSelection&&(Ni(this.dragSelection,l,this.component.context),this.dragSelection=null)};let{component:n}=e,{options:r}=n.context,i=this.dragging=new ze(e.el);i.touchScrollAllowed=!1,i.minDistance=r.selectMinDistance||0,i.autoScroller.isEnabled=r.dragScroll;let s=this.hitDragging=new Ct(this.dragging,Tn(e));s.emitter.on("pointerdown",this.handlePointerDown),s.emitter.on("dragstart",this.handleDragStart),s.emitter.on("hitupdate",this.handleHitUpdate),s.emitter.on("pointerup",this.handlePointerUp)}destroy(){this.dragging.destroy()}}function vu(t){let{options:e}=t.context,n=e.selectLongPressDelay;return n==null&&(n=e.longPressDelay),n}function yu(t,e,n){let r=t.dateSpan,i=e.dateSpan,s=[r.range.start,r.range.end,i.range.start,i.range.end];s.sort(El);let l={};for(let o of n){let a=o(t,e);if(a===!1)return null;a&&Object.assign(l,a)}return l.range={start:s[0],end:s[3]},l.allDay=r.allDay,l}class We extends _e{constructor(e){super(e),this.subjectEl=null,this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=l=>{let o=l.origEvent.target,{component:a,dragging:d}=this,{mirror:c}=d,{options:g}=a.context,h=a.context;this.subjectEl=l.subjectEl;let u=this.subjectSeg=we(l.subjectEl),v=(this.eventRange=u.eventRange).instance.instanceId;this.relevantEvents=An(h.getCurrentData().eventStore,v),d.minDistance=l.isTouch?0:g.eventDragMinDistance,d.delay=l.isTouch&&v!==a.props.eventSelection?Eu(a):null,g.fixedMirrorParent?c.parentNode=g.fixedMirrorParent:c.parentNode=P(o,".fc"),c.revertDuration=g.dragRevertDuration;let y=a.isValidSegDownEl(o)&&!P(o,".fc-event-resizer");d.setIgnoreMove(!y),this.isDragging=y&&l.subjectEl.classList.contains("fc-event-draggable")},this.handleDragStart=l=>{let o=this.component.context,a=this.eventRange,d=a.instance.instanceId;l.isTouch?d!==this.component.props.eventSelection&&o.dispatch({type:"SELECT_EVENT",eventInstanceId:d}):o.dispatch({type:"UNSELECT_EVENT"}),this.isDragging&&(o.calendarApi.unselect(l),o.emitter.trigger("eventDragStart",{el:this.subjectEl,event:new N(o,a.def,a.instance),jsEvent:l.origEvent,view:o.viewApi}))},this.handleHitUpdate=(l,o)=>{if(!this.isDragging)return;let a=this.relevantEvents,d=this.hitDragging.initialHit,c=this.component.context,g=null,h=null,u=null,m=!1,v={affectedEvents:a,mutatedEvents:F(),isEvent:!0};if(l){g=l.context;let y=g.options;c===g||y.editable&&y.droppable?(h=bu(d,l,this.eventRange.instance.range.start,g.getCurrentData().pluginHooks.eventDragMutationMassagers),h&&(u=Cn(a,g.getCurrentData().eventUiBases,h,g),v.mutatedEvents=u,$i(v,l.dateProfile,g)||(m=!0,h=null,u=null,v.mutatedEvents=F()))):g=null}this.displayDrag(g,v),m?dn():un(),o||(c===g&&Rt(d,l)&&(h=null),this.dragging.setMirrorNeedsRevert(!h),this.dragging.setMirrorIsVisible(!l||!this.subjectEl.getRootNode().querySelector(".fc-event-mirror")),this.receivingContext=g,this.validMutation=h,this.mutatedRelevantEvents=u)},this.handlePointerUp=()=>{this.isDragging||this.cleanup()},this.handleDragEnd=l=>{if(this.isDragging){let o=this.component.context,a=o.viewApi,{receivingContext:d,validMutation:c}=this,g=this.eventRange.def,h=this.eventRange.instance,u=new N(o,g,h),m=this.relevantEvents,v=this.mutatedRelevantEvents,{finalHit:y}=this.hitDragging;if(this.clearDrag(),o.emitter.trigger("eventDragStop",{el:this.subjectEl,event:u,jsEvent:l.origEvent,view:a}),c){if(d===o){let b=new N(o,v.defs[g.defId],h?v.instances[h.instanceId]:null);o.dispatch({type:"MERGE_EVENTS",eventStore:v});let E={oldEvent:u,event:b,relatedEvents:fe(v,o,h),revert(){o.dispatch({type:"MERGE_EVENTS",eventStore:m})}},D={};for(let w of o.getCurrentData().pluginHooks.eventDropTransformers)Object.assign(D,w(c,o));o.emitter.trigger("eventDrop",Object.assign(Object.assign(Object.assign({},E),D),{el:l.subjectEl,delta:c.datesDelta,jsEvent:l.origEvent,view:a})),o.emitter.trigger("eventChange",E)}else if(d){let b={event:u,relatedEvents:fe(m,o,h),revert(){o.dispatch({type:"MERGE_EVENTS",eventStore:m})}};o.emitter.trigger("eventLeave",Object.assign(Object.assign({},b),{draggedEl:l.subjectEl,view:a})),o.dispatch({type:"REMOVE_EVENTS",eventStore:m}),o.emitter.trigger("eventRemove",b);let E=v.defs[g.defId],D=v.instances[h.instanceId],w=new N(d,E,D);d.dispatch({type:"MERGE_EVENTS",eventStore:v});let O={event:w,relatedEvents:fe(v,d,D),revert(){d.dispatch({type:"REMOVE_EVENTS",eventStore:v})}};d.emitter.trigger("eventAdd",O),l.isTouch&&d.dispatch({type:"SELECT_EVENT",eventInstanceId:h.instanceId}),d.emitter.trigger("drop",Object.assign(Object.assign({},xs(y.dateSpan,d)),{draggedEl:l.subjectEl,jsEvent:l.origEvent,view:y.context.viewApi})),d.emitter.trigger("eventReceive",Object.assign(Object.assign({},O),{draggedEl:l.subjectEl,view:y.context.viewApi}))}}else o.emitter.trigger("_noEventDrop")}this.cleanup()};let{component:n}=this,{options:r}=n.context,i=this.dragging=new ze(e.el);i.pointer.selector=We.SELECTOR,i.touchScrollAllowed=!1,i.autoScroller.isEnabled=r.dragScroll;let s=this.hitDragging=new Ct(this.dragging,Kt);s.useSubjectCenter=e.useEventCenter,s.emitter.on("pointerdown",this.handlePointerDown),s.emitter.on("dragstart",this.handleDragStart),s.emitter.on("hitupdate",this.handleHitUpdate),s.emitter.on("pointerup",this.handlePointerUp),s.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}displayDrag(e,n){let r=this.component.context,i=this.receivingContext;i&&i!==e&&(i===r?i.dispatch({type:"SET_EVENT_DRAG",state:{affectedEvents:n.affectedEvents,mutatedEvents:F(),isEvent:!0}}):i.dispatch({type:"UNSET_EVENT_DRAG"})),e&&e.dispatch({type:"SET_EVENT_DRAG",state:n})}clearDrag(){let e=this.component.context,{receivingContext:n}=this;n&&n.dispatch({type:"UNSET_EVENT_DRAG"}),e!==n&&e.dispatch({type:"UNSET_EVENT_DRAG"})}cleanup(){this.subjectSeg=null,this.isDragging=!1,this.eventRange=null,this.relevantEvents=null,this.receivingContext=null,this.validMutation=null,this.mutatedRelevantEvents=null}}We.SELECTOR=".fc-event-draggable, .fc-event-resizable";function bu(t,e,n,r){let i=t.dateSpan,s=e.dateSpan,l=i.range.start,o=s.range.start,a={};i.allDay!==s.allDay&&(a.allDay=s.allDay,a.hasEnd=e.context.options.allDayMaintainDuration,s.allDay?l=I(n):l=n);let d=be(l,o,t.context.dateEnv,t.componentId===e.componentId?t.largeUnit:null);d.milliseconds&&(a.allDay=!1);let c={datesDelta:d,standardProps:a};for(let g of r)g(c,t,e);return c}function Eu(t){let{options:e}=t.context,n=e.eventLongPressDelay;return n==null&&(n=e.longPressDelay),n}class Su extends _e{constructor(e){super(e),this.draggingSegEl=null,this.draggingSeg=null,this.eventRange=null,this.relevantEvents=null,this.validMutation=null,this.mutatedRelevantEvents=null,this.handlePointerDown=s=>{let{component:l}=this,o=this.querySegEl(s),a=we(o),d=this.eventRange=a.eventRange;this.dragging.minDistance=l.context.options.eventDragMinDistance,this.dragging.setIgnoreMove(!this.component.isValidSegDownEl(s.origEvent.target)||s.isTouch&&this.component.props.eventSelection!==d.instance.instanceId)},this.handleDragStart=s=>{let{context:l}=this.component,o=this.eventRange;this.relevantEvents=An(l.getCurrentData().eventStore,this.eventRange.instance.instanceId);let a=this.querySegEl(s);this.draggingSegEl=a,this.draggingSeg=we(a),l.calendarApi.unselect(),l.emitter.trigger("eventResizeStart",{el:a,event:new N(l,o.def,o.instance),jsEvent:s.origEvent,view:l.viewApi})},this.handleHitUpdate=(s,l,o)=>{let{context:a}=this.component,d=this.relevantEvents,c=this.hitDragging.initialHit,g=this.eventRange.instance,h=null,u=null,m=!1,v={affectedEvents:d,mutatedEvents:F(),isEvent:!0};s&&(s.componentId===c.componentId&&this.isHitComboAllowed&&!this.isHitComboAllowed(c,s)||(h=Au(c,s,o.subjectEl.classList.contains("fc-event-resizer-start"),g.range))),h&&(u=Cn(d,a.getCurrentData().eventUiBases,h,a),v.mutatedEvents=u,$i(v,s.dateProfile,a)||(m=!0,h=null,u=null,v.mutatedEvents=null)),u?a.dispatch({type:"SET_EVENT_RESIZE",state:v}):a.dispatch({type:"UNSET_EVENT_RESIZE"}),m?dn():un(),l||(h&&Rt(c,s)&&(h=null),this.validMutation=h,this.mutatedRelevantEvents=u)},this.handleDragEnd=s=>{let{context:l}=this.component,o=this.eventRange.def,a=this.eventRange.instance,d=new N(l,o,a),c=this.relevantEvents,g=this.mutatedRelevantEvents;if(l.emitter.trigger("eventResizeStop",{el:this.draggingSegEl,event:d,jsEvent:s.origEvent,view:l.viewApi}),this.validMutation){let h=new N(l,g.defs[o.defId],a?g.instances[a.instanceId]:null);l.dispatch({type:"MERGE_EVENTS",eventStore:g});let u={oldEvent:d,event:h,relatedEvents:fe(g,l,a),revert(){l.dispatch({type:"MERGE_EVENTS",eventStore:c})}};l.emitter.trigger("eventResize",Object.assign(Object.assign({},u),{el:this.draggingSegEl,startDelta:this.validMutation.startDelta||C(0),endDelta:this.validMutation.endDelta||C(0),jsEvent:s.origEvent,view:l.viewApi})),l.emitter.trigger("eventChange",u)}else l.emitter.trigger("_noEventResize");this.draggingSeg=null,this.relevantEvents=null,this.validMutation=null};let{component:n}=e,r=this.dragging=new ze(e.el);r.pointer.selector=".fc-event-resizer",r.touchScrollAllowed=!1,r.autoScroller.isEnabled=n.context.options.dragScroll;let i=this.hitDragging=new Ct(this.dragging,Tn(e));i.emitter.on("pointerdown",this.handlePointerDown),i.emitter.on("dragstart",this.handleDragStart),i.emitter.on("hitupdate",this.handleHitUpdate),i.emitter.on("dragend",this.handleDragEnd)}destroy(){this.dragging.destroy()}querySegEl(e){return P(e.subjectEl,".fc-event")}}function Au(t,e,n,r){let i=t.context.dateEnv,s=t.dateSpan.range.start,l=e.dateSpan.range.start,o=be(s,l,i,t.largeUnit);if(n){if(i.add(r.start,o)<r.end)return{startDelta:o}}else if(i.add(r.end,o)>r.start)return{endDelta:o};return null}class Du{constructor(e){this.context=e,this.isRecentPointerDateSelect=!1,this.matchesCancel=!1,this.matchesEvent=!1,this.onSelect=r=>{r.jsEvent&&(this.isRecentPointerDateSelect=!0)},this.onDocumentPointerDown=r=>{let i=this.context.options.unselectCancel,s=ui(r.origEvent);this.matchesCancel=!!P(s,i),this.matchesEvent=!!P(s,We.SELECTOR)},this.onDocumentPointerUp=r=>{let{context:i}=this,{documentPointer:s}=this,l=i.getCurrentData();if(!s.wasTouchScroll){if(l.dateSelection&&!this.isRecentPointerDateSelect){let o=i.options.unselectAuto;o&&(!o||!this.matchesCancel)&&i.calendarApi.unselect(r)}l.eventSelection&&!this.matchesEvent&&i.dispatch({type:"UNSELECT_EVENT"})}this.isRecentPointerDateSelect=!1};let n=this.documentPointer=new Cs(document);n.shouldIgnoreMove=!0,n.shouldWatchScroll=!1,n.emitter.on("pointerdown",this.onDocumentPointerDown),n.emitter.on("pointerup",this.onDocumentPointerUp),e.emitter.on("select",this.onSelect)}destroy(){this.context.emitter.off("select",this.onSelect),this.documentPointer.destroy()}}const wu={fixedMirrorParent:p},Cu={dateClick:p,eventDragStart:p,eventDragStop:p,eventDrop:p,eventResizeStart:p,eventResizeStop:p,eventResize:p,drop:p,eventReceive:p,eventLeave:p};Nn.dataAttrPrefix="";var Ru=q({name:"@fullcalendar/interaction",componentInteractions:[pu,mu,We,Su],calendarInteractions:[Du],elementDraggingImpl:ze,optionRefiners:wu,listenerRefiners:Cu});class _u extends _{constructor(){super(...arguments),this.state={textId:re()}}render(){let{theme:e,dateEnv:n,options:r,viewApi:i}=this.context,{cellId:s,dayDate:l,todayRange:o}=this.props,{textId:a}=this.state,d=xn(l,o),c=r.listDayFormat?n.format(l,r.listDayFormat):"",g=r.listDaySideFormat?n.format(l,r.listDaySideFormat):"",h=Object.assign({date:n.toDate(l),view:i,textId:a,text:c,sideText:g,navLinkAttrs:Ce(this.context,l),sideNavLinkAttrs:Ce(this.context,l,"day",!1)},d);return f(B,{elTag:"tr",elClasses:["fc-list-day",...Dt(d,e)],elAttrs:{"data-date":Ue(l)},renderProps:h,generatorName:"dayHeaderContent",customGenerator:r.dayHeaderContent,defaultGenerator:Tu,classNameGenerator:r.dayHeaderClassNames,didMount:r.dayHeaderDidMount,willUnmount:r.dayHeaderWillUnmount},u=>f("th",{scope:"colgroup",colSpan:3,id:s,"aria-labelledby":a},f(u,{elTag:"div",elClasses:["fc-list-day-cushion",e.getClass("tableCellShaded")]})))}}function Tu(t){return f(x,null,t.text&&f("a",Object.assign({id:t.textId,className:"fc-list-day-text"},t.navLinkAttrs),t.text),t.sideText&&f("a",Object.assign({"aria-hidden":!0,className:"fc-list-day-side-text"},t.sideNavLinkAttrs),t.sideText))}const xu=M({hour:"numeric",minute:"2-digit",meridiem:"short"});class Iu extends _{render(){let{props:e,context:n}=this,{options:r}=n,{seg:i,timeHeaderId:s,eventHeaderId:l,dateHeaderId:o}=e,a=r.eventTimeFormat||xu;return f(wt,Object.assign({},e,{elTag:"tr",elClasses:["fc-list-event",i.eventRange.def.url&&"fc-event-forced-url"],defaultGenerator:()=>Mu(i,n),seg:i,timeText:"",disableDragging:!0,disableResizing:!0}),(d,c)=>f(x,null,ku(i,a,n,s,o),f("td",{"aria-hidden":!0,className:"fc-list-event-graphic"},f("span",{className:"fc-list-event-dot",style:{borderColor:c.borderColor||c.backgroundColor}})),f(d,{elTag:"td",elClasses:["fc-list-event-title"],elAttrs:{headers:`${l} ${o}`}})))}}function Mu(t,e){let n=_n(t,e);return f("a",Object.assign({},n),t.eventRange.def.title)}function ku(t,e,n,r,i){let{options:s}=n;if(s.displayEventTime!==!1){let l=t.eventRange.def,o=t.eventRange.instance,a=!1,d;if(l.allDay?a=!0:To(t.eventRange.range)?t.isStart?d=Oe(t,e,n,null,null,o.range.start,t.end):t.isEnd?d=Oe(t,e,n,null,null,t.start,o.range.end):a=!0:d=Oe(t,e,n),a){let c={text:n.options.allDayText,view:n.viewApi};return f(B,{elTag:"td",elClasses:["fc-list-event-time"],elAttrs:{headers:`${r} ${i}`},renderProps:c,generatorName:"allDayContent",customGenerator:s.allDayContent,defaultGenerator:Nu,classNameGenerator:s.allDayClassNames,didMount:s.allDayDidMount,willUnmount:s.allDayWillUnmount})}return f("td",{className:"fc-list-event-time"},d)}return null}function Nu(t){return t.text}class Ou extends W{constructor(){super(...arguments),this.computeDateVars=S(Pu),this.eventStoreToSegs=S(this._eventStoreToSegs),this.state={timeHeaderId:re(),eventHeaderId:re(),dateHeaderIdRoot:re()},this.setRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e}):this.context.unregisterInteractiveComponent(this)}}render(){let{props:e,context:n}=this,{dayDates:r,dayRanges:i}=this.computeDateVars(e.dateProfile),s=this.eventStoreToSegs(e.eventStore,e.eventUiBases,i);return f(Be,{elRef:this.setRootEl,elClasses:["fc-list",n.theme.getClass("table"),n.options.stickyHeaderDates!==!1?"fc-list-sticky":""],viewSpec:n.viewSpec},f(Ki,{liquid:!e.isHeightAuto,overflowX:e.isHeightAuto?"visible":"hidden",overflowY:e.isHeightAuto?"visible":"auto"},s.length>0?this.renderSegList(s,r):this.renderEmptyMessage()))}renderEmptyMessage(){let{options:e,viewApi:n}=this.context,r={text:e.noEventsText,view:n};return f(B,{elTag:"div",elClasses:["fc-list-empty"],renderProps:r,generatorName:"noEventsContent",customGenerator:e.noEventsContent,defaultGenerator:Hu,classNameGenerator:e.noEventsClassNames,didMount:e.noEventsDidMount,willUnmount:e.noEventsWillUnmount},i=>f(i,{elTag:"div",elClasses:["fc-list-empty-cushion"]}))}renderSegList(e,n){let{theme:r,options:i}=this.context,{timeHeaderId:s,eventHeaderId:l,dateHeaderIdRoot:o}=this.state,a=Bu(e);return f(Te,{unit:"day"},(d,c)=>{let g=[];for(let h=0;h<a.length;h+=1){let u=a[h];if(u){let m=Ue(n[h]),v=o+"-"+m;g.push(f(_u,{key:m,cellId:v,dayDate:n[h],todayRange:c})),u=Rn(u,i.eventOrder);for(let y of u)g.push(f(Iu,Object.assign({key:m+":"+y.eventRange.instance.instanceId,seg:y,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,timeHeaderId:s,eventHeaderId:l,dateHeaderId:v},Z(y,c,d))))}}return f("table",{className:"fc-list-table "+r.getClass("table")},f("thead",null,f("tr",null,f("th",{scope:"col",id:s},i.timeHint),f("th",{scope:"col","aria-hidden":!0}),f("th",{scope:"col",id:l},i.eventHint))),f("tbody",null,g))})}_eventStoreToSegs(e,n,r){return this.eventRangesToSegs(Jt(e,n,this.props.dateProfile.activeRange,this.context.options.nextDayThreshold).fg,r)}eventRangesToSegs(e,n){let r=[];for(let i of e)r.push(...this.eventRangeToSegs(i,n));return r}eventRangeToSegs(e,n){let{dateEnv:r}=this.context,{nextDayThreshold:i}=this.context.options,s=e.range,l=e.def.allDay,o,a,d,c=[];for(o=0;o<n.length;o+=1)if(a=le(s,n[o]),a&&(d={component:this,eventRange:e,start:a.start,end:a.end,isStart:e.isStart&&a.start.valueOf()===s.start.valueOf(),isEnd:e.isEnd&&a.end.valueOf()===s.end.valueOf(),dayIndex:o},c.push(d),!d.isEnd&&!l&&o+1<n.length&&s.end<r.add(n[o+1].start,i))){d.end=s.end,d.isEnd=!0;break}return c}}function Hu(t){return t.text}function Pu(t){let e=I(t.renderRange.start),n=t.renderRange.end,r=[],i=[];for(;e<n;)r.push(e),i.push({start:e,end:H(e,1)}),e=H(e,1);return{dayDates:r,dayRanges:i}}function Bu(t){let e=[],n,r;for(n=0;n<t.length;n+=1)r=t[n],(e[r.dayIndex]||(e[r.dayIndex]=[])).push(r);return e}var Lu=':root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:"";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}';yt(Lu);const Uu={listDayFormat:Lr,listDaySideFormat:Lr,noEventsClassNames:p,noEventsContent:p,noEventsDidMount:p,noEventsWillUnmount:p};function Lr(t){return t===!1?null:M(t)}var ju=q({name:"@fullcalendar/list",optionRefiners:Uu,views:{list:{component:Ou,buttonTextKey:"list",listDayFormat:{month:"long",day:"numeric",year:"numeric"}},listDay:{type:"list",duration:{days:1},listDayFormat:{weekday:"long"}},listWeek:{type:"list",duration:{weeks:1},listDayFormat:{weekday:"long"},listDaySideFormat:{month:"long",day:"numeric",year:"numeric"}},listMonth:{type:"list",duration:{month:1},listDaySideFormat:{weekday:"long"}},listYear:{type:"list",duration:{year:1},listDaySideFormat:{weekday:"long"}}}});class Fu extends Ra{getKeyInfo(){return{allDay:{},timed:{}}}getKeysForDateSpan(e){return e.allDay?["allDay"]:["timed"]}getKeysForEventDef(e){return e.allDay?ra(e)?["timed","allDay"]:["allDay"]:["timed"]}}const zu=M({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"short"});function Is(t){let e=["fc-timegrid-slot","fc-timegrid-slot-label",t.isLabeled?"fc-scrollgrid-shrink":"fc-timegrid-slot-minor"];return f(J.Consumer,null,n=>{if(!t.isLabeled)return f("td",{className:e.join(" "),"data-time":t.isoTimeStr});let{dateEnv:r,options:i,viewApi:s}=n,l=i.slotLabelFormat==null?zu:Array.isArray(i.slotLabelFormat)?M(i.slotLabelFormat[0]):M(i.slotLabelFormat),o={level:0,time:t.time,date:r.toDate(t.date),view:s,text:r.format(t.date,l)};return f(B,{elTag:"td",elClasses:e,elAttrs:{"data-time":t.isoTimeStr},renderProps:o,generatorName:"slotLabelContent",customGenerator:i.slotLabelContent,defaultGenerator:Wu,classNameGenerator:i.slotLabelClassNames,didMount:i.slotLabelDidMount,willUnmount:i.slotLabelWillUnmount},a=>f("div",{className:"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame"},f(a,{elTag:"div",elClasses:["fc-timegrid-slot-label-cushion","fc-scrollgrid-shrink-cushion"]})))})}function Wu(t){return t.text}class Vu extends _{render(){return this.props.slatMetas.map(e=>f("tr",{key:e.key},f(Is,Object.assign({},e))))}}const Gu=M({week:"short"}),qu=5;class Qu extends W{constructor(){super(...arguments),this.allDaySplitter=new Fu,this.headerElRef=L(),this.rootElRef=L(),this.scrollerElRef=L(),this.state={slatCoords:null},this.handleScrollTopRequest=e=>{let n=this.scrollerElRef.current;n&&(n.scrollTop=e)},this.renderHeadAxis=(e,n="")=>{let{options:r}=this.context,{dateProfile:i}=this.props,s=i.renderRange,o=oe(s.start,s.end)===1?Ce(this.context,s.start,"week"):{};return r.weekNumbers&&e==="day"?f(ss,{elTag:"th",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},date:s.start,defaultFormat:Gu},a=>f("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame","fc-timegrid-axis-frame-liquid"].join(" "),style:{height:n}},f(a,{elTag:"a",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"],elAttrs:o}))):f("th",{"aria-hidden":!0,className:"fc-timegrid-axis"},f("div",{className:"fc-timegrid-axis-frame",style:{height:n}}))},this.renderTableRowAxis=e=>{let{options:n,viewApi:r}=this.context,i={text:n.allDayText,view:r};return f(B,{elTag:"td",elClasses:["fc-timegrid-axis","fc-scrollgrid-shrink"],elAttrs:{"aria-hidden":!0},renderProps:i,generatorName:"allDayContent",customGenerator:n.allDayContent,defaultGenerator:Yu,classNameGenerator:n.allDayClassNames,didMount:n.allDayDidMount,willUnmount:n.allDayWillUnmount},s=>f("div",{className:["fc-timegrid-axis-frame","fc-scrollgrid-shrink-frame",e==null?" fc-timegrid-axis-frame-liquid":""].join(" "),style:{height:e}},f(s,{elTag:"span",elClasses:["fc-timegrid-axis-cushion","fc-scrollgrid-shrink-cushion","fc-scrollgrid-sync-inner"]})))},this.handleSlatCoords=e=>{this.setState({slatCoords:e})}}renderSimpleLayout(e,n,r){let{context:i,props:s}=this,l=[],o=pt(i.options);return e&&l.push({type:"header",key:"header",isSticky:o,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),n&&(l.push({type:"body",key:"all-day",chunk:{content:n}}),l.push({type:"body",key:"all-day-divider",outerContent:f("tr",{role:"presentation",className:"fc-scrollgrid-section"},f("td",{className:"fc-timegrid-divider "+i.theme.getClass("tableCellShaded")}))})),l.push({type:"body",key:"body",liquid:!0,expandRows:!!i.options.expandRows,chunk:{scrollerElRef:this.scrollerElRef,content:r}}),f(Be,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:i.viewSpec},f(On,{liquid:!s.isHeightAuto&&!s.forPrint,collapsibleWidth:s.forPrint,cols:[{width:"shrink"}],sections:l}))}renderHScrollLayout(e,n,r,i,s,l,o){let a=this.context.pluginHooks.scrollGridImpl;if(!a)throw new Error("No ScrollGrid implementation");let{context:d,props:c}=this,g=!c.forPrint&&pt(d.options),h=!c.forPrint&&ts(d.options),u=[];e&&u.push({type:"header",key:"header",isSticky:g,syncRowHeights:!0,chunks:[{key:"axis",rowContent:v=>f("tr",{role:"presentation"},this.renderHeadAxis("day",v.rowSyncHeights[0]))},{key:"cols",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),n&&(u.push({type:"body",key:"all-day",syncRowHeights:!0,chunks:[{key:"axis",rowContent:v=>f("tr",{role:"presentation"},this.renderTableRowAxis(v.rowSyncHeights[0]))},{key:"cols",content:n}]}),u.push({key:"all-day-divider",type:"body",outerContent:f("tr",{role:"presentation",className:"fc-scrollgrid-section"},f("td",{colSpan:2,className:"fc-timegrid-divider "+d.theme.getClass("tableCellShaded")}))}));let m=d.options.nowIndicator;return u.push({type:"body",key:"body",liquid:!0,expandRows:!!d.options.expandRows,chunks:[{key:"axis",content:v=>f("div",{className:"fc-timegrid-axis-chunk"},f("table",{"aria-hidden":!0,style:{height:v.expandRows?v.clientHeight:""}},v.tableColGroupNode,f("tbody",null,f(Vu,{slatMetas:l}))),f("div",{className:"fc-timegrid-now-indicator-container"},f(Te,{unit:m?"minute":"day"},y=>{let b=m&&o&&o.safeComputeTop(y);return typeof b=="number"?f(Hn,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:b},isAxis:!0,date:y}):null})))},{key:"cols",scrollerElRef:this.scrollerElRef,content:r}]}),h&&u.push({key:"footer",type:"footer",isSticky:!0,chunks:[{key:"axis",content:nn},{key:"cols",content:nn}]}),f(Be,{elRef:this.rootElRef,elClasses:["fc-timegrid"],viewSpec:d.viewSpec},f(a,{liquid:!c.isHeightAuto&&!c.forPrint,forPrint:c.forPrint,collapsibleWidth:!1,colGroups:[{width:"shrink",cols:[{width:"shrink"}]},{cols:[{span:i,minWidth:s}]}],sections:u}))}getAllDayMaxEventProps(){let{dayMaxEvents:e,dayMaxEventRows:n}=this.context.options;return(e===!0||n===!0)&&(e=void 0,n=qu),{dayMaxEvents:e,dayMaxEventRows:n}}}function Yu(t){return t.text}class Zu{constructor(e,n,r){this.positions=e,this.dateProfile=n,this.slotDuration=r}safeComputeTop(e){let{dateProfile:n}=this;if(Y(n.currentRange,e)){let r=I(e),i=e.valueOf()-r.valueOf();if(i>=z(n.slotMinTime)&&i<z(n.slotMaxTime))return this.computeTimeTop(C(i))}return null}computeDateTop(e,n){return n||(n=I(e)),this.computeTimeTop(C(e.valueOf()-n.valueOf()))}computeTimeTop(e){let{positions:n,dateProfile:r}=this,i=n.els.length,s=(e.milliseconds-z(r.slotMinTime))/z(this.slotDuration),l,o;return s=Math.max(0,s),s=Math.min(i,s),l=Math.floor(s),l=Math.min(l,i-1),o=s-l,n.tops[l]+n.getHeight(l)*o}}class $u extends _{render(){let{props:e,context:n}=this,{options:r}=n,{slatElRefs:i}=e;return f("tbody",null,e.slatMetas.map((s,l)=>{let o={time:s.time,date:n.dateEnv.toDate(s.date),view:n.viewApi};return f("tr",{key:s.key,ref:i.createRef(s.key)},e.axis&&f(Is,Object.assign({},s)),f(B,{elTag:"td",elClasses:["fc-timegrid-slot","fc-timegrid-slot-lane",!s.isLabeled&&"fc-timegrid-slot-minor"],elAttrs:{"data-time":s.isoTimeStr},renderProps:o,generatorName:"slotLaneContent",customGenerator:r.slotLaneContent,classNameGenerator:r.slotLaneClassNames,didMount:r.slotLaneDidMount,willUnmount:r.slotLaneWillUnmount}))}))}}class Xu extends _{constructor(){super(...arguments),this.rootElRef=L(),this.slatElRefs=new Q}render(){let{props:e,context:n}=this;return f("div",{ref:this.rootElRef,className:"fc-timegrid-slots"},f("table",{"aria-hidden":!0,className:n.theme.getClass("table"),style:{minWidth:e.tableMinWidth,width:e.clientWidth,height:e.minHeight}},e.tableColGroupNode,f($u,{slatElRefs:this.slatElRefs,axis:e.axis,slatMetas:e.slatMetas})))}componentDidMount(){this.updateSizing()}componentDidUpdate(){this.updateSizing()}componentWillUnmount(){this.props.onCoords&&this.props.onCoords(null)}updateSizing(){let{context:e,props:n}=this;n.onCoords&&n.clientWidth!==null&&this.rootElRef.current.offsetHeight&&n.onCoords(new Zu(new Re(this.rootElRef.current,Ju(this.slatElRefs.currentMap,n.slatMetas),!1,!0),this.props.dateProfile,e.options.slotDuration))}}function Ju(t,e){return e.map(n=>t[n.key])}function Ie(t,e){let n=[],r;for(r=0;r<e;r+=1)n.push([]);if(t)for(r=0;r<t.length;r+=1)n[t[r].col].push(t[r]);return n}function Ur(t,e){let n=[];if(t){for(let r=0;r<e;r+=1)n[r]={affectedInstances:t.affectedInstances,isEvent:t.isEvent,segs:[]};for(let r of t.segs)n[r.col].segs.push(r)}else for(let r=0;r<e;r+=1)n[r]=null;return n}class Ku extends _{render(){let{props:e}=this;return f(ls,{elClasses:["fc-timegrid-more-link"],elStyle:{top:e.top,bottom:e.bottom},allDayDate:null,moreCnt:e.hiddenSegs.length,allSegs:e.hiddenSegs,hiddenSegs:e.hiddenSegs,extraDateSpan:e.extraDateSpan,dateProfile:e.dateProfile,todayRange:e.todayRange,popoverContent:()=>ks(e.hiddenSegs,e),defaultGenerator:ef,forceTimed:!0},n=>f(n,{elTag:"div",elClasses:["fc-timegrid-more-link-inner","fc-sticky"]}))}}function ef(t){return t.shortText}function tf(t,e,n){let r=new Wi;e!=null&&(r.strictOrder=e),n!=null&&(r.maxStackCnt=n);let i=r.addSegs(t),s=Ua(i),l=nf(r);return l=of(l,1),{segRects:af(l),hiddenGroups:s}}function nf(t){const{entriesByLevel:e}=t,n=Un((r,i)=>r+":"+i,(r,i)=>{let s=lf(t,r,i),l=jr(s,n),o=e[r][i];return[Object.assign(Object.assign({},o),{nextLevelNodes:l[0]}),o.thickness+l[1]]});return jr(e.length?{level:0,lateralStart:0,lateralEnd:e[0].length}:null,n)[0]}function jr(t,e){if(!t)return[[],0];let{level:n,lateralStart:r,lateralEnd:i}=t,s=r,l=[];for(;s<i;)l.push(e(n,s)),s+=1;return l.sort(rf),[l.map(sf),l[0][1]]}function rf(t,e){return e[1]-t[1]}function sf(t){return t[0]}function lf(t,e,n){let{levelCoords:r,entriesByLevel:i}=t,s=i[e][n],l=r[e]+s.thickness,o=r.length,a=e;for(;a<o&&r[a]<l;a+=1);for(;a<o;a+=1){let d=i[a],c,g=tn(d,s.span.start,en),h=g[0]+g[1],u=h;for(;(c=d[u])&&c.span.start<s.span.end;)u+=1;if(h<u)return{level:a,lateralStart:h,lateralEnd:u}}return null}function of(t,e){const n=Un((r,i,s)=>he(r),(r,i,s)=>{let{nextLevelNodes:l,thickness:o}=r,a=o+s,d=o/a,c,g=[];if(!l.length)c=e;else for(let u of l)if(c===void 0){let m=n(u,i,a);c=m[0],g.push(m[1])}else{let m=n(u,c,0);g.push(m[1])}let h=(c-i)*d;return[c-h,Object.assign(Object.assign({},r),{thickness:h,nextLevelNodes:g})]});return t.map(r=>n(r,0,0)[1])}function af(t){let e=[];const n=Un((i,s,l)=>he(i),(i,s,l)=>{let o=Object.assign(Object.assign({},i),{levelCoord:s,stackDepth:l,stackForward:0});return e.push(o),o.stackForward=r(i.nextLevelNodes,s+i.thickness,l+1)+1});function r(i,s,l){let o=0;for(let a of i)o=Math.max(n(a,s,l),o);return o}return r(t,0,0),e}function Un(t,e){const n={};return(...r)=>{let i=t(...r);return i in n?n[i]:n[i]=e(...r)}}function Fr(t,e,n=null,r=0){let i=[];if(n)for(let s=0;s<t.length;s+=1){let l=t[s],o=n.computeDateTop(l.start,e),a=Math.max(o+(r||0),n.computeDateTop(l.end,e));i.push({start:Math.round(o),end:Math.round(a)})}return i}function cf(t,e,n,r){let i=[],s=[];for(let d=0;d<t.length;d+=1){let c=e[d];c?i.push({index:d,thickness:1,span:c}):s.push(t[d])}let{segRects:l,hiddenGroups:o}=tf(i,n,r),a=[];for(let d of l)a.push({seg:t[d.index],rect:d});for(let d of s)a.push({seg:d,rect:null});return{segPlacements:a,hiddenGroups:o}}const df=M({hour:"numeric",minute:"2-digit",meridiem:!1});class Ms extends _{render(){return f(ns,Object.assign({},this.props,{elClasses:["fc-timegrid-event","fc-v-event",this.props.isShort&&"fc-timegrid-event-short"],defaultTimeFormat:df}))}}class uf extends _{constructor(){super(...arguments),this.sortEventSegs=S(Rn)}render(){let{props:e,context:n}=this,{options:r}=n,i=r.selectMirror,s=e.eventDrag&&e.eventDrag.segs||e.eventResize&&e.eventResize.segs||i&&e.dateSelectionSegs||[],l=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{},o=this.sortEventSegs(e.fgEventSegs,r.eventOrder);return f(Pn,{elTag:"td",elRef:e.elRef,elClasses:["fc-timegrid-col",...e.extraClassNames||[]],elAttrs:Object.assign({role:"gridcell"},e.extraDataAttrs),date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,extraRenderProps:e.extraRenderProps},a=>f("div",{className:"fc-timegrid-col-frame"},f("div",{className:"fc-timegrid-col-bg"},this.renderFillSegs(e.businessHourSegs,"non-business"),this.renderFillSegs(e.bgEventSegs,"bg-event"),this.renderFillSegs(e.dateSelectionSegs,"highlight")),f("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(o,l,!1,!1,!1)),f("div",{className:"fc-timegrid-col-events"},this.renderFgSegs(s,{},!!e.eventDrag,!!e.eventResize,!!i,"mirror")),f("div",{className:"fc-timegrid-now-indicator-container"},this.renderNowIndicator(e.nowIndicatorSegs)),Bn(r)&&f(a,{elTag:"div",elClasses:["fc-timegrid-col-misc"]})))}renderFgSegs(e,n,r,i,s,l){let{props:o}=this;return o.forPrint?ks(e,o):this.renderPositionedFgSegs(e,n,r,i,s,l)}renderPositionedFgSegs(e,n,r,i,s,l){let{eventMaxStack:o,eventShortHeight:a,eventOrderStrict:d,eventMinHeight:c}=this.context.options,{date:g,slatCoords:h,eventSelection:u,todayRange:m,nowDate:v}=this.props,y=r||i||s,b=Fr(e,g,h,c),{segPlacements:E,hiddenGroups:D}=cf(e,b,d,o);return f(x,null,this.renderHiddenGroups(D,e),E.map(w=>{let{seg:O,rect:T}=w,k=O.eventRange.instance.instanceId,R=y||!!(!n[k]&&T),K=zt(T&&T.span),xe=!y&&T?this.computeSegHStyle(T):{left:0,right:0},Ns=!!T&&T.stackForward>0,Os=!!T&&T.span.end-T.span.start<a;return f("div",{className:"fc-timegrid-event-harness"+(Ns?" fc-timegrid-event-harness-inset":""),key:l||k,style:Object.assign(Object.assign({visibility:R?"":"hidden"},K),xe)},f(Ms,Object.assign({seg:O,isDragging:r,isResizing:i,isDateSelecting:s,isSelected:k===u,isShort:Os},Z(O,m,v))))}))}renderHiddenGroups(e,n){let{extraDateSpan:r,dateProfile:i,todayRange:s,nowDate:l,eventSelection:o,eventDrag:a,eventResize:d}=this.props;return f(x,null,e.map(c=>{let g=zt(c.span),h=ff(c.entries,n);return f(Ku,{key:mi(os(h)),hiddenSegs:h,top:g.top,bottom:g.bottom,extraDateSpan:r,dateProfile:i,todayRange:s,nowDate:l,eventSelection:o,eventDrag:a,eventResize:d})}))}renderFillSegs(e,n){let{props:r,context:i}=this,l=Fr(e,r.date,r.slatCoords,i.options.eventMinHeight).map((o,a)=>{let d=e[a];return f("div",{key:Pi(d.eventRange),className:"fc-timegrid-bg-harness",style:zt(o)},n==="bg-event"?f(rs,Object.assign({seg:d},Z(d,r.todayRange,r.nowDate))):is(n))});return f(x,null,l)}renderNowIndicator(e){let{slatCoords:n,date:r}=this.props;return n?e.map((i,s)=>f(Hn,{key:s,elClasses:["fc-timegrid-now-indicator-line"],elStyle:{top:n.computeDateTop(i.start,r)},isAxis:!1,date:r})):null}computeSegHStyle(e){let{isRtl:n,options:r}=this.context,i=r.slotEventOverlap,s=e.levelCoord,l=e.levelCoord+e.thickness,o,a;i&&(l=Math.min(1,s+(l-s)*2)),n?(o=1-l,a=s):(o=s,a=1-l);let d={zIndex:e.stackDepth+1,left:o*100+"%",right:a*100+"%"};return i&&!e.stackForward&&(d[n?"marginLeft":"marginRight"]=10*2),d}}function ks(t,{todayRange:e,nowDate:n,eventSelection:r,eventDrag:i,eventResize:s}){let l=(i?i.affectedInstances:null)||(s?s.affectedInstances:null)||{};return f(x,null,t.map(o=>{let a=o.eventRange.instance.instanceId;return f("div",{key:a,style:{visibility:l[a]?"hidden":""}},f(Ms,Object.assign({seg:o,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:a===r,isShort:!1},Z(o,e,n))))}))}function zt(t){return t?{top:t.start,bottom:-t.end}:{top:"",bottom:""}}function ff(t,e){return t.map(n=>e[n.index])}class hf extends _{constructor(){super(...arguments),this.splitFgEventSegs=S(Ie),this.splitBgEventSegs=S(Ie),this.splitBusinessHourSegs=S(Ie),this.splitNowIndicatorSegs=S(Ie),this.splitDateSelectionSegs=S(Ie),this.splitEventDrag=S(Ur),this.splitEventResize=S(Ur),this.rootElRef=L(),this.cellElRefs=new Q}render(){let{props:e,context:n}=this,r=n.options.nowIndicator&&e.slatCoords&&e.slatCoords.safeComputeTop(e.nowDate),i=e.cells.length,s=this.splitFgEventSegs(e.fgEventSegs,i),l=this.splitBgEventSegs(e.bgEventSegs,i),o=this.splitBusinessHourSegs(e.businessHourSegs,i),a=this.splitNowIndicatorSegs(e.nowIndicatorSegs,i),d=this.splitDateSelectionSegs(e.dateSelectionSegs,i),c=this.splitEventDrag(e.eventDrag,i),g=this.splitEventResize(e.eventResize,i);return f("div",{className:"fc-timegrid-cols",ref:this.rootElRef},f("table",{role:"presentation",style:{minWidth:e.tableMinWidth,width:e.clientWidth}},e.tableColGroupNode,f("tbody",{role:"presentation"},f("tr",{role:"row"},e.axis&&f("td",{"aria-hidden":!0,className:"fc-timegrid-col fc-timegrid-axis"},f("div",{className:"fc-timegrid-col-frame"},f("div",{className:"fc-timegrid-now-indicator-container"},typeof r=="number"&&f(Hn,{elClasses:["fc-timegrid-now-indicator-arrow"],elStyle:{top:r},isAxis:!0,date:e.nowDate})))),e.cells.map((h,u)=>f(uf,{key:h.key,elRef:this.cellElRefs.createRef(h.key),dateProfile:e.dateProfile,date:h.date,nowDate:e.nowDate,todayRange:e.todayRange,extraRenderProps:h.extraRenderProps,extraDataAttrs:h.extraDataAttrs,extraClassNames:h.extraClassNames,extraDateSpan:h.extraDateSpan,fgEventSegs:s[u],bgEventSegs:l[u],businessHourSegs:o[u],nowIndicatorSegs:a[u],dateSelectionSegs:d[u],eventDrag:c[u],eventResize:g[u],slatCoords:e.slatCoords,eventSelection:e.eventSelection,forPrint:e.forPrint}))))))}componentDidMount(){this.updateCoords()}componentDidUpdate(){this.updateCoords()}updateCoords(){let{props:e}=this;e.onColCoords&&e.clientWidth!==null&&e.onColCoords(new Re(this.rootElRef.current,gf(this.cellElRefs.currentMap,e.cells),!0,!1))}}function gf(t,e){return e.map(n=>t[n.key])}class pf extends W{constructor(){super(...arguments),this.processSlotOptions=S(mf),this.state={slatCoords:null},this.handleRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e,isHitComboAllowed:this.props.isHitComboAllowed}):this.context.unregisterInteractiveComponent(this)},this.handleScrollRequest=e=>{let{onScrollTopRequest:n}=this.props,{slatCoords:r}=this.state;if(n&&r){if(e.time){let i=r.computeTimeTop(e.time);i=Math.ceil(i),i&&(i+=1),n(i)}return!0}return!1},this.handleColCoords=e=>{this.colCoords=e},this.handleSlatCoords=e=>{this.setState({slatCoords:e}),this.props.onSlatCoords&&this.props.onSlatCoords(e)}}render(){let{props:e,state:n}=this;return f("div",{className:"fc-timegrid-body",ref:this.handleRootEl,style:{width:e.clientWidth,minWidth:e.tableMinWidth}},f(Xu,{axis:e.axis,dateProfile:e.dateProfile,slatMetas:e.slatMetas,clientWidth:e.clientWidth,minHeight:e.expandRows?e.clientHeight:"",tableMinWidth:e.tableMinWidth,tableColGroupNode:e.axis?e.tableColGroupNode:null,onCoords:this.handleSlatCoords}),f(hf,{cells:e.cells,axis:e.axis,dateProfile:e.dateProfile,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,todayRange:e.todayRange,nowDate:e.nowDate,nowIndicatorSegs:e.nowIndicatorSegs,clientWidth:e.clientWidth,tableMinWidth:e.tableMinWidth,tableColGroupNode:e.tableColGroupNode,slatCoords:n.slatCoords,onColCoords:this.handleColCoords,forPrint:e.forPrint}))}componentDidMount(){this.scrollResponder=this.context.createScrollResponder(this.handleScrollRequest)}componentDidUpdate(e){this.scrollResponder.update(e.dateProfile!==this.props.dateProfile)}componentWillUnmount(){this.scrollResponder.detach()}queryHit(e,n){let{dateEnv:r,options:i}=this.context,{colCoords:s}=this,{dateProfile:l}=this.props,{slatCoords:o}=this.state,{snapDuration:a,snapsPerSlot:d}=this.processSlotOptions(this.props.slotDuration,i.snapDuration),c=s.leftToIndex(e),g=o.positions.topToIndex(n);if(c!=null&&g!=null){let h=this.props.cells[c],u=o.positions.tops[g],m=o.positions.getHeight(g),v=(n-u)/m,y=Math.floor(v*d),b=g*d+y,E=this.props.cells[c].date,D=qt(l.slotMinTime,Rl(a,b)),w=r.add(E,D),O=r.add(w,a);return{dateProfile:l,dateSpan:Object.assign({range:{start:w,end:O},allDay:!1},h.extraDateSpan),dayEl:s.els[c],rect:{left:s.lefts[c],right:s.rights[c],top:u,bottom:u+m},layer:0}}return null}}function mf(t,e){let n=e||t,r=fn(t,n);return r===null&&(n=t,r=1),{snapDuration:n,snapsPerSlot:r}}class vf extends Zi{sliceRange(e,n){let r=[];for(let i=0;i<n.length;i+=1){let s=le(e,n[i]);s&&r.push({start:s.start,end:s.end,isStart:s.start.valueOf()===e.start.valueOf(),isEnd:s.end.valueOf()===e.end.valueOf(),col:i})}return r}}class yf extends W{constructor(){super(...arguments),this.buildDayRanges=S(bf),this.slicer=new vf,this.timeColsRef=L()}render(){let{props:e,context:n}=this,{dateProfile:r,dayTableModel:i}=e,{nowIndicator:s,nextDayThreshold:l}=n.options,o=this.buildDayRanges(i,r,n.dateEnv);return f(Te,{unit:s?"minute":"day"},(a,d)=>f(pf,Object.assign({ref:this.timeColsRef},this.slicer.sliceProps(e,r,null,n,o),{forPrint:e.forPrint,axis:e.axis,dateProfile:r,slatMetas:e.slatMetas,slotDuration:e.slotDuration,cells:i.cells[0],tableColGroupNode:e.tableColGroupNode,tableMinWidth:e.tableMinWidth,clientWidth:e.clientWidth,clientHeight:e.clientHeight,expandRows:e.expandRows,nowDate:a,nowIndicatorSegs:s&&this.slicer.sliceNowDate(a,r,l,n,o),todayRange:d,onScrollTopRequest:e.onScrollTopRequest,onSlatCoords:e.onSlatCoords})))}}function bf(t,e,n){let r=[];for(let i of t.headerDates)r.push({start:n.add(i,e.slotMinTime),end:n.add(i,e.slotMaxTime)});return r}const zr=[{hours:1},{minutes:30},{minutes:15},{seconds:30},{seconds:15}];function Ef(t,e,n,r,i){let s=new Date(0),l=t,o=C(0),a=n||Sf(r),d=[];for(;z(l)<z(e);){let c=i.add(s,l),g=fn(o,a)!==null;d.push({date:c,time:l,key:c.toISOString(),isoTimeStr:zl(c),isLabeled:g}),l=qt(l,r),o=qt(o,r)}return d}function Sf(t){let e,n,r;for(e=zr.length-1;e>=0;e-=1)if(n=C(zr[e]),r=fn(n,t),r!==null&&r>1)return n;return t}class Af extends Qu{constructor(){super(...arguments),this.buildTimeColsModel=S(Df),this.buildSlatMetas=S(Ef)}render(){let{options:e,dateEnv:n,dateProfileGenerator:r}=this.context,{props:i}=this,{dateProfile:s}=i,l=this.buildTimeColsModel(s,r),o=this.allDaySplitter.splitProps(i),a=this.buildSlatMetas(s.slotMinTime,s.slotMaxTime,e.slotLabelInterval,e.slotDuration,n),{dayMinWidth:d}=e,c=!d,g=d,h=e.dayHeaders&&f(qi,{dates:l.headerDates,dateProfile:s,datesRepDistinctDays:!0,renderIntro:c?this.renderHeadAxis:null}),u=e.allDaySlot!==!1&&(v=>f(ws,Object.assign({},o.allDay,{dateProfile:s,dayTableModel:l,nextDayThreshold:e.nextDayThreshold,tableMinWidth:v.tableMinWidth,colGroupNode:v.tableColGroupNode,renderRowIntro:c?this.renderTableRowAxis:null,showWeekNumbers:!1,expandRows:!1,headerAlignElRef:this.headerElRef,clientWidth:v.clientWidth,clientHeight:v.clientHeight,forPrint:i.forPrint},this.getAllDayMaxEventProps()))),m=v=>f(yf,Object.assign({},o.timed,{dayTableModel:l,dateProfile:s,axis:c,slotDuration:e.slotDuration,slatMetas:a,forPrint:i.forPrint,tableColGroupNode:v.tableColGroupNode,tableMinWidth:v.tableMinWidth,clientWidth:v.clientWidth,clientHeight:v.clientHeight,onSlatCoords:this.handleSlatCoords,expandRows:v.expandRows,onScrollTopRequest:this.handleScrollTopRequest}));return g?this.renderHScrollLayout(h,u,m,l.colCnt,d,a,this.state.slatCoords):this.renderSimpleLayout(h,u,m)}}function Df(t,e){let n=new Qi(t.renderRange,e);return new Yi(n,!1)}var wf='.fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:"\\00a0"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:"\\00a0-\\00a0"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}';yt(wf);const Cf={allDaySlot:Boolean};var Rf=q({name:"@fullcalendar/timegrid",initialView:"timeGridWeek",optionRefiners:Cf,views:{timeGrid:{component:Af,usesMinMaxTime:!0,allDaySlot:!0,slotDuration:"00:30:00",slotEventOverlap:!0},timeGridDay:{type:"timeGrid",duration:{days:1}},timeGridWeek:{type:"timeGrid",duration:{weeks:1}}}});try{window.Calendar=Nd,window.dayGridPlugin=iu,window.interactionPlugin=Ru,window.listPlugin=ju,window.timegridPlugin=Rf}catch{}
