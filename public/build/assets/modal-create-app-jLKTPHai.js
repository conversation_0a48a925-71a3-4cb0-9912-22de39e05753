$(function(){const s=document.getElementById("createApp"),r=document.querySelector(".app-credit-card-mask"),a=document.querySelector(".app-expiry-date-mask"),c=document.querySelector(".app-cvv-code-mask");function i(){r&&new Cleave(r,{creditCard:!0,onCreditCardTypeChanged:function(t){t!=""&&t!="unknown"?document.querySelector(".app-card-type").innerHTML='<img src="'+assetsPath+"img/icons/payments/"+t+'-cc.png" class="cc-icon-image" height="28"/>':document.querySelector(".app-card-type").innerHTML=""}})}a&&new Cleave(a,{date:!0,delimiter:"/",datePattern:["m","y"]}),c&&new Cleave(c,{numeral:!0,numeralPositiveOnly:!0}),s.addEventListener("show.bs.modal",function(t){const e=document.querySelector("#wizard-create-app");if(typeof e!==void 0&&e!==null){const d=[].slice.call(e.querySelectorAll(".btn-next")),l=[].slice.call(e.querySelectorAll(".btn-prev")),o=e.querySelector(".btn-submit"),p=new Stepper(e,{linear:!1});d&&d.forEach(n=>{n.addEventListener("click",u=>{p.next(),i()})}),l&&l.forEach(n=>{n.addEventListener("click",u=>{p.previous(),i()})}),o&&o.addEventListener("click",n=>{alert("Submitted..!!")})}})});
