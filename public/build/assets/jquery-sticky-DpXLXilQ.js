import{r as N}from"./jquery-BQXThELV.js";var g={exports:{}},E;function S(){return E||(E=1,function(y){(function(n){y.exports?y.exports=n(N()):n(jQuery)})(function(n){var v=Array.prototype.slice,x=Array.prototype.splice,d={topSpacing:0,bottomSpacing:0,className:"is-sticky",wrapperClassName:"sticky-wrapper",center:!1,getWidthFrom:"",widthFromWrapper:!0,responsiveWidth:!1,zIndex:"auto"},f=n(window),W=n(document),p=[],m=f.height(),u=function(){for(var e=f.scrollTop(),i=W.height(),r=i-m,s=e>r?r-e:0,c=0,h=p.length;c<h;c++){var t=p[c],b=t.stickyWrapper.offset().top,H=b-t.topSpacing-s;if(t.stickyWrapper.css("height",t.stickyElement.outerHeight()),e<=H)t.currentTop!==null&&(t.stickyElement.css({width:"",position:"",top:"","z-index":""}),t.stickyElement.parent().removeClass(t.className),t.stickyElement.trigger("sticky-end",[t]),t.currentTop=null);else{var a=i-t.stickyElement.outerHeight()-t.topSpacing-t.bottomSpacing-e-s;if(a<0?a=a+t.topSpacing:a=t.topSpacing,t.currentTop!==a){var l;t.getWidthFrom?l=n(t.getWidthFrom).width()||null:t.widthFromWrapper&&(l=t.stickyWrapper.width()),l==null&&(l=t.stickyElement.width()),t.stickyElement.css("width",l).css("position","fixed").css("top",a).css("z-index",t.zIndex),t.stickyElement.parent().addClass(t.className),t.currentTop===null?t.stickyElement.trigger("sticky-start",[t]):t.stickyElement.trigger("sticky-update",[t]),t.currentTop===t.topSpacing&&t.currentTop>a||t.currentTop===null&&a<t.topSpacing?t.stickyElement.trigger("sticky-bottom-reached",[t]):t.currentTop!==null&&a===t.topSpacing&&t.currentTop<a&&t.stickyElement.trigger("sticky-bottom-unreached",[t]),t.currentTop=a}var w=t.stickyWrapper.parent(),z=t.stickyElement.offset().top+t.stickyElement.outerHeight()>=w.offset().top+w.outerHeight()&&t.stickyElement.offset().top<=t.topSpacing;z?t.stickyElement.css("position","absolute").css("top","").css("bottom",0).css("z-index",""):t.stickyElement.css("position","fixed").css("top",a).css("bottom","").css("z-index",t.zIndex)}}},k=function(){m=f.height();for(var e=0,i=p.length;e<i;e++){var r=p[e],s=null;r.getWidthFrom?r.responsiveWidth&&(s=n(r.getWidthFrom).width()):r.widthFromWrapper&&(s=r.stickyWrapper.width()),s!=null&&r.stickyElement.css("width",s)}},o={init:function(e){var i=n.extend({},d,e);return this.each(function(){var r=n(this),s=r.attr("id"),c=s?s+"-"+d.wrapperClassName:d.wrapperClassName,h=n("<div></div>").attr("id",c).addClass(i.wrapperClassName);r.wrapAll(h);var t=r.parent();i.center&&t.css({width:r.outerWidth(),marginLeft:"auto",marginRight:"auto"}),r.css("float")==="right"&&r.css({float:"none"}).parent().css({float:"right"}),i.stickyElement=r,i.stickyWrapper=t,i.currentTop=null,p.push(i),o.setWrapperHeight(this),o.setupChangeListeners(this)})},setWrapperHeight:function(e){var i=n(e),r=i.parent();r&&r.css("height",i.outerHeight())},setupChangeListeners:function(e){if(window.MutationObserver){var i=new window.MutationObserver(function(r){(r[0].addedNodes.length||r[0].removedNodes.length)&&o.setWrapperHeight(e)});i.observe(e,{subtree:!0,childList:!0})}else e.addEventListener("DOMNodeInserted",function(){o.setWrapperHeight(e)},!1),e.addEventListener("DOMNodeRemoved",function(){o.setWrapperHeight(e)},!1)},update:u,unstick:function(e){return this.each(function(){for(var i=this,r=n(i),s=-1,c=p.length;c-- >0;)p[c].stickyElement.get(0)===i&&(x.call(p,c,1),s=c);s!==-1&&(r.unwrap(),r.css({width:"",position:"",top:"",float:"","z-index":""}))})}};window.addEventListener?(window.addEventListener("scroll",u,!1),window.addEventListener("resize",k,!1)):window.attachEvent&&(window.attachEvent("onscroll",u),window.attachEvent("onresize",k)),n.fn.sticky=function(e){if(o[e])return o[e].apply(this,v.call(arguments,1));if(typeof e=="object"||!e)return o.init.apply(this,arguments);n.error("Method "+e+" does not exist on jQuery.sticky")},n.fn.unstick=function(e){if(o[e])return o[e].apply(this,v.call(arguments,1));if(typeof e=="object"||!e)return o.unstick.apply(this,arguments);n.error("Method "+e+" does not exist on jQuery.sticky")},n(function(){setTimeout(u,0)})})}(g)),g.exports}S();
