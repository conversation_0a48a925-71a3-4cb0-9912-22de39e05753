(function(){const a=document.querySelector("#animation-dropdown"),i=document.querySelector("#animationModal");a&&(a.onchange=function(){i.classList="",i.classList.add("modal","animate__animated",this.value)});const n=document.querySelector("#youTubeModal"),l=n.querySelector("iframe");n.addEventListener("hidden.bs.modal",function(){l.setAttribute("src","")}),function(){[].slice.call(document.querySelectorAll('[data-bs-toggle="modal"]')).map(function(t){t.onclick=function(){const o=this.getAttribute("data-bs-target"),u=this.getAttribute("data-theVideo"),r=`${u}?autoplay=1`,d=document.querySelector(`${o} iframe`);d&&d.setAttribute("src",r)}})}(),document.querySelectorAll(".carousel").forEach(e=>{e.addEventListener("slide.bs.carousel",t=>{var o=$(t.relatedTarget).height();$(e).find(".active.carousel-item").parent().animate({height:o},500)})})})();
