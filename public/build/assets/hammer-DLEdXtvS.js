var _t={exports:{}};/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 <PERSON><PERSON>;
 * Licensed under the MIT license */var jt;function be(){return jt||(jt=1,function(yt){(function(l,Pt,Kt,v){var Nt=["","webkit","Moz","MS","ms","o"],$t=Pt.createElement("div"),te="function",L=Math.round,S=Math.abs,at=Date.now;function ot(t,e,i){return setTimeout(ht(t,i),e)}function x(t,e,i){return Array.isArray(t)?(P(t,i[e],i),!0):!1}function P(t,e,i){var r;if(t)if(t.forEach)t.forEach(e,i);else if(t.length!==v)for(r=0;r<t.length;)e.call(i,t[r],r,t),r++;else for(r in t)t.hasOwnProperty(r)&&e.call(i,t[r],r,t)}function Ot(t,e,i){var r="DEPRECATED METHOD: "+e+`
`+i+` AT 
`;return function(){var n=new Error("get-stack-trace"),s=n&&n.stack?n.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",a=l.console&&(l.console.warn||l.console.log);return a&&a.call(l.console,r,s),t.apply(this,arguments)}}var g;typeof Object.assign!="function"?g=function(e){if(e===v||e===null)throw new TypeError("Cannot convert undefined or null to object");for(var i=Object(e),r=1;r<arguments.length;r++){var n=arguments[r];if(n!==v&&n!==null)for(var s in n)n.hasOwnProperty(s)&&(i[s]=n[s])}return i}:g=Object.assign;var At=Ot(function(e,i,r){for(var n=Object.keys(i),s=0;s<n.length;)(!r||r&&e[n[s]]===v)&&(e[n[s]]=i[n[s]]),s++;return e},"extend","Use `assign`."),ee=Ot(function(e,i){return At(e,i,!0)},"merge","Use `assign`.");function T(t,e,i){var r=e.prototype,n;n=t.prototype=Object.create(r),n.constructor=t,n._super=r,i&&g(n,i)}function ht(t,e){return function(){return t.apply(e,arguments)}}function ct(t,e){return typeof t==te?t.apply(e&&e[0]||v,e):t}function Ct(t,e){return t===v?e:t}function G(t,e,i){P(Z(e),function(r){t.addEventListener(r,i,!1)})}function z(t,e,i){P(Z(e),function(r){t.removeEventListener(r,i,!1)})}function St(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function D(t,e){return t.indexOf(e)>-1}function Z(t){return t.trim().split(/\s+/g)}function H(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var r=0;r<t.length;){if(i&&t[r][i]==e||!i&&t[r]===e)return r;r++}return-1}function B(t){return Array.prototype.slice.call(t,0)}function Dt(t,e,i){for(var r=[],n=[],s=0;s<t.length;){var a=t[s][e];H(n,a)<0&&r.push(t[s]),n[s]=a,s++}return r=r.sort(function(c,f){return c[e]>f[e]}),r}function J(t,e){for(var i,r,n=e[0].toUpperCase()+e.slice(1),s=0;s<Nt.length;){if(i=Nt[s],r=i?i+n:e,r in t)return r;s++}return v}var ie=1;function re(){return ie++}function Mt(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||l}var ne=/mobile|tablet|ip(ad|hone|od)|android/i,Ut="ontouchstart"in l,se=J(l,"PointerEvent")!==v,ae=Ut&&ne.test(navigator.userAgent),b="touch",oe="pen",lt="mouse",he="kinect",ce=25,u=1,M=2,o=4,p=8,Q=1,X=2,F=4,V=8,W=16,I=X|F,U=V|W,Rt=I|U,Lt=["x","y"],j=["clientX","clientY"];function d(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(r){ct(t.options.enable,[t])&&i.handler(r)},this.init()}d.prototype={handler:function(){},init:function(){this.evEl&&G(this.element,this.evEl,this.domHandler),this.evTarget&&G(this.target,this.evTarget,this.domHandler),this.evWin&&G(Mt(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&z(this.element,this.evEl,this.domHandler),this.evTarget&&z(this.target,this.evTarget,this.domHandler),this.evWin&&z(Mt(this.element),this.evWin,this.domHandler)}};function le(t){var e,i=t.options.inputClass;return i?e=i:se?e=ft:ae?e=tt:Ut?e=vt:e=$,new e(t,ue)}function ue(t,e,i){var r=i.pointers.length,n=i.changedPointers.length,s=e&u&&r-n===0,a=e&(o|p)&&r-n===0;i.isFirst=!!s,i.isFinal=!!a,s&&(t.session={}),i.eventType=e,fe(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function fe(t,e){var i=t.session,r=e.pointers,n=r.length;i.firstInput||(i.firstInput=xt(e)),n>1&&!i.firstMultiple?i.firstMultiple=xt(e):n===1&&(i.firstMultiple=!1);var s=i.firstInput,a=i.firstMultiple,h=a?a.center:s.center,c=e.center=Ht(r);e.timeStamp=at(),e.deltaTime=e.timeStamp-s.timeStamp,e.angle=ut(h,c),e.distance=K(h,c),ve(i,e),e.offsetDirection=bt(e.deltaX,e.deltaY);var f=Yt(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=f.x,e.overallVelocityY=f.y,e.overallVelocity=S(f.x)>S(f.y)?f.x:f.y,e.scale=a?de(a.pointers,r):1,e.rotation=a?Te(a.pointers,r):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,pe(i,e);var y=t.element;St(e.srcEvent.target,y)&&(y=e.srcEvent.target),e.target=y}function ve(t,e){var i=e.center,r=t.offsetDelta||{},n=t.prevDelta||{},s=t.prevInput||{};(e.eventType===u||s.eventType===o)&&(n=t.prevDelta={x:s.deltaX||0,y:s.deltaY||0},r=t.offsetDelta={x:i.x,y:i.y}),e.deltaX=n.x+(i.x-r.x),e.deltaY=n.y+(i.y-r.y)}function pe(t,e){var i=t.lastInterval||e,r=e.timeStamp-i.timeStamp,n,s,a,h;if(e.eventType!=p&&(r>ce||i.velocity===v)){var c=e.deltaX-i.deltaX,f=e.deltaY-i.deltaY,y=Yt(r,c,f);s=y.x,a=y.y,n=S(y.x)>S(y.y)?y.x:y.y,h=bt(c,f),t.lastInterval=e}else n=i.velocity,s=i.velocityX,a=i.velocityY,h=i.direction;e.velocity=n,e.velocityX=s,e.velocityY=a,e.direction=h}function xt(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:L(t.pointers[i].clientX),clientY:L(t.pointers[i].clientY)},i++;return{timeStamp:at(),pointers:e,center:Ht(e),deltaX:t.deltaX,deltaY:t.deltaY}}function Ht(t){var e=t.length;if(e===1)return{x:L(t[0].clientX),y:L(t[0].clientY)};for(var i=0,r=0,n=0;n<e;)i+=t[n].clientX,r+=t[n].clientY,n++;return{x:L(i/e),y:L(r/e)}}function Yt(t,e,i){return{x:e/t||0,y:i/t||0}}function bt(t,e){return t===e?Q:S(t)>=S(e)?t<0?X:F:e<0?V:W}function K(t,e,i){i||(i=Lt);var r=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return Math.sqrt(r*r+n*n)}function ut(t,e,i){i||(i=Lt);var r=e[i[0]]-t[i[0]],n=e[i[1]]-t[i[1]];return Math.atan2(n,r)*180/Math.PI}function Te(t,e){return ut(e[1],e[0],j)+ut(t[1],t[0],j)}function de(t,e){return K(e[0],e[1],j)/K(t[0],t[1],j)}var me={mousedown:u,mousemove:M,mouseup:o},Ee="mousedown",ge="mousemove mouseup";function $(){this.evEl=Ee,this.evWin=ge,this.pressed=!1,d.apply(this,arguments)}T($,d,{handler:function(e){var i=me[e.type];i&u&&e.button===0&&(this.pressed=!0),i&M&&e.which!==1&&(i=o),this.pressed&&(i&o&&(this.pressed=!1),this.callback(this.manager,i,{pointers:[e],changedPointers:[e],pointerType:lt,srcEvent:e}))}});var Ie={pointerdown:u,pointermove:M,pointerup:o,pointercancel:p,pointerout:p},_e={2:b,3:oe,4:lt,5:he},Xt="pointerdown",Ft="pointermove pointerup pointercancel";l.MSPointerEvent&&!l.PointerEvent&&(Xt="MSPointerDown",Ft="MSPointerMove MSPointerUp MSPointerCancel");function ft(){this.evEl=Xt,this.evWin=Ft,d.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}T(ft,d,{handler:function(e){var i=this.store,r=!1,n=e.type.toLowerCase().replace("ms",""),s=Ie[n],a=_e[e.pointerType]||e.pointerType,h=a==b,c=H(i,e.pointerId,"pointerId");s&u&&(e.button===0||h)?c<0&&(i.push(e),c=i.length-1):s&(o|p)&&(r=!0),!(c<0)&&(i[c]=e,this.callback(this.manager,s,{pointers:i,changedPointers:[e],pointerType:a,srcEvent:e}),r&&i.splice(c,1))}});var ye={touchstart:u,touchmove:M,touchend:o,touchcancel:p},Pe="touchstart",Ne="touchstart touchmove touchend touchcancel";function Vt(){this.evTarget=Pe,this.evWin=Ne,this.started=!1,d.apply(this,arguments)}T(Vt,d,{handler:function(e){var i=ye[e.type];if(i===u&&(this.started=!0),!!this.started){var r=Oe.call(this,e,i);i&(o|p)&&r[0].length-r[1].length===0&&(this.started=!1),this.callback(this.manager,i,{pointers:r[0],changedPointers:r[1],pointerType:b,srcEvent:e})}}});function Oe(t,e){var i=B(t.touches),r=B(t.changedTouches);return e&(o|p)&&(i=Dt(i.concat(r),"identifier")),[i,r]}var Ae={touchstart:u,touchmove:M,touchend:o,touchcancel:p},Ce="touchstart touchmove touchend touchcancel";function tt(){this.evTarget=Ce,this.targetIds={},d.apply(this,arguments)}T(tt,d,{handler:function(e){var i=Ae[e.type],r=Se.call(this,e,i);r&&this.callback(this.manager,i,{pointers:r[0],changedPointers:r[1],pointerType:b,srcEvent:e})}});function Se(t,e){var i=B(t.touches),r=this.targetIds;if(e&(u|M)&&i.length===1)return r[i[0].identifier]=!0,[i,i];var n,s,a=B(t.changedTouches),h=[],c=this.target;if(s=i.filter(function(f){return St(f.target,c)}),e===u)for(n=0;n<s.length;)r[s[n].identifier]=!0,n++;for(n=0;n<a.length;)r[a[n].identifier]&&h.push(a[n]),e&(o|p)&&delete r[a[n].identifier],n++;if(h.length)return[Dt(s.concat(h),"identifier"),h]}var De=2500,Wt=25;function vt(){d.apply(this,arguments);var t=ht(this.handler,this);this.touch=new tt(this.manager,t),this.mouse=new $(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}T(vt,d,{handler:function(e,i,r){var n=r.pointerType==b,s=r.pointerType==lt;if(!(s&&r.sourceCapabilities&&r.sourceCapabilities.firesTouchEvents)){if(n)Me.call(this,i,r);else if(s&&Ue.call(this,r))return;this.callback(e,i,r)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});function Me(t,e){t&u?(this.primaryTouch=e.changedPointers[0].identifier,qt.call(this,e)):t&(o|p)&&qt.call(this,e)}function qt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY};this.lastTouches.push(i);var r=this.lastTouches,n=function(){var s=r.indexOf(i);s>-1&&r.splice(s,1)};setTimeout(n,De)}}function Ue(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,r=0;r<this.lastTouches.length;r++){var n=this.lastTouches[r],s=Math.abs(e-n.x),a=Math.abs(i-n.y);if(s<=Wt&&a<=Wt)return!0}return!1}var wt=J($t.style,"touchAction"),kt=wt!==v,Gt="compute",zt="auto",pt="manipulation",R="none",q="pan-x",w="pan-y",et=Le();function Tt(t,e){this.manager=t,this.set(e)}Tt.prototype={set:function(t){t==Gt&&(t=this.compute()),kt&&this.manager.element.style&&et[t]&&(this.manager.element.style[wt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return P(this.manager.recognizers,function(e){ct(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),Re(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented){e.preventDefault();return}var r=this.actions,n=D(r,R)&&!et[R],s=D(r,w)&&!et[w],a=D(r,q)&&!et[q];if(n){var h=t.pointers.length===1,c=t.distance<2,f=t.deltaTime<250;if(h&&c&&f)return}if(!(a&&s)&&(n||s&&i&I||a&&i&U))return this.preventSrc(e)},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};function Re(t){if(D(t,R))return R;var e=D(t,q),i=D(t,w);return e&&i?R:e||i?e?q:w:D(t,pt)?pt:zt}function Le(){if(!kt)return!1;var t={},e=l.CSS&&l.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(i){t[i]=e?l.CSS.supports("touch-action",i):!0}),t}var it=1,m=2,Y=4,A=8,N=A,k=16,_=32;function O(t){this.options=g({},this.defaults,t||{}),this.id=re(),this.manager=null,this.options.enable=Ct(this.options.enable,!0),this.state=it,this.simultaneous={},this.requireFail=[]}O.prototype={defaults:{},set:function(t){return g(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(x(t,"recognizeWith",this))return this;var e=this.simultaneous;return t=rt(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return x(t,"dropRecognizeWith",this)?this:(t=rt(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(x(t,"requireFailure",this))return this;var e=this.requireFail;return t=rt(t,this),H(e,t)===-1&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(x(t,"dropRequireFailure",this))return this;t=rt(t,this);var e=H(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,i=this.state;function r(n){e.manager.emit(n,t)}i<A&&r(e.options.event+Zt(i)),r(e.options.event),t.additionalEvent&&r(t.additionalEvent),i>=A&&r(e.options.event+Zt(i))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=_},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(_|it)))return!1;t++}return!0},recognize:function(t){var e=g({},t);if(!ct(this.options.enable,[this,e])){this.reset(),this.state=_;return}this.state&(N|k|_)&&(this.state=it),this.state=this.process(e),this.state&(m|Y|A|k)&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}};function Zt(t){return t&k?"cancel":t&A?"end":t&Y?"move":t&m?"start":""}function Bt(t){return t==W?"down":t==V?"up":t==X?"left":t==F?"right":""}function rt(t,e){var i=e.manager;return i?i.get(t):t}function E(){O.apply(this,arguments)}T(E,O,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return e===0||t.pointers.length===e},process:function(t){var e=this.state,i=t.eventType,r=e&(m|Y),n=this.attrTest(t);return r&&(i&p||!n)?e|k:r||n?i&o?e|A:e&m?e|Y:m:_}});function nt(){E.apply(this,arguments),this.pX=null,this.pY=null}T(nt,E,{defaults:{event:"pan",threshold:10,pointers:1,direction:Rt},getTouchAction:function(){var t=this.options.direction,e=[];return t&I&&e.push(w),t&U&&e.push(q),e},directionTest:function(t){var e=this.options,i=!0,r=t.distance,n=t.direction,s=t.deltaX,a=t.deltaY;return n&e.direction||(e.direction&I?(n=s===0?Q:s<0?X:F,i=s!=this.pX,r=Math.abs(t.deltaX)):(n=a===0?Q:a<0?V:W,i=a!=this.pY,r=Math.abs(t.deltaY))),t.direction=n,i&&r>e.threshold&&n&e.direction},attrTest:function(t){return E.prototype.attrTest.call(this,t)&&(this.state&m||!(this.state&m)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Bt(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}});function dt(){E.apply(this,arguments)}T(dt,E,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[R]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&m)},emit:function(t){if(t.scale!==1){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}});function mt(){O.apply(this,arguments),this._timer=null,this._input=null}T(mt,O,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[zt]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,r=t.distance<e.threshold,n=t.deltaTime>e.time;if(this._input=t,!r||!i||t.eventType&(o|p)&&!n)this.reset();else if(t.eventType&u)this.reset(),this._timer=ot(function(){this.state=N,this.tryEmit()},e.time,this);else if(t.eventType&o)return N;return _},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===N&&(t&&t.eventType&o?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=at(),this.manager.emit(this.options.event,this._input)))}});function Et(){E.apply(this,arguments)}T(Et,E,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[R]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&m)}});function gt(){E.apply(this,arguments)}T(gt,E,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:I|U,pointers:1},getTouchAction:function(){return nt.prototype.getTouchAction.call(this)},attrTest:function(t){var e=this.options.direction,i;return e&(I|U)?i=t.overallVelocity:e&I?i=t.overallVelocityX:e&U&&(i=t.overallVelocityY),this._super.attrTest.call(this,t)&&e&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&S(i)>this.options.velocity&&t.eventType&o},emit:function(t){var e=Bt(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}});function st(){O.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}T(st,O,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[pt]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,r=t.distance<e.threshold,n=t.deltaTime<e.time;if(this.reset(),t.eventType&u&&this.count===0)return this.failTimeout();if(r&&n&&i){if(t.eventType!=o)return this.failTimeout();var s=this.pTime?t.timeStamp-this.pTime<e.interval:!0,a=!this.pCenter||K(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,!a||!s?this.count=1:this.count+=1,this._input=t;var h=this.count%e.taps;if(h===0)return this.hasRequireFailures()?(this._timer=ot(function(){this.state=N,this.tryEmit()},e.interval,this),m):N}return _},failTimeout:function(){return this._timer=ot(function(){this.state=_},this.options.interval,this),_},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==N&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}});function C(t,e){return e=e||{},e.recognizers=Ct(e.recognizers,C.defaults.preset),new It(t,e)}C.VERSION="2.0.7",C.defaults={domEvents:!1,touchAction:Gt,enable:!0,inputTarget:null,inputClass:null,preset:[[Et,{enable:!1}],[dt,{enable:!1},["rotate"]],[gt,{direction:I}],[nt,{direction:I},["swipe"]],[st],[st,{event:"doubletap",taps:2},["tap"]],[mt]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var xe=1,Jt=2;function It(t,e){this.options=g({},C.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=le(this),this.touchAction=new Tt(this,this.options.touchAction),Qt(this,!0),P(this.options.recognizers,function(i){var r=this.add(new i[0](i[1]));i[2]&&r.recognizeWith(i[2]),i[3]&&r.requireFailure(i[3])},this)}It.prototype={set:function(t){return g(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?Jt:xe},recognize:function(t){var e=this.session;if(!e.stopped){this.touchAction.preventDefaults(t);var i,r=this.recognizers,n=e.curRecognizer;(!n||n&&n.state&N)&&(n=e.curRecognizer=null);for(var s=0;s<r.length;)i=r[s],e.stopped!==Jt&&(!n||i==n||i.canRecognizeWith(n))?i.recognize(t):i.reset(),!n&&i.state&(m|Y|A)&&(n=e.curRecognizer=i),s++}},get:function(t){if(t instanceof O)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event==t)return e[i];return null},add:function(t){if(x(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(x(t,"remove",this))return this;if(t=this.get(t),t){var e=this.recognizers,i=H(e,t);i!==-1&&(e.splice(i,1),this.touchAction.update())}return this},on:function(t,e){if(t!==v&&e!==v){var i=this.handlers;return P(Z(t),function(r){i[r]=i[r]||[],i[r].push(e)}),this}},off:function(t,e){if(t!==v){var i=this.handlers;return P(Z(t),function(r){e?i[r]&&i[r].splice(H(i[r],e),1):delete i[r]}),this}},emit:function(t,e){this.options.domEvents&&He(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(!(!i||!i.length)){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<i.length;)i[r](e),r++}},destroy:function(){this.element&&Qt(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}};function Qt(t,e){var i=t.element;if(i.style){var r;P(t.options.cssProps,function(n,s){r=J(i.style,s),e?(t.oldCssProps[r]=i.style[r],i.style[r]=n):i.style[r]=t.oldCssProps[r]||""}),e||(t.oldCssProps={})}}function He(t,e){var i=Pt.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}g(C,{INPUT_START:u,INPUT_MOVE:M,INPUT_END:o,INPUT_CANCEL:p,STATE_POSSIBLE:it,STATE_BEGAN:m,STATE_CHANGED:Y,STATE_ENDED:A,STATE_RECOGNIZED:N,STATE_CANCELLED:k,STATE_FAILED:_,DIRECTION_NONE:Q,DIRECTION_LEFT:X,DIRECTION_RIGHT:F,DIRECTION_UP:V,DIRECTION_DOWN:W,DIRECTION_HORIZONTAL:I,DIRECTION_VERTICAL:U,DIRECTION_ALL:Rt,Manager:It,Input:d,TouchAction:Tt,TouchInput:tt,MouseInput:$,PointerEventInput:ft,TouchMouseInput:vt,SingleTouchInput:Vt,Recognizer:O,AttrRecognizer:E,Tap:st,Pan:nt,Swipe:gt,Pinch:dt,Rotate:Et,Press:mt,on:G,off:z,each:P,merge:ee,extend:At,assign:g,inherit:T,bindFn:ht,prefixed:J});var Ye=typeof l<"u"?l:typeof self<"u"?self:{};Ye.Hammer=C,yt.exports?yt.exports=C:l[Kt]=C})(window,document,"Hammer")}(_t)),_t.exports}be();
