import{g as kn}from"./_commonjsHelpers-BosuxZz1.js";var ye={exports:{}},A={},Gt;function Nn(){if(Gt)return A;Gt=1;/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 * 
 * @license https://formvalidation.io/license
 * @package @form-validation/bundle
 * @version 2.4.0
 */var T,be={exports:{}},C={};be.exports=function(){if(T)return C;T=1;var u={luhn:function(n){for(var e=n.length,t=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],i=0,a=0;e--;)a+=t[i][parseInt(n.charAt(e),10)],i=1-i;return a%10==0&&a>0},mod11And10:function(n){for(var e=n.length,t=5,i=0;i<e;i++)t=(2*(t||10)%11+parseInt(n.charAt(i),10))%10;return t===1},mod37And36:function(n,e){e===void 0&&(e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");for(var t=n.length,i=e.length,a=Math.floor(i/2),o=0;o<t;o++)a=(2*(a||i)%(i+1)+e.indexOf(n.charAt(o)))%i;return a===1},mod97And10:function(n){for(var e=function(o){return o.split("").map(function(d){var f=d.charCodeAt(0);return f>=65&&f<=90?f-55:d}).join("").split("").map(function(d){return parseInt(d,10)})}(n),t=0,i=e.length,a=0;a<i-1;++a)t=10*(t+e[a])%97;return(t+=e[i-1])%97==1},verhoeff:function(n){for(var e=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],t=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],i=n.reverse(),a=0,o=0;o<i.length;o++)a=e[a][t[o%8][i[o]]];return a===0}},l=function(){function n(e,t){this.fields={},this.elements={},this.ee={fns:{},clear:function(){this.fns={}},emit:function(i){for(var a=[],o=1;o<arguments.length;o++)a[o-1]=arguments[o];(this.fns[i]||[]).map(function(d){return d.apply(d,a)})},off:function(i,a){if(this.fns[i]){var o=this.fns[i].indexOf(a);o>=0&&this.fns[i].splice(o,1)}},on:function(i,a){(this.fns[i]=this.fns[i]||[]).push(a)}},this.filter={filters:{},add:function(i,a){(this.filters[i]=this.filters[i]||[]).push(a)},clear:function(){this.filters={}},execute:function(i,a,o){if(!this.filters[i]||!this.filters[i].length)return a;for(var d=a,f=this.filters[i],p=f.length,h=0;h<p;h++)d=f[h].apply(d,o);return d},remove:function(i,a){this.filters[i]&&(this.filters[i]=this.filters[i].filter(function(o){return o!==a}))}},this.plugins={},this.results=new Map,this.validators={},this.form=e,this.fields=t}return n.prototype.on=function(e,t){return this.ee.on(e,t),this},n.prototype.off=function(e,t){return this.ee.off(e,t),this},n.prototype.emit=function(e){for(var t,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];return(t=this.ee).emit.apply(t,function(o,d,f){if(arguments.length===2)for(var p,h=0,g=d.length;h<g;h++)!p&&h in d||(p||(p=Array.prototype.slice.call(d,0,h)),p[h]=d[h]);return o.concat(p||Array.prototype.slice.call(d))}([e],i,!1)),this},n.prototype.registerPlugin=function(e,t){if(this.plugins[e])throw new Error("The plguin ".concat(e," is registered"));return t.setCore(this),t.install(),this.plugins[e]=t,this},n.prototype.deregisterPlugin=function(e){var t=this.plugins[e];return t&&t.uninstall(),delete this.plugins[e],this},n.prototype.enablePlugin=function(e){var t=this.plugins[e];return t&&t.enable(),this},n.prototype.disablePlugin=function(e){var t=this.plugins[e];return t&&t.disable(),this},n.prototype.isPluginEnabled=function(e){var t=this.plugins[e];return!!t&&t.isPluginEnabled()},n.prototype.registerValidator=function(e,t){if(this.validators[e])throw new Error("The validator ".concat(e," is registered"));return this.validators[e]=t,this},n.prototype.registerFilter=function(e,t){return this.filter.add(e,t),this},n.prototype.deregisterFilter=function(e,t){return this.filter.remove(e,t),this},n.prototype.executeFilter=function(e,t,i){return this.filter.execute(e,t,i)},n.prototype.addField=function(e,t){var i=Object.assign({},{selector:"",validators:{}},t);return this.fields[e]=this.fields[e]?{selector:i.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,i.validators)}:i,this.elements[e]=this.queryElements(e),this.emit("core.field.added",{elements:this.elements[e],field:e,options:this.fields[e]}),this},n.prototype.removeField=function(e){if(!this.fields[e])throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"));var t=this.elements[e],i=this.fields[e];return delete this.elements[e],delete this.fields[e],this.emit("core.field.removed",{elements:t,field:e,options:i}),this},n.prototype.validate=function(){var e=this;return this.emit("core.form.validating",{formValidation:this}),this.filter.execute("validate-pre",Promise.resolve(),[]).then(function(){return Promise.all(Object.keys(e.fields).map(function(t){return e.validateField(t)})).then(function(t){switch(!0){case t.indexOf("Invalid")!==-1:return e.emit("core.form.invalid",{formValidation:e}),Promise.resolve("Invalid");case t.indexOf("NotValidated")!==-1:return e.emit("core.form.notvalidated",{formValidation:e}),Promise.resolve("NotValidated");default:return e.emit("core.form.valid",{formValidation:e}),Promise.resolve("Valid")}})})},n.prototype.validateField=function(e){var t=this,i=this.results.get(e);if(i==="Valid"||i==="Invalid")return Promise.resolve(i);this.emit("core.field.validating",e);var a=this.elements[e];if(a.length===0)return this.emit("core.field.valid",e),Promise.resolve("Valid");var o=a[0].getAttribute("type");return o==="radio"||o==="checkbox"||a.length===1?this.validateElement(e,a[0]):Promise.all(a.map(function(d){return t.validateElement(e,d)})).then(function(d){switch(!0){case d.indexOf("Invalid")!==-1:return t.emit("core.field.invalid",e),t.results.set(e,"Invalid"),Promise.resolve("Invalid");case d.indexOf("NotValidated")!==-1:return t.emit("core.field.notvalidated",e),t.results.delete(e),Promise.resolve("NotValidated");default:return t.emit("core.field.valid",e),t.results.set(e,"Valid"),Promise.resolve("Valid")}})},n.prototype.validateElement=function(e,t){var i=this;this.results.delete(e);var a=this.elements[e];if(this.filter.execute("element-ignored",!1,[e,t,a]))return this.emit("core.element.ignored",{element:t,elements:a,field:e}),Promise.resolve("Ignored");var o=this.fields[e].validators;this.emit("core.element.validating",{element:t,elements:a,field:e});var d=Object.keys(o).map(function(f){return function(){return i.executeValidator(e,t,f,o[f])}});return this.waterfall(d).then(function(f){var p=f.indexOf("Invalid")===-1;i.emit("core.element.validated",{element:t,elements:a,field:e,valid:p});var h=t.getAttribute("type");return h!=="radio"&&h!=="checkbox"&&a.length!==1||i.emit(p?"core.field.valid":"core.field.invalid",e),Promise.resolve(p?"Valid":"Invalid")}).catch(function(f){return i.emit("core.element.notvalidated",{element:t,elements:a,field:e}),Promise.resolve(f)})},n.prototype.executeValidator=function(e,t,i,a){var o=this,d=this.elements[e],f=this.filter.execute("validator-name",i,[i,e]);if(a.message=this.filter.execute("validator-message",a.message,[this.locale,e,f]),!this.validators[f]||a.enabled===!1)return this.emit("core.validator.validated",{element:t,elements:d,field:e,result:this.normalizeResult(e,f,{valid:!0}),validator:f}),Promise.resolve("Valid");var p=this.validators[f],h=this.getElementValue(e,t,f);if(!this.filter.execute("field-should-validate",!0,[e,t,h,i]))return this.emit("core.validator.notvalidated",{element:t,elements:d,field:e,validator:i}),Promise.resolve("NotValidated");this.emit("core.validator.validating",{element:t,elements:d,field:e,validator:i});var g=p().validate({element:t,elements:d,field:e,l10n:this.localization,options:a,value:h});if(typeof g.then=="function")return g.then(function(v){var b=o.normalizeResult(e,i,v);return o.emit("core.validator.validated",{element:t,elements:d,field:e,result:b,validator:i}),b.valid?"Valid":"Invalid"});var m=this.normalizeResult(e,i,g);return this.emit("core.validator.validated",{element:t,elements:d,field:e,result:m,validator:i}),Promise.resolve(m.valid?"Valid":"Invalid")},n.prototype.getElementValue=function(e,t,i){var a=function(o,d,f,p){var h=(f.getAttribute("type")||"").toLowerCase(),g=f.tagName.toLowerCase();if(g==="textarea")return f.value;if(g==="select"){var m=f,v=m.selectedIndex;return v>=0?m.options.item(v).value:""}if(g==="input"){if(h==="radio"||h==="checkbox"){var b=p.filter(function(E){return E.checked}).length;return b===0?"":b+""}return f.value}return""}(this.form,0,t,this.elements[e]);return this.filter.execute("field-value",a,[a,e,t,i])},n.prototype.getElements=function(e){return this.elements[e]},n.prototype.getFields=function(){return this.fields},n.prototype.getFormElement=function(){return this.form},n.prototype.getLocale=function(){return this.locale},n.prototype.getPlugin=function(e){return this.plugins[e]},n.prototype.updateFieldStatus=function(e,t,i){var a=this,o=this.elements[e],d=o[0].getAttribute("type");if((d==="radio"||d==="checkbox"?[o[0]]:o).forEach(function(f){return a.updateElementStatus(e,f,t,i)}),i)t==="Invalid"&&(this.emit("core.field.invalid",e),this.results.set(e,"Invalid"));else switch(t){case"NotValidated":this.emit("core.field.notvalidated",e),this.results.delete(e);break;case"Validating":this.emit("core.field.validating",e),this.results.delete(e);break;case"Valid":this.emit("core.field.valid",e),this.results.set(e,"Valid");break;case"Invalid":this.emit("core.field.invalid",e),this.results.set(e,"Invalid")}return this},n.prototype.updateElementStatus=function(e,t,i,a){var o=this,d=this.elements[e],f=this.fields[e].validators,p=a?[a]:Object.keys(f);switch(i){case"NotValidated":p.forEach(function(h){return o.emit("core.validator.notvalidated",{element:t,elements:d,field:e,validator:h})}),this.emit("core.element.notvalidated",{element:t,elements:d,field:e});break;case"Validating":p.forEach(function(h){return o.emit("core.validator.validating",{element:t,elements:d,field:e,validator:h})}),this.emit("core.element.validating",{element:t,elements:d,field:e});break;case"Valid":p.forEach(function(h){return o.emit("core.validator.validated",{element:t,elements:d,field:e,result:{message:f[h].message,valid:!0},validator:h})}),this.emit("core.element.validated",{element:t,elements:d,field:e,valid:!0});break;case"Invalid":p.forEach(function(h){return o.emit("core.validator.validated",{element:t,elements:d,field:e,result:{message:f[h].message,valid:!1},validator:h})}),this.emit("core.element.validated",{element:t,elements:d,field:e,valid:!1})}return this},n.prototype.resetForm=function(e){var t=this;return Object.keys(this.fields).forEach(function(i){return t.resetField(i,e)}),this.emit("core.form.reset",{formValidation:this,reset:e}),this},n.prototype.resetField=function(e,t){if(t){var i=this.elements[e],a=i[0].getAttribute("type");i.forEach(function(o){a==="radio"||a==="checkbox"?(o.removeAttribute("selected"),o.removeAttribute("checked"),o.checked=!1):(o.setAttribute("value",""),(o instanceof HTMLInputElement||o instanceof HTMLTextAreaElement)&&(o.value=""))})}return this.updateFieldStatus(e,"NotValidated"),this.emit("core.field.reset",{field:e,reset:t}),this},n.prototype.revalidateField=function(e){return this.fields[e]?(this.updateFieldStatus(e,"NotValidated"),this.validateField(e)):Promise.resolve("Ignored")},n.prototype.disableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!1,e,t),this.emit("core.validator.disabled",{elements:i,field:e,formValidation:this,validator:t}),this},n.prototype.enableValidator=function(e,t){if(!this.fields[e])return this;var i=this.elements[e];return this.toggleValidator(!0,e,t),this.emit("core.validator.enabled",{elements:i,field:e,formValidation:this,validator:t}),this},n.prototype.updateValidatorOption=function(e,t,i,a){return this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][i]=a),this},n.prototype.setFieldOptions=function(e,t){return this.fields[e]=t,this},n.prototype.destroy=function(){var e=this;return Object.keys(this.plugins).forEach(function(t){return e.plugins[t].uninstall()}),this.ee.clear(),this.filter.clear(),this.results.clear(),this.plugins={},this},n.prototype.setLocale=function(e,t){return this.locale=e,this.localization=t,this},n.prototype.waterfall=function(e){return e.reduce(function(t,i){return t.then(function(a){return i().then(function(o){return a.push(o),a})})},Promise.resolve([]))},n.prototype.queryElements=function(e){var t=this.fields[e].selector?this.fields[e].selector.charAt(0)==="#"?'[id="'.concat(this.fields[e].selector.substring(1),'"]'):this.fields[e].selector:'[name="'.concat(e.replace(/"/g,'\\"'),'"]');return[].slice.call(this.form.querySelectorAll(t))},n.prototype.normalizeResult=function(e,t,i){var a=this.fields[e].validators[t];return Object.assign({},i,{message:i.message||(a?a.message:"")||(this.localization&&this.localization[t]&&this.localization[t].default?this.localization[t].default:"")||"The field ".concat(e," is not valid")})},n.prototype.toggleValidator=function(e,t,i){var a=this,o=this.fields[t].validators;return i&&o&&o[i]?this.fields[t].validators[i].enabled=e:i||Object.keys(o).forEach(function(d){return a.fields[t].validators[d].enabled=e}),this.updateFieldStatus(t,"NotValidated",i)},n}(),c=function(){function n(e){this.opts=e,this.isEnabled=!0}return n.prototype.setCore=function(e){return this.core=e,this},n.prototype.enable=function(){return this.isEnabled=!0,this.onEnabled(),this},n.prototype.disable=function(){return this.isEnabled=!1,this.onDisabled(),this},n.prototype.isPluginEnabled=function(){return this.isEnabled},n.prototype.onEnabled=function(){},n.prototype.onDisabled=function(){},n.prototype.install=function(){},n.prototype.uninstall=function(){},n}(),s=function(n,e){var t=n.matches||n.webkitMatchesSelector||n.mozMatchesSelector||n.msMatchesSelector;return t?t.call(n,e):[].slice.call(n.parentElement.querySelectorAll(e)).indexOf(n)>=0},r={call:function(n,e){if(typeof n=="function")return n.apply(this,e);if(typeof n=="string"){var t=n;t.substring(t.length-2)==="()"&&(t=t.substring(0,t.length-2));for(var i=t.split("."),a=i.pop(),o=window,d=0,f=i;d<f.length;d++)o=o[f[d]];return o[a]===void 0?null:o[a].apply(this,e)}},classSet:function(n,e){var t=[],i=[];Object.keys(e).forEach(function(a){a&&(e[a]?t.push(a):i.push(a))}),i.forEach(function(a){return function(o,d){d.split(" ").forEach(function(f){o.classList?o.classList.remove(f):o.className=o.className.replace(f,"")})}(n,a)}),t.forEach(function(a){return function(o,d){d.split(" ").forEach(function(f){o.classList?o.classList.add(f):" ".concat(o.className," ").indexOf(" ".concat(f," "))&&(o.className+=" ".concat(f))})}(n,a)})},closest:function(n,e){for(var t=n;t&&!s(t,e);)t=t.parentElement;return t},fetch:function(n,e){return new Promise(function(t,i){var a,o=Object.assign({},{crossDomain:!1,headers:{},method:"GET",params:{}},e),d=Object.keys(o.params).map(function(v){return"".concat(encodeURIComponent(v),"=").concat(encodeURIComponent(o.params[v]))}).join("&"),f=n.indexOf("?")>-1,p=o.method==="GET"?"".concat(n).concat(f?"&":"?").concat(d):n;if(o.crossDomain){var h=document.createElement("script"),g="___FormValidationFetch_".concat(Array(12).fill("").map(function(v){return Math.random().toString(36).charAt(2)}).join(""),"___");window[g]=function(v){delete window[g],t(v)},h.src="".concat(p).concat(f?"&":"?","callback=").concat(g),h.async=!0,h.addEventListener("load",function(){h.parentNode.removeChild(h)}),h.addEventListener("error",function(){return i}),document.head.appendChild(h)}else{var m=new XMLHttpRequest;m.open(o.method,p),m.setRequestHeader("X-Requested-With","XMLHttpRequest"),o.method==="POST"&&m.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),Object.keys(o.headers).forEach(function(v){return m.setRequestHeader(v,o.headers[v])}),m.addEventListener("load",function(){t(JSON.parse(this.responseText))}),m.addEventListener("error",function(){return i}),m.send((a=o.params,Object.keys(a).map(function(v){return"".concat(encodeURIComponent(v),"=").concat(encodeURIComponent(a[v]))}).join("&")))}})},format:function(n,e){var t=Array.isArray(e)?e:[e],i=n;return t.forEach(function(a){i=i.replace("%s",a)}),i},hasClass:function(n,e){return n.classList?n.classList.contains(e):new RegExp("(^| )".concat(e,"( |$)"),"gi").test(n.className)},isValidDate:function(n,e,t,i){if(isNaN(n)||isNaN(e)||isNaN(t)||n<1e3||n>9999||e<=0||e>12||t<=0||t>[31,n%400==0||n%100!=0&&n%4==0?29:28,31,30,31,30,31,31,30,31,30,31][e-1])return!1;if(i===!0){var a=new Date,o=a.getFullYear(),d=a.getMonth(),f=a.getDate();return n<o||n===o&&e-1<d||n===o&&e-1===d&&t<f}return!0},removeUndefined:function(n){return n?Object.entries(n).reduce(function(e,t){var i=t[0],a=t[1];return a===void 0||(e[i]=a),e},{}):{}}};return C.Plugin=c,C.algorithms=u,C.formValidation=function(n,e){var t=Object.assign({},{fields:{},locale:"en_US",plugins:{},init:function(a){}},e),i=new l(n,t.fields);return i.setLocale(t.locale,t.localization),Object.keys(t.plugins).forEach(function(a){return i.registerPlugin(a,t.plugins[a])}),t.init(i),Object.keys(t.fields).forEach(function(a){return i.addField(a,t.fields[a])}),i},C.utils=r,C}();var Ee,y=be.exports,xe={exports:{}},D={};xe.exports=function(){if(Ee)return D;Ee=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;return n.opts=r||{},n.validatorNameFilter=n.getValidatorName.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){this.core.registerFilter("validator-name",this.validatorNameFilter)},s.prototype.uninstall=function(){this.core.deregisterFilter("validator-name",this.validatorNameFilter)},s.prototype.getValidatorName=function(r,n){return this.isEnabled&&this.opts[r]||r},s}(y.Plugin);return D.Alias=l,D}();var Ve,Jt=xe.exports,we={exports:{}},R={};we.exports=function(){if(Ve)return R;Ve=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(){var r=c.call(this,{})||this;return r.elementValidatedHandler=r.onElementValidated.bind(r),r.fieldValidHandler=r.onFieldValid.bind(r),r.fieldInvalidHandler=r.onFieldInvalid.bind(r),r.messageDisplayedHandler=r.onMessageDisplayed.bind(r),r}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){this.core.on("core.field.valid",this.fieldValidHandler).on("core.field.invalid",this.fieldInvalidHandler).on("core.element.validated",this.elementValidatedHandler).on("plugins.message.displayed",this.messageDisplayedHandler)},s.prototype.uninstall=function(){this.core.off("core.field.valid",this.fieldValidHandler).off("core.field.invalid",this.fieldInvalidHandler).off("core.element.validated",this.elementValidatedHandler).off("plugins.message.displayed",this.messageDisplayedHandler)},s.prototype.onElementValidated=function(r){r.valid&&(r.element.setAttribute("aria-invalid","false"),r.element.removeAttribute("aria-describedby"))},s.prototype.onFieldValid=function(r){var n=this.core.getElements(r);n&&n.forEach(function(e){e.setAttribute("aria-invalid","false"),e.removeAttribute("aria-describedby")})},s.prototype.onFieldInvalid=function(r){var n=this.core.getElements(r);n&&n.forEach(function(e){return e.setAttribute("aria-invalid","true")})},s.prototype.onMessageDisplayed=function(r){r.messageElement.setAttribute("role","alert"),r.messageElement.setAttribute("aria-hidden","false");var n=this.core.getElements(r.field),e=n.indexOf(r.element),t="js-fv-".concat(r.field,"-").concat(e,"-").concat(Date.now(),"-message");r.messageElement.setAttribute("id",t),r.element.setAttribute("aria-describedby",t);var i=r.element.getAttribute("type");i!=="radio"&&i!=="checkbox"||n.forEach(function(a){return a.setAttribute("aria-describedby",t)})},s}(y.Plugin);return R.Aria=l,R}();var Ae,Kt=we.exports,Oe={exports:{}},z={};Oe.exports=function(){if(Ae)return z;Ae=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;return n.addedFields=new Map,n.opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},r),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){var r=this;this.parsePlugins();var n=this.parseOptions();Object.keys(n).forEach(function(e){r.addedFields.has(e)||r.addedFields.set(e,!0),r.core.addField(e,n[e])}),this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},s.prototype.uninstall=function(){this.addedFields.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},s.prototype.onFieldAdded=function(r){var n=this,e=r.elements;e&&e.length!==0&&!this.addedFields.has(r.field)&&(this.addedFields.set(r.field,!0),e.forEach(function(t){var i=n.parseElement(t);if(!n.isEmptyOption(i)){var a={selector:r.options.selector,validators:Object.assign({},r.options.validators||{},i.validators)};n.core.setFieldOptions(r.field,a)}}))},s.prototype.onFieldRemoved=function(r){r.field&&this.addedFields.has(r.field)&&this.addedFields.delete(r.field)},s.prototype.parseOptions=function(){var r=this,n=this.opts.prefix,e={},t=this.core.getFields(),i=this.core.getFormElement();return[].slice.call(i.querySelectorAll("[name], [".concat(n,"field]"))).forEach(function(a){var o=r.parseElement(a);if(!r.isEmptyOption(o)){var d=a.getAttribute("name")||a.getAttribute("".concat(n,"field"));e[d]=Object.assign({},e[d],o)}}),Object.keys(e).forEach(function(a){Object.keys(e[a].validators).forEach(function(o){e[a].validators[o].enabled=e[a].validators[o].enabled||!1,t[a]&&t[a].validators&&t[a].validators[o]&&Object.assign(e[a].validators[o],t[a].validators[o])})}),Object.assign({},t,e)},s.prototype.createPluginInstance=function(r,n){for(var e=r.split("."),t=window||this,i=0,a=e.length;i<a;i++)t=t[e[i]];if(typeof t!="function")throw new Error("the plugin ".concat(r," doesn't exist"));return new t(n)},s.prototype.parsePlugins=function(){for(var r,n=this,e=this.core.getFormElement(),t=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),i=e.attributes.length,a={},o=0;o<i;o++){var d=e.attributes[o].name,f=e.attributes[o].value,p=t.exec(d);if(p&&p.length===4){var h=this.toCamelCase(p[1]);a[h]=Object.assign({},p[3]?((r={})[this.toCamelCase(p[3])]=f,r):{enabled:f===""||f==="true"},a[h])}}Object.keys(a).forEach(function(g){var m=a[g],v=m.enabled,b=m.class;if(v&&b){delete m.enabled,delete m.clazz;var E=n.createPluginInstance(b,m);n.core.registerPlugin(g,E)}})},s.prototype.isEmptyOption=function(r){var n=r.validators;return Object.keys(n).length===0&&n.constructor===Object},s.prototype.parseElement=function(r){for(var n=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),e=r.attributes.length,t={},i=r.getAttribute("type"),a=0;a<e;a++){var o=r.attributes[a].name,d=r.attributes[a].value;if(this.opts.html5Input)switch(!0){case o==="minlength":t.stringLength=Object.assign({},{enabled:!0,min:parseInt(d,10)},t.stringLength);break;case o==="maxlength":t.stringLength=Object.assign({},{enabled:!0,max:parseInt(d,10)},t.stringLength);break;case o==="pattern":t.regexp=Object.assign({},{enabled:!0,regexp:d},t.regexp);break;case o==="required":t.notEmpty=Object.assign({},{enabled:!0},t.notEmpty);break;case(o==="type"&&d==="color"):t.color=Object.assign({},{enabled:!0,type:"hex"},t.color);break;case(o==="type"&&d==="email"):t.emailAddress=Object.assign({},{enabled:!0},t.emailAddress);break;case(o==="type"&&d==="url"):t.uri=Object.assign({},{enabled:!0},t.uri);break;case(o==="type"&&d==="range"):t.between=Object.assign({},{enabled:!0,max:parseFloat(r.getAttribute("max")),min:parseFloat(r.getAttribute("min"))},t.between);break;case(o==="min"&&i!=="date"&&i!=="range"):t.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(d)},t.greaterThan);break;case(o==="max"&&i!=="date"&&i!=="range"):t.lessThan=Object.assign({},{enabled:!0,max:parseFloat(d)},t.lessThan)}var f=n.exec(o);if(f&&f.length===4){var p=this.toCamelCase(f[1]);t[p]||(t[p]={}),f[3]?t[p][this.toCamelCase(f[3])]=this.normalizeValue(d):t[p].enabled===!0&&t[p].enabled===!1||(t[p].enabled=d===""||d==="true")}}return{validators:t}},s.prototype.normalizeValue=function(r){return r==="true"||r===""||r!=="false"&&r},s.prototype.toUpperCase=function(r){return r.charAt(1).toUpperCase()},s.prototype.toCamelCase=function(r){return r.replace(/-./g,this.toUpperCase)},s}(y.Plugin);return z.Declarative=l,z}();var Fe,Wt=Oe.exports,Ce={exports:{}},U={};Ce.exports=function(){if(Fe)return U;Fe=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(){var r=c.call(this,{})||this;return r.onValidHandler=r.onFormValid.bind(r),r}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){if(this.core.getFormElement().querySelectorAll('[type="submit"][name="submit"]').length)throw new Error("Do not use `submit` for the name attribute of submit button");this.core.on("core.form.valid",this.onValidHandler)},s.prototype.uninstall=function(){this.core.off("core.form.valid",this.onValidHandler)},s.prototype.onFormValid=function(){var r=this.core.getFormElement();this.isEnabled&&r instanceof HTMLFormElement&&r.submit()},s}(y.Plugin);return U.DefaultSubmit=l,U}();var He,Qt=Ce.exports,Ie={exports:{}},B={};Ie.exports=function(){if(He)return B;He=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;return n.opts=r||{},n.triggerExecutedHandler=n.onTriggerExecuted.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){this.core.on("plugins.trigger.executed",this.triggerExecutedHandler)},s.prototype.uninstall=function(){this.core.off("plugins.trigger.executed",this.triggerExecutedHandler)},s.prototype.onTriggerExecuted=function(r){if(this.isEnabled&&this.opts[r.field])for(var n=0,e=this.opts[r.field].split(" ");n<e.length;n++){var t=e[n].trim();this.opts[t]&&this.core.revalidateField(t)}},s}(y.Plugin);return B.Dependency=l,B}();var _e,en=Ie.exports,Se={exports:{}},q={};Se.exports=function(){if(_e)return q;_e=1;var u=y,l=function(r,n){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},l(r,n)},c=u.utils.removeUndefined,s=function(r){function n(e){var t=r.call(this,e)||this;return t.opts=Object.assign({},{excluded:n.defaultIgnore},c(e)),t.ignoreValidationFilter=t.ignoreValidation.bind(t),t}return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}l(e,t),e.prototype=t===null?Object.create(t):(i.prototype=t.prototype,new i)}(n,r),n.defaultIgnore=function(e,t,i){var a=!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length),o=t.getAttribute("disabled");return o===""||o==="disabled"||t.getAttribute("type")==="hidden"||!a},n.prototype.install=function(){this.core.registerFilter("element-ignored",this.ignoreValidationFilter)},n.prototype.uninstall=function(){this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)},n.prototype.ignoreValidation=function(e,t,i){return!!this.isEnabled&&this.opts.excluded.apply(this,[e,t,i])},n}(u.Plugin);return q.Excluded=s,q}();var Pe,tn=Se.exports,je={exports:{}},Y={};je.exports=function(){if(Pe)return Y;Pe=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;return n.statuses=new Map,n.opts=Object.assign({},{onStatusChanged:function(){}},r),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},s.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},s.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every(function(r){return r==="Valid"||r==="NotValidated"||r==="Ignored"})},s.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},s.prototype.onFieldAdded=function(r){this.statuses.set(r.field,"NotValidated")},s.prototype.onFieldRemoved=function(r){this.statuses.has(r.field)&&this.statuses.delete(r.field),this.handleStatusChanged(this.areFieldsValid())},s.prototype.onElementValidating=function(r){this.statuses.set(r.field,"Validating"),this.handleStatusChanged(!1)},s.prototype.onElementValidated=function(r){this.statuses.set(r.field,r.valid?"Valid":"Invalid"),r.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},s.prototype.onElementNotValidated=function(r){this.statuses.set(r.field,"NotValidated"),this.handleStatusChanged(!1)},s.prototype.onElementIgnored=function(r){this.statuses.set(r.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},s.prototype.handleStatusChanged=function(r){this.isEnabled&&this.opts.onStatusChanged(r)},s}(y.Plugin);return Y.FieldStatus=l,Y}();var ke,nn=je.exports,Ne={exports:{}},$={},Le={exports:{}},Z={};Le.exports=function(){if(ke)return Z;ke=1;var u=y,l=function(r,n){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},l(r,n)},c=u.utils.classSet,s=function(r){function n(e){var t=r.call(this,e)||this;return t.useDefaultContainer=!1,t.messages=new Map,t.defaultContainer=document.createElement("div"),t.useDefaultContainer=!e||!e.container,t.opts=Object.assign({},{container:function(i,a){return t.defaultContainer}},e),t.elementIgnoredHandler=t.onElementIgnored.bind(t),t.fieldAddedHandler=t.onFieldAdded.bind(t),t.fieldRemovedHandler=t.onFieldRemoved.bind(t),t.validatorValidatedHandler=t.onValidatorValidated.bind(t),t.validatorNotValidatedHandler=t.onValidatorNotValidated.bind(t),t}return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}l(e,t),e.prototype=t===null?Object.create(t):(i.prototype=t.prototype,new i)}(n,r),n.getClosestContainer=function(e,t,i){for(var a=e;a&&a!==t&&(a=a.parentElement,!i.test(a.className)););return a},n.prototype.install=function(){this.useDefaultContainer&&this.core.getFormElement().appendChild(this.defaultContainer),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.validator.notvalidated",this.validatorNotValidatedHandler)},n.prototype.uninstall=function(){this.useDefaultContainer&&this.core.getFormElement().removeChild(this.defaultContainer),this.messages.forEach(function(e){return e.parentNode.removeChild(e)}),this.messages.clear(),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.validator.notvalidated",this.validatorNotValidatedHandler)},n.prototype.onEnabled=function(){this.messages.forEach(function(e,t,i){c(t,{"fv-plugins-message-container--enabled":!0,"fv-plugins-message-container--disabled":!1})})},n.prototype.onDisabled=function(){this.messages.forEach(function(e,t,i){c(t,{"fv-plugins-message-container--enabled":!1,"fv-plugins-message-container--disabled":!0})})},n.prototype.onFieldAdded=function(e){var t=this,i=e.elements;i&&(i.forEach(function(a){var o=t.messages.get(a);o&&(o.parentNode.removeChild(o),t.messages.delete(a))}),this.prepareFieldContainer(e.field,i))},n.prototype.onFieldRemoved=function(e){var t=this;if(e.elements.length&&e.field){var i=e.elements[0].getAttribute("type");(i==="radio"||i==="checkbox"?[e.elements[0]]:e.elements).forEach(function(a){if(t.messages.has(a)){var o=t.messages.get(a);o.parentNode.removeChild(o),t.messages.delete(a)}})}},n.prototype.prepareFieldContainer=function(e,t){var i=this;if(t.length){var a=t[0].getAttribute("type");a==="radio"||a==="checkbox"?this.prepareElementContainer(e,t[0],t):t.forEach(function(o){return i.prepareElementContainer(e,o,t)})}},n.prototype.prepareElementContainer=function(e,t,i){var a;if(typeof this.opts.container=="string"){var o=this.opts.container.charAt(0)==="#"?'[id="'.concat(this.opts.container.substring(1),'"]'):this.opts.container;a=this.core.getFormElement().querySelector(o)}else a=this.opts.container(e,t);var d=document.createElement("div");a.appendChild(d),c(d,{"fv-plugins-message-container":!0,"fv-plugins-message-container--enabled":this.isEnabled,"fv-plugins-message-container--disabled":!this.isEnabled}),this.core.emit("plugins.message.placed",{element:t,elements:i,field:e,messageElement:d}),this.messages.set(t,d)},n.prototype.getMessage=function(e){return typeof e.message=="string"?e.message:e.message[this.core.getLocale()]},n.prototype.onValidatorValidated=function(e){var t,i=e.elements,a=e.element.getAttribute("type"),o=(a==="radio"||a==="checkbox")&&i.length>0?i[0]:e.element;if(this.messages.has(o)){var d=this.messages.get(o),f=d.querySelector('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"][data-validator="').concat(e.validator.replace(/"/g,'\\"'),'"]'));if(f||e.result.valid)f&&!e.result.valid?(f.innerHTML=this.getMessage(e.result),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:f,meta:e.result.meta,validator:e.validator})):f&&e.result.valid&&d.removeChild(f);else{var p=document.createElement("div");p.innerHTML=this.getMessage(e.result),p.setAttribute("data-field",e.field),p.setAttribute("data-validator",e.validator),this.opts.clazz&&c(p,((t={})[this.opts.clazz]=!0,t)),d.appendChild(p),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:p,meta:e.result.meta,validator:e.validator})}}},n.prototype.onValidatorNotValidated=function(e){var t=e.elements,i=e.element.getAttribute("type"),a=i==="radio"||i==="checkbox"?t[0]:e.element;if(this.messages.has(a)){var o=this.messages.get(a),d=o.querySelector('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"][data-validator="').concat(e.validator.replace(/"/g,'\\"'),'"]'));d&&o.removeChild(d)}},n.prototype.onElementIgnored=function(e){var t=e.elements,i=e.element.getAttribute("type"),a=i==="radio"||i==="checkbox"?t[0]:e.element;if(this.messages.has(a)){var o=this.messages.get(a);[].slice.call(o.querySelectorAll('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"]'))).forEach(function(d){o.removeChild(d)})}},n}(u.Plugin);return Z.Message=s,Z}();var Me,Te=Le.exports;Ne.exports=function(){if(Me)return $;Me=1;var u=y,l=Te,c=function(e,t){return c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,a){i.__proto__=a}||function(i,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(i[o]=a[o])},c(e,t)},s=u.utils.classSet,r=u.utils.closest,n=function(e){function t(i){var a=e.call(this,i)||this;return a.results=new Map,a.containers=new Map,a.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},i),a.elementIgnoredHandler=a.onElementIgnored.bind(a),a.elementValidatingHandler=a.onElementValidating.bind(a),a.elementValidatedHandler=a.onElementValidated.bind(a),a.elementNotValidatedHandler=a.onElementNotValidated.bind(a),a.iconPlacedHandler=a.onIconPlaced.bind(a),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.fieldRemovedHandler=a.onFieldRemoved.bind(a),a.messagePlacedHandler=a.onMessagePlaced.bind(a),a}return function(i,a){if(typeof a!="function"&&a!==null)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function o(){this.constructor=i}c(i,a),i.prototype=a===null?Object.create(a):(o.prototype=a.prototype,new o)}(t,e),t.prototype.install=function(){var i,a=this;s(this.core.getFormElement(),((i={})[this.opts.formClass]=!0,i["fv-plugins-framework"]=!0,i)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.registerPlugin(t.MESSAGE_PLUGIN,new l.Message({clazz:this.opts.messageClass,container:function(o,d){var f=typeof a.opts.rowSelector=="string"?a.opts.rowSelector:a.opts.rowSelector(o,d),p=r(d,f);return l.Message.getClosestContainer(d,p,a.opts.rowPattern)}})),this.core.on("plugins.message.placed",this.messagePlacedHandler))},t.prototype.uninstall=function(){var i;this.results.clear(),this.containers.clear(),s(this.core.getFormElement(),((i={})[this.opts.formClass]=!1,i["fv-plugins-framework"]=!1,i)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.deregisterPlugin(t.MESSAGE_PLUGIN),this.core.off("plugins.message.placed",this.messagePlacedHandler))},t.prototype.onEnabled=function(){var i;s(this.core.getFormElement(),((i={})[this.opts.formClass]=!0,i)),this.opts.defaultMessageContainer&&this.core.enablePlugin(t.MESSAGE_PLUGIN)},t.prototype.onDisabled=function(){var i;s(this.core.getFormElement(),((i={})[this.opts.formClass]=!1,i)),this.opts.defaultMessageContainer&&this.core.disablePlugin(t.MESSAGE_PLUGIN)},t.prototype.onIconPlaced=function(i){},t.prototype.onMessagePlaced=function(i){},t.prototype.onFieldAdded=function(i){var a=this,o=i.elements;o&&(o.forEach(function(d){var f,p=a.containers.get(d);p&&(s(p,((f={})[a.opts.rowInvalidClass]=!1,f[a.opts.rowValidatingClass]=!1,f[a.opts.rowValidClass]=!1,f["fv-plugins-icon-container"]=!1,f)),a.containers.delete(d))}),this.prepareFieldContainer(i.field,o))},t.prototype.onFieldRemoved=function(i){var a=this;i.elements.forEach(function(o){var d,f=a.containers.get(o);f&&s(f,((d={})[a.opts.rowInvalidClass]=!1,d[a.opts.rowValidatingClass]=!1,d[a.opts.rowValidClass]=!1,d))})},t.prototype.prepareFieldContainer=function(i,a){var o=this;if(a.length){var d=a[0].getAttribute("type");d==="radio"||d==="checkbox"?this.prepareElementContainer(i,a[0]):a.forEach(function(f){return o.prepareElementContainer(i,f)})}},t.prototype.prepareElementContainer=function(i,a){var o,d=typeof this.opts.rowSelector=="string"?this.opts.rowSelector:this.opts.rowSelector(i,a),f=r(a,d);f!==a&&(s(f,((o={})[this.opts.rowClasses]=!0,o["fv-plugins-icon-container"]=!0,o)),this.containers.set(a,f))},t.prototype.onElementValidating=function(i){this.removeClasses(i.element,i.elements)},t.prototype.onElementNotValidated=function(i){this.removeClasses(i.element,i.elements)},t.prototype.onElementIgnored=function(i){this.removeClasses(i.element,i.elements)},t.prototype.removeClasses=function(i,a){var o,d=this,f=i.getAttribute("type"),p=f==="radio"||f==="checkbox"?a[0]:i;a.forEach(function(g){var m;s(g,((m={})[d.opts.eleValidClass]=!1,m[d.opts.eleInvalidClass]=!1,m))});var h=this.containers.get(p);h&&s(h,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!1,o))},t.prototype.onElementValidated=function(i){var a,o,d=this,f=i.elements,p=i.element.getAttribute("type"),h=p==="radio"||p==="checkbox"?f[0]:i.element;f.forEach(function(v){var b;s(v,((b={})[d.opts.eleValidClass]=i.valid,b[d.opts.eleInvalidClass]=!i.valid,b))});var g=this.containers.get(h);if(g)if(i.valid){this.results.delete(h);var m=!0;this.containers.forEach(function(v,b){v===g&&d.results.get(b)===!1&&(m=!1)}),m&&s(g,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!0,o))}else this.results.set(h,!1),s(g,((a={})[this.opts.rowInvalidClass]=!0,a[this.opts.rowValidatingClass]=!1,a[this.opts.rowValidClass]=!1,a))},t.MESSAGE_PLUGIN="___frameworkMessage",t}(u.Plugin);return $.Framework=n,$}();var De,rn=Ne.exports,Re={exports:{}},G={};Re.exports=function(){if(De)return G;De=1;var u=y,l=function(r,n){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},l(r,n)},c=u.utils.classSet,s=function(r){function n(e){var t=r.call(this,e)||this;return t.icons=new Map,t.opts=Object.assign({},{invalid:"fv-plugins-icon--invalid",onPlaced:function(){},onSet:function(){},valid:"fv-plugins-icon--valid",validating:"fv-plugins-icon--validating"},e),t.elementValidatingHandler=t.onElementValidating.bind(t),t.elementValidatedHandler=t.onElementValidated.bind(t),t.elementNotValidatedHandler=t.onElementNotValidated.bind(t),t.elementIgnoredHandler=t.onElementIgnored.bind(t),t.fieldAddedHandler=t.onFieldAdded.bind(t),t}return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}l(e,t),e.prototype=t===null?Object.create(t):(i.prototype=t.prototype,new i)}(n,r),n.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler)},n.prototype.uninstall=function(){this.icons.forEach(function(e){return e.parentNode.removeChild(e)}),this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler)},n.prototype.onEnabled=function(){this.icons.forEach(function(e,t,i){c(t,{"fv-plugins-icon--enabled":!0,"fv-plugins-icon--disabled":!1})})},n.prototype.onDisabled=function(){this.icons.forEach(function(e,t,i){c(t,{"fv-plugins-icon--enabled":!1,"fv-plugins-icon--disabled":!0})})},n.prototype.onFieldAdded=function(e){var t=this,i=e.elements;i&&(i.forEach(function(a){var o=t.icons.get(a);o&&(o.parentNode.removeChild(o),t.icons.delete(a))}),this.prepareFieldIcon(e.field,i))},n.prototype.prepareFieldIcon=function(e,t){var i=this;if(t.length){var a=t[0].getAttribute("type");a==="radio"||a==="checkbox"?this.prepareElementIcon(e,t[0]):t.forEach(function(o){return i.prepareElementIcon(e,o)})}},n.prototype.prepareElementIcon=function(e,t){var i=document.createElement("i");i.setAttribute("data-field",e),t.parentNode.insertBefore(i,t.nextSibling),c(i,{"fv-plugins-icon":!0,"fv-plugins-icon--enabled":this.isEnabled,"fv-plugins-icon--disabled":!this.isEnabled});var a={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:i};this.core.emit("plugins.icon.placed",a),this.opts.onPlaced(a),this.icons.set(t,i)},n.prototype.onElementValidating=function(e){var t,i=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!0,t)),a={element:e.element,field:e.field,iconElement:i,status:"Validating"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},n.prototype.onElementValidated=function(e){var t,i=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!e.valid,t[this.opts.valid]=e.valid,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:i,status:e.valid?"Valid":"Invalid"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},n.prototype.onElementNotValidated=function(e){var t,i=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:i,status:"NotValidated"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},n.prototype.onElementIgnored=function(e){var t,i=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:i,status:"Ignored"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},n.prototype.setClasses=function(e,t,i,a){var o=t.getAttribute("type"),d=o==="radio"||o==="checkbox"?i[0]:t;if(this.icons.has(d)){var f=this.icons.get(d);return c(f,a),f}return null},n}(u.Plugin);return G.Icon=s,G}();var ze,an=Re.exports,Ue={exports:{}},X={};Ue.exports=function(){if(ze)return X;ze=1;var u=y,l=function(r,n){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},l(r,n)},c=u.utils.removeUndefined,s=function(r){function n(e){var t=r.call(this,e)||this;return t.invalidFields=new Map,t.opts=Object.assign({},{enabled:!0},c(e)),t.validatorHandler=t.onValidatorValidated.bind(t),t.shouldValidateFilter=t.shouldValidate.bind(t),t.fieldAddedHandler=t.onFieldAdded.bind(t),t.elementNotValidatedHandler=t.onElementNotValidated.bind(t),t.elementValidatingHandler=t.onElementValidating.bind(t),t}return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}l(e,t),e.prototype=t===null?Object.create(t):(i.prototype=t.prototype,new i)}(n,r),n.prototype.install=function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter)},n.prototype.uninstall=function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter)},n.prototype.shouldValidate=function(e,t,i,a){return!this.isEnabled||!((this.opts.enabled===!0||this.opts.enabled[e]===!0)&&this.invalidFields.has(t)&&this.invalidFields.get(t).length&&this.invalidFields.get(t).indexOf(a)===-1)},n.prototype.onValidatorValidated=function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],i=t.indexOf(e.validator);e.result.valid&&i>=0?t.splice(i,1):e.result.valid||i!==-1||t.push(e.validator),this.invalidFields.set(e.element,t)},n.prototype.onFieldAdded=function(e){e.elements&&this.clearInvalidFields(e.elements)},n.prototype.onElementNotValidated=function(e){this.clearInvalidFields(e.elements)},n.prototype.onElementValidating=function(e){this.clearInvalidFields(e.elements)},n.prototype.clearInvalidFields=function(e){var t=this;e.forEach(function(i){return t.invalidFields.delete(i)})},n}(u.Plugin);return X.Sequence=s,X}();var Be,on=Ue.exports,qe={exports:{}},J={};qe.exports=function(){if(Be)return J;Be=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;return n.isFormValid=!1,n.isButtonClicked=!1,n.opts=Object.assign({},{aspNetButton:!1,buttons:function(e){return[].slice.call(e.querySelectorAll('[type="submit"]:not([formnovalidate])'))},liveMode:!0},r),n.submitHandler=n.handleSubmitEvent.bind(n),n.buttonClickHandler=n.handleClickEvent.bind(n),n.ignoreValidationFilter=n.ignoreValidation.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){var r=this;if(this.core.getFormElement()instanceof HTMLFormElement){var n=this.core.getFormElement();this.submitButtons=this.opts.buttons(n),n.setAttribute("novalidate","novalidate"),n.addEventListener("submit",this.submitHandler),this.hiddenClickedEle=document.createElement("input"),this.hiddenClickedEle.setAttribute("type","hidden"),n.appendChild(this.hiddenClickedEle),this.submitButtons.forEach(function(e){e.addEventListener("click",r.buttonClickHandler)}),this.core.registerFilter("element-ignored",this.ignoreValidationFilter)}},s.prototype.uninstall=function(){var r=this,n=this.core.getFormElement();n instanceof HTMLFormElement&&n.removeEventListener("submit",this.submitHandler),this.submitButtons.forEach(function(e){e.removeEventListener("click",r.buttonClickHandler)}),this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle),this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)},s.prototype.handleSubmitEvent=function(r){this.validateForm(r)},s.prototype.handleClickEvent=function(r){var n=r.currentTarget;if(this.isButtonClicked=!0,n instanceof HTMLElement&&!(this.opts.aspNetButton&&this.isFormValid===!0)){this.core.getFormElement().removeEventListener("submit",this.submitHandler),this.clickedButton=r.target;var e=this.clickedButton.getAttribute("name"),t=this.clickedButton.getAttribute("value");e&&t&&(this.hiddenClickedEle.setAttribute("name",e),this.hiddenClickedEle.setAttribute("value",t)),this.validateForm(r)}},s.prototype.validateForm=function(r){var n=this;this.isEnabled&&(r.preventDefault(),this.core.validate().then(function(e){e==="Valid"&&n.opts.aspNetButton&&!n.isFormValid&&n.clickedButton&&(n.isFormValid=!0,n.clickedButton.removeEventListener("click",n.buttonClickHandler),n.clickedButton.click())}))},s.prototype.ignoreValidation=function(r,n,e){return!!this.isEnabled&&!this.opts.liveMode&&!this.isButtonClicked},s}(y.Plugin);return J.SubmitButton=l,J}();var Ye,sn=qe.exports,$e={exports:{}},K={};$e.exports=function(){if(Ye)return K;Ye=1;var u=y,l=function(r,n){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},l(r,n)},c=u.utils.classSet,s=function(r){function n(e){var t=r.call(this,e)||this;return t.messages=new Map,t.opts=Object.assign({},{placement:"top",trigger:"click"},e),t.iconPlacedHandler=t.onIconPlaced.bind(t),t.validatorValidatedHandler=t.onValidatorValidated.bind(t),t.elementValidatedHandler=t.onElementValidated.bind(t),t.documentClickHandler=t.onDocumentClicked.bind(t),t}return function(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}l(e,t),e.prototype=t===null?Object.create(t):(i.prototype=t.prototype,new i)}(n,r),n.prototype.install=function(){var e;this.tip=document.createElement("div"),c(this.tip,((e={"fv-plugins-tooltip":!0})["fv-plugins-tooltip--".concat(this.opts.placement)]=!0,e)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),this.opts.trigger==="click"&&document.addEventListener("click",this.documentClickHandler)},n.prototype.uninstall=function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),this.opts.trigger==="click"&&document.removeEventListener("click",this.documentClickHandler)},n.prototype.onIconPlaced=function(e){var t=this;c(e.iconElement,{"fv-plugins-tooltip-icon":!0}),this.opts.trigger==="hover"?(e.iconElement.addEventListener("mouseenter",function(i){return t.show(e.element,i)}),e.iconElement.addEventListener("mouseleave",function(i){return t.hide()})):e.iconElement.addEventListener("click",function(i){return t.show(e.element,i)})},n.prototype.onValidatorValidated=function(e){if(!e.result.valid){var t=e.elements,i=e.element.getAttribute("type"),a=i==="radio"||i==="checkbox"?t[0]:e.element,o=typeof e.result.message=="string"?e.result.message:e.result.message[this.core.getLocale()];this.messages.set(a,o)}},n.prototype.onElementValidated=function(e){if(e.valid){var t=e.elements,i=e.element.getAttribute("type"),a=i==="radio"||i==="checkbox"?t[0]:e.element;this.messages.delete(a)}},n.prototype.onDocumentClicked=function(e){this.hide()},n.prototype.show=function(e,t){if(this.isEnabled&&(t.preventDefault(),t.stopPropagation(),this.messages.has(e))){c(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML='<div class="fv-plugins-tooltip__content">'.concat(this.messages.get(e),"</div>");var i=t.target.getBoundingClientRect(),a=this.tip.getBoundingClientRect(),o=a.height,d=a.width,f=0,p=0;switch(this.opts.placement){case"bottom":f=i.top+i.height,p=i.left+i.width/2-d/2;break;case"bottom-left":f=i.top+i.height,p=i.left;break;case"bottom-right":f=i.top+i.height,p=i.left+i.width-d;break;case"left":f=i.top+i.height/2-o/2,p=i.left-d;break;case"right":f=i.top+i.height/2-o/2,p=i.left+i.width;break;case"top-left":f=i.top-o,p=i.left;break;case"top-right":f=i.top-o,p=i.left+i.width-d;break;default:f=i.top-o,p=i.left+i.width/2-d/2}f+=window.scrollY||document.documentElement.scrollTop||document.body.scrollTop||0,p+=window.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(f,"px; left: ").concat(p,"px"))}},n.prototype.hide=function(){this.isEnabled&&c(this.tip,{"fv-plugins-tooltip--hide":!0})},n}(u.Plugin);return K.Tooltip=s,K}();var Ze,ln=$e.exports,Ge={exports:{}},W={};Ge.exports=function(){if(Ze)return W;Ze=1;var u=function(c,s){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,n){r.__proto__=n}||function(r,n){for(var e in n)Object.prototype.hasOwnProperty.call(n,e)&&(r[e]=n[e])},u(c,s)},l=function(c){function s(r){var n=c.call(this,r)||this;n.handlers=[],n.timers=new Map;var e=document.createElement("div");return n.defaultEvent="oninput"in e?"input":"keyup",n.opts=Object.assign({},{delay:0,event:n.defaultEvent,threshold:0},r),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(r,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function e(){this.constructor=r}u(r,n),r.prototype=n===null?Object.create(n):(e.prototype=n.prototype,new e)}(s,c),s.prototype.install=function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},s.prototype.uninstall=function(){this.handlers.forEach(function(r){return r.element.removeEventListener(r.event,r.handler)}),this.handlers=[],this.timers.forEach(function(r){return window.clearTimeout(r)}),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},s.prototype.prepareHandler=function(r,n){var e=this;n.forEach(function(t){var i=[];if(e.opts.event&&e.opts.event[r]===!1)i=[];else if(e.opts.event&&e.opts.event[r]&&typeof e.opts.event[r]!="function")i=e.opts.event[r].split(" ");else if(typeof e.opts.event=="string"&&e.opts.event!==e.defaultEvent)i=e.opts.event.split(" ");else{var a=t.getAttribute("type"),o=t.tagName.toLowerCase();i=[a==="radio"||a==="checkbox"||a==="file"||o==="select"?"change":e.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":e.defaultEvent]}i.forEach(function(d){var f=function(p){return e.handleEvent(p,r,t)};e.handlers.push({element:t,event:d,field:r,handler:f}),t.addEventListener(d,f)})})},s.prototype.handleEvent=function(r,n,e){var t=this;if(this.isEnabled&&this.exceedThreshold(n,e)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[n,e])){var i=function(){return t.core.validateElement(n,e).then(function(d){t.core.emit("plugins.trigger.executed",{element:e,event:r,field:n})})},a=this.opts.delay[n]||this.opts.delay;if(a===0)i();else{var o=this.timers.get(e);o&&window.clearTimeout(o),this.timers.set(e,window.setTimeout(i,1e3*a))}}},s.prototype.onFieldAdded=function(r){this.handlers.filter(function(n){return n.field===r.field}).forEach(function(n){return n.element.removeEventListener(n.event,n.handler)}),this.prepareHandler(r.field,r.elements)},s.prototype.onFieldRemoved=function(r){this.handlers.filter(function(n){return n.field===r.field&&r.elements.indexOf(n.element)>=0}).forEach(function(n){return n.element.removeEventListener(n.event,n.handler)})},s.prototype.exceedThreshold=function(r,n){var e=this.opts.threshold[r]!==0&&this.opts.threshold!==0&&(this.opts.threshold[r]||this.opts.threshold);if(!e)return!0;var t=n.getAttribute("type");return["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(t)!==-1||this.core.getElementValue(r,n).length>=e},s}(y.Plugin);return W.Trigger=l,W}();var Xe,dn=Ge.exports,Je={exports:{}},Q={};Je.exports=function(){if(Xe)return Q;Xe=1;var u=y,l=u.utils.format,c=u.utils.removeUndefined;return Q.between=function(){var s=function(r){return parseFloat("".concat(r).replace(",","."))};return{validate:function(r){var n=r.value;if(n==="")return{valid:!0};var e=Object.assign({},{inclusive:!0,message:""},c(r.options)),t=s(e.min),i=s(e.max);return e.inclusive?{message:l(r.l10n?e.message||r.l10n.between.default:e.message,["".concat(t),"".concat(i)]),valid:parseFloat(n)>=t&&parseFloat(n)<=i}:{message:l(r.l10n?e.message||r.l10n.between.notInclusive:e.message,["".concat(t),"".concat(i)]),valid:parseFloat(n)>t&&parseFloat(n)<i}}}},Q}();var Ke,cn=Je.exports,We={exports:{}},Qe={};We.exports=(Ke||(Ke=1,Qe.blank=function(){return{validate:function(u){return{valid:!0}}}}),Qe);var et,un=We.exports,tt={exports:{}},ee={};tt.exports=function(){if(et)return ee;et=1;var u=y.utils.call;return ee.callback=function(){return{validate:function(l){var c=u(l.options.callback,[l]);return typeof c=="boolean"?{valid:c}:c}}},ee}();var nt,fn=tt.exports,it={exports:{}},te={};it.exports=function(){if(nt)return te;nt=1;var u=y.utils.format;return te.choice=function(){return{validate:function(l){var c=l.element.tagName.toLowerCase()==="select"?l.element.querySelectorAll("option:checked").length:l.elements.filter(function(t){return t.checked}).length,s=l.options.min?"".concat(l.options.min):"",r=l.options.max?"".concat(l.options.max):"",n=l.l10n?l.options.message||l.l10n.choice.default:l.options.message,e=!(s&&c<parseInt(s,10)||r&&c>parseInt(r,10));switch(!0){case(!!s&&!!r):n=u(l.l10n?l.l10n.choice.between:l.options.message,[s,r]);break;case!!s:n=u(l.l10n?l.l10n.choice.more:l.options.message,s);break;case!!r:n=u(l.l10n?l.l10n.choice.less:l.options.message,r)}return{message:n,valid:e}}}},te}();var rt,pn=it.exports,at={exports:{}},N={};at.exports=function(){if(rt)return N;rt=1;var u=y.algorithms.luhn,l={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DANKORT:{length:[16],prefix:["5019"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},ELO:{length:[16],prefix:["4011","4312","4389","4514","4573","4576","5041","5066","5067","509","6277","6362","6363","650","6516","6550"]},FORBRUGSFORENINGEN:{length:[16],prefix:["600722"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","5868","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]},VISA_ELECTRON:{length:[16],prefix:["4026","417500","4405","4508","4844","4913","4917"]}};return N.CREDIT_CARD_TYPES=l,N.creditCard=function(){return{validate:function(c){if(c.value==="")return{meta:{type:null},valid:!0};if(/[^0-9-\s]+/.test(c.value))return{meta:{type:null},valid:!1};var s=c.value.replace(/\D/g,"");if(!u(s))return{meta:{type:null},valid:!1};for(var r=0,n=Object.keys(l);r<n.length;r++){var e=n[r];for(var t in l[e].prefix)if(c.value.substr(0,l[e].prefix[t].length)===l[e].prefix[t]&&l[e].length.indexOf(s.length)!==-1)return{meta:{type:e},valid:!0}}return{meta:{type:null},valid:!1}}}},N}();var ot,hn=at.exports,st={exports:{}},ne={};st.exports=function(){if(ot)return ne;ot=1;var u=y,l=u.utils.format,c=u.utils.isValidDate,s=u.utils.removeUndefined,r=function(e,t,i){var a=t.indexOf("YYYY"),o=t.indexOf("MM"),d=t.indexOf("DD");if(a===-1||o===-1||d===-1)return null;var f=e.split(" "),p=f[0].split(i);if(p.length<3)return null;var h=new Date(parseInt(p[a],10),parseInt(p[o],10)-1,parseInt(p[d],10)),g=f.length>2?f[2]:null;if(f.length>1){var m=f[1].split(":"),v=m.length>0?parseInt(m[0],10):0;h.setHours(g&&g.toUpperCase()==="PM"&&v<12?v+12:v),h.setMinutes(m.length>1?parseInt(m[1],10):0),h.setSeconds(m.length>2?parseInt(m[2],10):0)}return h},n=function(e,t){var i=t.replace(/Y/g,"y").replace(/M/g,"m").replace(/D/g,"d").replace(/:m/g,":M").replace(/:mm/g,":MM").replace(/:S/,":s").replace(/:SS/,":ss"),a=e.getDate(),o=a<10?"0".concat(a):a,d=e.getMonth()+1,f=d<10?"0".concat(d):d,p="".concat(e.getFullYear()).substr(2),h=e.getFullYear(),g=e.getHours()%12||12,m=g<10?"0".concat(g):g,v=e.getHours(),b=v<10?"0".concat(v):v,E=e.getMinutes(),_=E<10?"0".concat(E):E,w=e.getSeconds(),S=w<10?"0".concat(w):w,P={H:"".concat(v),HH:"".concat(b),M:"".concat(E),MM:"".concat(_),d:"".concat(a),dd:"".concat(o),h:"".concat(g),hh:"".concat(m),m:"".concat(d),mm:"".concat(f),s:"".concat(w),ss:"".concat(S),yy:"".concat(p),yyyy:"".concat(h)};return i.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\1?|"[^"]*"|'[^']*'/g,function(O){return P[O]?P[O]:O.slice(1,O.length-1)})};return ne.date=function(){return{validate:function(e){if(e.value==="")return{meta:{date:null},valid:!0};var t=Object.assign({},{format:e.element&&e.element.getAttribute("type")==="date"?"YYYY-MM-DD":"MM/DD/YYYY",message:""},s(e.options)),i=e.l10n?e.l10n.date.default:t.message,a={message:"".concat(i),meta:{date:null},valid:!1},o=t.format.split(" "),d=o.length>1?o[1]:null,f=o.length>2?o[2]:null,p=e.value.split(" "),h=p[0],g=p.length>1?p[1]:null,m=p.length>2?p[2]:null;if(o.length!==p.length)return a;var v=t.separator||(h.indexOf("/")!==-1?"/":h.indexOf("-")!==-1?"-":h.indexOf(".")!==-1?".":"/");if(v===null||h.indexOf(v)===-1)return a;var b=h.split(v),E=o[0].split(v);if(b.length!==E.length)return a;var _=b[E.indexOf("YYYY")],w=b[E.indexOf("MM")],S=b[E.indexOf("DD")];if(!/^\d+$/.test(_)||!/^\d+$/.test(w)||!/^\d+$/.test(S)||_.length>4||w.length>2||S.length>2)return a;var P=parseInt(_,10),O=parseInt(w,10),Zt=parseInt(S,10);if(!c(P,O,Zt))return a;var x=new Date(P,O-1,Zt);if(d){var V=g.split(":");if(d.split(":").length!==V.length)return a;var F=V.length>0?V[0].length<=2&&/^\d+$/.test(V[0])?parseInt(V[0],10):-1:0,L=V.length>1?V[1].length<=2&&/^\d+$/.test(V[1])?parseInt(V[1],10):-1:0,M=V.length>2?V[2].length<=2&&/^\d+$/.test(V[2])?parseInt(V[2],10):-1:0;if(F===-1||L===-1||M===-1||M<0||M>60||F<0||F>=24||f&&F>12||L<0||L>59)return a;x.setHours(m&&m.toUpperCase()==="PM"&&F<12?F+12:F),x.setMinutes(L),x.setSeconds(M)}var H=typeof t.min=="function"?t.min():t.min,ve=H instanceof Date?H:H?r(H,E,v):x,I=typeof t.max=="function"?t.max():t.max,ge=I instanceof Date?I:I?r(I,E,v):x,j=H instanceof Date?n(ve,t.format):H,k=I instanceof Date?n(ge,t.format):I;switch(!0){case(!!j&&!k):return{message:l(e.l10n?e.l10n.date.min:i,j),meta:{date:x},valid:x.getTime()>=ve.getTime()};case(!!k&&!j):return{message:l(e.l10n?e.l10n.date.max:i,k),meta:{date:x},valid:x.getTime()<=ge.getTime()};case(!!k&&!!j):return{message:l(e.l10n?e.l10n.date.range:i,[j,k]),meta:{date:x},valid:x.getTime()<=ge.getTime()&&x.getTime()>=ve.getTime()};default:return{message:"".concat(i),meta:{date:x},valid:!0}}}}},ne}();var lt,mn=st.exports,dt={exports:{}},ct={};dt.exports=(lt||(lt=1,ct.different=function(){return{validate:function(u){var l=typeof u.options.compare=="function"?u.options.compare.call(this):u.options.compare;return{valid:l===""||u.value!==l}}}}),ct);var ut,vn=dt.exports,ft={exports:{}},pt={};ft.exports=(ut||(ut=1,pt.digits=function(){return{validate:function(u){return{valid:u.value===""||/^\d+$/.test(u.value)}}}}),pt);var ht,gn=ft.exports,mt={exports:{}},ie={};mt.exports=function(){if(ht)return ie;ht=1;var u=y.utils.removeUndefined,l=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,c=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;return ie.emailAddress=function(){return{validate:function(s){if(s.value==="")return{valid:!0};var r=Object.assign({},{multiple:!1,requireGlobalDomain:!1,separator:/[,;]/},u(s.options)),n=r.requireGlobalDomain?c:l;if(r.multiple===!0||"".concat(r.multiple)==="true"){for(var e=r.separator||/[,;]/,t=function(o,d){for(var f=o.split(/"/),p=f.length,h=[],g="",m=0;m<p;m++)if(m%2==0){var v=f[m].split(d),b=v.length;if(b===1)g+=v[0];else{h.push(g+v[0]);for(var E=1;E<b-1;E++)h.push(v[E]);g=v[b-1]}}else g+='"'+f[m],m<p-1&&(g+='"');return h.push(g),h}(s.value,e),i=t.length,a=0;a<i;a++)if(!n.test(t[a]))return{valid:!1};return{valid:!0}}return{valid:n.test(s.value)}}}},ie}();var vt,yn=mt.exports,gt={exports:{}},re={};gt.exports=function(){if(vt)return re;vt=1;var u=function(l){return l.indexOf(".")===-1?l:l.split(".").slice(0,-1).join(".")};return re.file=function(){return{validate:function(l){if(l.value==="")return{valid:!0};var c,s,r=l.options.extension?l.options.extension.toLowerCase().split(",").map(function(d){return d.trim()}):[],n=l.options.type?l.options.type.toLowerCase().split(",").map(function(d){return d.trim()}):[];if(window.File&&window.FileList&&window.FileReader){var e=l.element.files,t=e.length,i=0;if(l.options.maxFiles&&t>parseInt("".concat(l.options.maxFiles),10))return{meta:{error:"INVALID_MAX_FILES"},valid:!1};if(l.options.minFiles&&t<parseInt("".concat(l.options.minFiles),10))return{meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var a={},o=0;o<t;o++){if(i+=e[o].size,a={ext:c=e[o].name.substr(e[o].name.lastIndexOf(".")+1),file:e[o],size:e[o].size,type:e[o].type},l.options.minSize&&e[o].size<parseInt("".concat(l.options.minSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_SIZE"},a),valid:!1};if(l.options.maxSize&&e[o].size>parseInt("".concat(l.options.maxSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_SIZE"},a),valid:!1};if(r.length>0&&r.indexOf(c.toLowerCase())===-1)return{meta:Object.assign({},{error:"INVALID_EXTENSION"},a),valid:!1};if(n.length>0&&e[o].type&&n.indexOf(e[o].type.toLowerCase())===-1)return{meta:Object.assign({},{error:"INVALID_TYPE"},a),valid:!1};if(l.options.validateFileName&&!l.options.validateFileName(u(e[o].name)))return{meta:Object.assign({},{error:"INVALID_NAME"},a),valid:!1}}if(l.options.maxTotalSize&&i>parseInt("".concat(l.options.maxTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:i},a),valid:!1};if(l.options.minTotalSize&&i<parseInt("".concat(l.options.minTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:i},a),valid:!1}}else{if(c=l.value.substr(l.value.lastIndexOf(".")+1),r.length>0&&r.indexOf(c.toLowerCase())===-1)return{meta:{error:"INVALID_EXTENSION",ext:c},valid:!1};if(s=u(l.value),l.options.validateFileName&&!l.options.validateFileName(s))return{meta:{error:"INVALID_NAME",name:s},valid:!1}}return{valid:!0}}}},re}();var yt,bn=gt.exports,bt={exports:{}},ae={};bt.exports=function(){if(yt)return ae;yt=1;var u=y,l=u.utils.format,c=u.utils.removeUndefined;return ae.greaterThan=function(){return{validate:function(s){if(s.value==="")return{valid:!0};var r=Object.assign({},{inclusive:!0,message:""},c(s.options)),n=parseFloat("".concat(r.min).replace(",","."));return r.inclusive?{message:l(s.l10n?r.message||s.l10n.greaterThan.default:r.message,"".concat(n)),valid:parseFloat(s.value)>=n}:{message:l(s.l10n?r.message||s.l10n.greaterThan.notInclusive:r.message,"".concat(n)),valid:parseFloat(s.value)>n}}}},ae}();var Et,En=bt.exports,xt={exports:{}},Vt={};xt.exports=(Et||(Et=1,Vt.identical=function(){return{validate:function(u){var l=typeof u.options.compare=="function"?u.options.compare.call(this):u.options.compare;return{valid:l===""||u.value===l}}}}),Vt);var wt,xn=xt.exports,At={exports:{}},oe={};At.exports=function(){if(wt)return oe;wt=1;var u=y.utils.removeUndefined;return oe.integer=function(){return{validate:function(l){if(l.value==="")return{valid:!0};var c=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},u(l.options)),s=c.decimalSeparator==="."?"\\.":c.decimalSeparator,r=c.thousandsSeparator==="."?"\\.":c.thousandsSeparator,n=new RegExp("^-?[0-9]{1,3}(".concat(r,"[0-9]{3})*(").concat(s,"[0-9]+)?$")),e=new RegExp(r,"g"),t="".concat(l.value);if(!n.test(t))return{valid:!1};r&&(t=t.replace(e,"")),s&&(t=t.replace(s,"."));var i=parseFloat(t);return{valid:!isNaN(i)&&isFinite(i)&&Math.floor(i)===i}}}},oe}();var Ot,Vn=At.exports,Ft={exports:{}},se={};Ft.exports=function(){if(Ot)return se;Ot=1;var u=y.utils.removeUndefined;return se.ip=function(){return{validate:function(l){if(l.value==="")return{valid:!0};var c=Object.assign({},{ipv4:!0,ipv6:!0},u(l.options)),s=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/([0-9]|[1-2][0-9]|3[0-2]))?$/,r=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*(\/(\d|\d\d|1[0-1]\d|12[0-8]))?$/;switch(!0){case(c.ipv4&&!c.ipv6):return{message:l.l10n?c.message||l.l10n.ip.ipv4:c.message,valid:s.test(l.value)};case(!c.ipv4&&c.ipv6):return{message:l.l10n?c.message||l.l10n.ip.ipv6:c.message,valid:r.test(l.value)};case(c.ipv4&&c.ipv6):default:return{message:l.l10n?c.message||l.l10n.ip.default:c.message,valid:s.test(l.value)||r.test(l.value)}}}}},se}();var Ct,wn=Ft.exports,Ht={exports:{}},le={};Ht.exports=function(){if(Ct)return le;Ct=1;var u=y,l=u.utils.format,c=u.utils.removeUndefined;return le.lessThan=function(){return{validate:function(s){if(s.value==="")return{valid:!0};var r=Object.assign({},{inclusive:!0,message:""},c(s.options)),n=parseFloat("".concat(r.max).replace(",","."));return r.inclusive?{message:l(s.l10n?r.message||s.l10n.lessThan.default:r.message,"".concat(n)),valid:parseFloat(s.value)<=n}:{message:l(s.l10n?r.message||s.l10n.lessThan.notInclusive:r.message,"".concat(n)),valid:parseFloat(s.value)<n}}}},le}();var It,An=Ht.exports,_t={exports:{}},St={};_t.exports=(It||(It=1,St.notEmpty=function(){return{validate:function(u){var l=!!u.options&&!!u.options.trim,c=u.value;return{valid:!l&&c!==""||l&&c!==""&&c.trim()!==""}}}}),St);var Pt,On=_t.exports,jt={exports:{}},de={};jt.exports=function(){if(Pt)return de;Pt=1;var u=y.utils.removeUndefined;return de.numeric=function(){return{validate:function(l){if(l.value==="")return{valid:!0};var c=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},u(l.options)),s="".concat(l.value);s.substr(0,1)===c.decimalSeparator?s="0".concat(c.decimalSeparator).concat(s.substr(1)):s.substr(0,2)==="-".concat(c.decimalSeparator)&&(s="-0".concat(c.decimalSeparator).concat(s.substr(2)));var r=c.decimalSeparator==="."?"\\.":c.decimalSeparator,n=c.thousandsSeparator==="."?"\\.":c.thousandsSeparator,e=new RegExp("^-?[0-9]{1,3}(".concat(n,"[0-9]{3})*(").concat(r,"[0-9]+)?$")),t=new RegExp(n,"g");if(!e.test(s))return{valid:!1};n&&(s=s.replace(t,"")),r&&(s=s.replace(r,"."));var i=parseFloat(s);return{valid:!isNaN(i)&&isFinite(i)}}}},de}();var kt,Fn=jt.exports,Nt={exports:{}},ce={};Nt.exports=function(){if(kt)return ce;kt=1;var u=y.utils.call;return ce.promise=function(){return{validate:function(l){return u(l.options.promise,[l])}}},ce}();var Lt,Cn=Nt.exports,Mt={exports:{}},Tt={};Mt.exports=(Lt||(Lt=1,Tt.regexp=function(){return{validate:function(u){if(u.value==="")return{valid:!0};var l=u.options.regexp;if(l instanceof RegExp)return{valid:l.test(u.value)};var c=l.toString();return{valid:(u.options.flags?new RegExp(c,u.options.flags):new RegExp(c)).test(u.value)}}}}),Tt);var Dt,Hn=Mt.exports,Rt={exports:{}},ue={};Rt.exports=function(){if(Dt)return ue;Dt=1;var u=y,l=u.utils.fetch,c=u.utils.removeUndefined;return ue.remote=function(){var s={crossDomain:!1,data:{},headers:{},method:"GET",validKey:"valid"};return{validate:function(r){if(r.value==="")return Promise.resolve({valid:!0});var n=Object.assign({},s,c(r.options)),e=n.data;typeof n.data=="function"&&(e=n.data.call(this,r)),typeof e=="string"&&(e=JSON.parse(e)),e[n.name||r.field]=r.value;var t=typeof n.url=="function"?n.url.call(this,r):n.url;return l(t,{crossDomain:n.crossDomain,headers:n.headers,method:n.method,params:e}).then(function(i){return Promise.resolve({message:i.message,meta:i,valid:"".concat(i[n.validKey])==="true"})}).catch(function(i){return Promise.reject({valid:!1})})}}},ue}();var zt,In=Rt.exports,Ut={exports:{}},fe={};Ut.exports=function(){if(zt)return fe;zt=1;var u=y.utils.removeUndefined;return fe.stringCase=function(){return{validate:function(l){if(l.value==="")return{valid:!0};var c=Object.assign({},{case:"lower"},u(l.options)),s=(c.case||"lower").toLowerCase();return{message:c.message||(l.l10n?s==="upper"?l.l10n.stringCase.upper:l.l10n.stringCase.default:c.message),valid:s==="upper"?l.value===l.value.toUpperCase():l.value===l.value.toLowerCase()}}}},fe}();var Bt,_n=Ut.exports,qt={exports:{}},pe={};qt.exports=function(){if(Bt)return pe;Bt=1;var u=y,l=u.utils.format,c=u.utils.removeUndefined;return pe.stringLength=function(){return{validate:function(s){var r=Object.assign({},{message:"",trim:!1,utf8Bytes:!1},c(s.options)),n=r.trim===!0||"".concat(r.trim)==="true"?s.value.trim():s.value;if(n==="")return{valid:!0};var e=r.min?"".concat(r.min):"",t=r.max?"".concat(r.max):"",i=r.utf8Bytes?function(d){for(var f=d.length,p=d.length-1;p>=0;p--){var h=d.charCodeAt(p);h>127&&h<=2047?f++:h>2047&&h<=65535&&(f+=2),h>=56320&&h<=57343&&p--}return f}(n):n.length,a=!0,o=s.l10n?r.message||s.l10n.stringLength.default:r.message;switch((e&&i<parseInt(e,10)||t&&i>parseInt(t,10))&&(a=!1),!0){case(!!e&&!!t):o=l(s.l10n?r.message||s.l10n.stringLength.between:r.message,[e,t]);break;case!!e:o=l(s.l10n?r.message||s.l10n.stringLength.more:r.message,"".concat(parseInt(e,10)));break;case!!t:o=l(s.l10n?r.message||s.l10n.stringLength.less:r.message,"".concat(parseInt(t,10)))}return{message:o,valid:a}}}},pe}();var Yt,Sn=qt.exports,$t={exports:{}},he={};$t.exports=function(){if(Yt)return he;Yt=1;var u=y.utils.removeUndefined;return he.uri=function(){var l={allowEmptyProtocol:!1,allowLocal:!1,protocol:"http, https, ftp"};return{validate:function(c){if(c.value==="")return{valid:!0};var s=Object.assign({},l,u(c.options)),r=s.allowLocal===!0||"".concat(s.allowLocal)==="true",n=s.allowEmptyProtocol===!0||"".concat(s.allowEmptyProtocol)==="true",e=s.protocol.split(",").join("|").replace(/\s/g,"");return{valid:new RegExp("^(?:(?:"+e+")://)"+(n?"?":"")+"(?:\\S+(?::\\S*)?@)?(?:"+(r?"":"(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})")+"(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9])*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))"+(r?"?":"")+")(?::\\d{2,5})?(?:/[^\\s]*)?$","i").test(c.value)}}}},he}();var Pn=$t.exports,jn={Alias:Jt.Alias,Aria:Kt.Aria,Declarative:Wt.Declarative,DefaultSubmit:Qt.DefaultSubmit,Dependency:en.Dependency,Excluded:tn.Excluded,FieldStatus:nn.FieldStatus,Framework:rn.Framework,Icon:an.Icon,Message:Te.Message,Sequence:on.Sequence,SubmitButton:sn.SubmitButton,Tooltip:ln.Tooltip,Trigger:dn.Trigger},me={between:cn.between,blank:un.blank,callback:fn.callback,choice:pn.choice,creditCard:hn.creditCard,date:mn.date,different:vn.different,digits:gn.digits,emailAddress:yn.emailAddress,file:bn.file,greaterThan:En.greaterThan,identical:xn.identical,integer:Vn.integer,ip:wn.ip,lessThan:An.lessThan,notEmpty:On.notEmpty,numeric:Fn.numeric,promise:Cn.promise,regexp:Hn.regexp,remote:In.remote,stringCase:_n.stringCase,stringLength:Sn.stringLength,uri:Pn.uri};return A.Plugin=y.Plugin,A.algorithms=y.algorithms,A.formValidation=function(u,l){var c=y.formValidation(u,l);return Object.keys(me).forEach(function(s){return c.registerValidator(s,me[s])}),c},A.plugins=jn,A.utils=y.utils,A.validators=me,A}var Xt;function Ln(){return Xt||(Xt=1,ye.exports=Nn()),ye.exports}var Mn=Ln();const Tn=kn(Mn);try{window.FormValidation=Tn}catch{}
