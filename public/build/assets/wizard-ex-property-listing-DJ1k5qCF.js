(function(){window.Helpers.initCustomOptionCheck();const l=document.querySelector(".flatpickr"),s=document.querySelector(".contact-number-mask"),r=$("#plCountry"),p=document.querySelector("#plFurnishingDetails");s&&new Cleave(s,{phone:!0,phoneRegionCode:"US"}),r&&(r.wrap('<div class="position-relative"></div>'),r.select2({placeholder:"Select country",dropdownParent:r.parent()})),l&&l.flatpickr();const u=["Fridge","TV","AC","WiFi","RO","Washing Machine","Sofa","Bed","Dining Table","Microwave","Cupboard"];p&&new Tagify(p,{whitelist:u,maxTags:10,dropdown:{maxItems:20,classname:"tags-inline",enabled:0,closeOnSelect:!1}});const n=document.querySelector("#wizard-property-listing");if(typeof n!==void 0&&n!==null){const t=n.querySelector("#wizard-property-listing-form"),d=t.querySelector("#personal-details"),m=t.querySelector("#property-details"),g=t.querySelector("#property-features"),F=t.querySelector("#property-area"),w=t.querySelector("#price-details"),y=[].slice.call(t.querySelectorAll(".btn-next")),f=[].slice.call(t.querySelectorAll(".btn-prev")),e=new Stepper(n,{linear:!0}),V=FormValidation.formValidation(d,{fields:{plFirstName:{validators:{notEmpty:{message:"Please enter your first name"}}},plLastName:{validators:{notEmpty:{message:"Please enter your last name"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton},init:o=>{o.on("plugins.message.placed",function(i){i.element.parentElement.classList.contains("input-group")&&i.element.parentElement.insertAdjacentElement("afterend",i.messageElement)})}}).on("core.form.valid",function(){e.next()}),c=FormValidation.formValidation(m,{fields:{plPropertyType:{validators:{notEmpty:{message:"Please select property type"}}},plZipCode:{validators:{notEmpty:{message:"Please enter zip code"},stringLength:{min:4,max:10,message:"The zip code must be more than 4 and less than 10 characters long"}}}},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:function(o,i){switch(o){case"plAddress":return".col-lg-12";default:return".col-sm-6"}}}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){e.next()}),a=$("#plPropertyType");a.length&&(a.wrap('<div class="position-relative"></div>'),a.select2({placeholder:"Select property type",dropdownParent:a.parent()}).on("change",function(){c.revalidateField("plPropertyType")}));const S=FormValidation.formValidation(g,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-sm-6"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){e.next()}),v=FormValidation.formValidation(F,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-12"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){e.next()}),b=FormValidation.formValidation(w,{fields:{},plugins:{trigger:new FormValidation.plugins.Trigger,bootstrap5:new FormValidation.plugins.Bootstrap5({eleValidClass:"",rowSelector:".col-md-12"}),autoFocus:new FormValidation.plugins.AutoFocus,submitButton:new FormValidation.plugins.SubmitButton}}).on("core.form.valid",function(){alert("Submitted..!!")});y.forEach(o=>{o.addEventListener("click",i=>{switch(e._currentIndex){case 0:V.validate();break;case 1:c.validate();break;case 2:S.validate();break;case 3:v.validate();break;case 4:b.validate();break}})}),f.forEach(o=>{o.addEventListener("click",i=>{switch(e._currentIndex){case 4:e.previous();break;case 3:e.previous();break;case 2:e.previous();break;case 1:e.previous();break}})})}})();
