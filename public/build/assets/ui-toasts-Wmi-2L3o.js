(function(){const e=document.querySelector(".toast-ex"),i=document.querySelector(".toast-placement-ex"),l=document.querySelector("#showToastAnimation"),u=document.querySelector("#showToastPlacement");let o,c,t,n,s;l&&(l.onclick=function(){n&&p(n),o=document.querySelector("#selectType").value,c=document.querySelector("#selectAnimation").value,e.classList.add(c),e.querySelector(".ti").classList.add(o),n=new bootstrap.Toast(e),n.show()});function p(r){r&&r._element!==null&&(i&&(i.classList.remove(o),i.querySelector(".ti").classList.remove(o),DOMTokenList.prototype.remove.apply(i.classList,t)),e&&(e.classList.remove(o,c),e.querySelector(".ti").classList.remove(o)),r.dispose())}u&&(u.onclick=function(){s&&p(s),o=document.querySelector("#selectTypeOpt").value,t=document.querySelector("#selectPlacement").value.split(" "),i.querySelector(".ti").classList.add(o),DOMTokenList.prototype.add.apply(i.classList,t),s=new bootstrap.Toast(i),s.show()})})();$(function(){var e=-1,i=0,l,u=function(){var t=["Don't be pushed around by the fears in your mind. Be led by the dreams in your heart.",'<div class="mb-3"><input class="input-small form-control" value="Textbox"/>&nbsp;<a href="http://johnpapa.net" target="_blank">This is a hyperlink</a></div><div class="d-flex"><button type="button" id="okBtn" class="btn btn-primary btn-sm me-2 waves-effect waves-light">Close me</button><button type="button" id="surpriseBtn" class="btn btn-sm btn-secondary waves-effect waves-light">Surprise me</button></div>',"Live the Life of Your Dreams","Believe in Your Self!","Be mindful. Be grateful. Be positive.","Accept yourself, love yourself!"];return e++,e===t.length&&(e=0),t[e]},o=function(t){return t=t||"Clear itself?",t+='<br /><br /><button type="button" class="btn btn-secondary clear waves-effect waves-light">Yes</button>',t};$("#closeButton").on("click",function(){$(this).is(":checked")?$("#addBehaviorOnToastCloseClick").prop("disabled",!1):($("#addBehaviorOnToastCloseClick").prop("disabled",!0),$("#addBehaviorOnToastCloseClick").prop("checked",!1))}),$("#showtoast").on("click",function(){var t=$("#toastTypeGroup input:radio:checked").val(),n=$("html").attr("dir")==="rtl",s=$("#message").val(),p=$("#title").val()||"",r=$("#showDuration"),h=$("#hideDuration"),f=$("#timeOut"),m=$("#extendedTimeOut"),v=$("#showEasing"),g=$("#hideEasing"),k=$("#showMethod"),y=$("#hideMethod"),b=i++,d=$("#addClear").prop("checked"),w="toast-top-right";w=typeof toastr.options.positionClass>"u"?"toast-top-right":toastr.options.positionClass,toastr.options={maxOpened:1,autoDismiss:!0,closeButton:$("#closeButton").prop("checked"),debug:$("#debugInfo").prop("checked"),newestOnTop:$("#newestOnTop").prop("checked"),progressBar:$("#progressBar").prop("checked"),positionClass:$("#positionGroup input:radio:checked").val()||"toast-top-right",preventDuplicates:$("#preventDuplicates").prop("checked"),onclick:null,rtl:n},w!=toastr.options.positionClass&&(toastr.options.hideDuration=0,toastr.clear()),$("#addBehaviorOnToastClick").prop("checked")&&(toastr.options.onclick=function(){alert("You can perform some custom action after a toast goes away")}),$("#addBehaviorOnToastCloseClick").prop("checked")&&(toastr.options.onCloseClick=function(){alert("You can perform some custom action when the close button is clicked")}),r.val().length&&(toastr.options.showDuration=parseInt(r.val())),h.val().length&&(toastr.options.hideDuration=parseInt(h.val())),f.val().length&&(toastr.options.timeOut=d?0:parseInt(f.val())),m.val().length&&(toastr.options.extendedTimeOut=d?0:parseInt(m.val())),v.val().length&&(toastr.options.showEasing=v.val()),g.val().length&&(toastr.options.hideEasing=g.val()),k.val().length&&(toastr.options.showMethod=k.val()),y.val().length&&(toastr.options.hideMethod=y.val()),d&&(s=o(s),toastr.options.tapToDismiss=!1),s||(s=u());var a=toastr[t](s,p);l=a,!(typeof a>"u")&&(a.find("#okBtn").length&&a.delegate("#okBtn","click",function(){alert("you clicked me. i was toast #"+b+". goodbye!"),a.remove()}),a.find("#surpriseBtn").length&&a.delegate("#surpriseBtn","click",function(){alert("Surprise! you clicked me. i was toast #"+b+". You could perform an action here.")}),a.find(".clear").length&&a.delegate(".clear","click",function(){toastr.clear(a,{force:!0})}))});function c(){return l}$("#clearlasttoast").on("click",function(){toastr.clear(c())}),$("#cleartoasts").on("click",function(){toastr.clear()})});
