$(function(){var p=$(".datatables-projects");p.length&&(p.DataTable({ajax:assetsPath+"json/user-profile.json",columns:[{data:""},{data:"id"},{data:"project_name"},{data:"project_leader"},{data:""},{data:"status"},{data:""}],columnDefs:[{className:"control",searchable:!1,orderable:!1,responsivePriority:2,targets:0,render:function(t,r,e,s){return""}},{targets:1,orderable:!1,searchable:!1,responsivePriority:3,checkboxes:!0,render:function(){return'<input type="checkbox" class="dt-checkboxes form-check-input">'},checkboxes:{selectAllRender:'<input type="checkbox" class="form-check-input">'}},{targets:2,responsivePriority:4,render:function(t,r,e,s){var a=e.project_img,l=e.project_name,n=e.date;if(a)var d='<img src="'+assetsPath+"img/icons/brands/"+a+'" alt="Avatar" class="rounded-circle">';else{var i=Math.floor(Math.random()*6),c=["success","danger","warning","info","primary","secondary"],m=c[i],l=e.project_name,o=l.match(/\b\w/g)||[];o=((o.shift()||"")+(o.pop()||"")).toUpperCase(),d='<span class="avatar-initial rounded-circle bg-label-'+m+'">'+o+"</span>"}var u='<div class="d-flex justify-content-left align-items-center"><div class="avatar-wrapper"><div class="avatar avatar-sm me-3">'+d+'</div></div><div class="d-flex flex-column"><span class="text-truncate fw-medium text-heading">'+l+'</span><small class="text-truncate">'+n+"</small></div></div>";return u}},{targets:3,render:function(t,r,e,s){var a=e.project_leader;return'<span class="text-heading">'+a+"</span>"}},{targets:4,orderable:!1,searchable:!1,render:function(t,r,e,s){for(var a=e.team,l="",n=0,d=0;d<a.length&&(l+='<li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="Kim Karlos" class="avatar avatar-sm pull-up"><img class="rounded-circle" src="'+assetsPath+"img/avatars/"+a[d]+'"  alt="Avatar"></li>',n++,!(n>2));d++);if(n>2){var i=a.length-3;i>0&&(l+='<li class="avatar avatar-sm"><span class="avatar-initial rounded-circle pull-up text-heading" data-bs-toggle="tooltip" data-bs-placement="top" title="'+i+' more">+'+i+"</span ></li>")}var c='<div class="d-flex align-items-center"><ul class="list-unstyled d-flex align-items-center avatar-group mb-0 z-2">'+l+"</ul></div>";return c}},{targets:-2,render:function(t,r,e,s){var a=e.status;return'<div class="d-flex align-items-center"><div class="progress w-100 me-3" style="height: 6px;"><div class="progress-bar" style="width: '+a+'" aria-valuenow="'+a+'" aria-valuemin="0" aria-valuemax="100"></div></div><span class="text-heading">'+a+"</span></div>"}},{targets:-1,searchable:!1,title:"Action",orderable:!1,render:function(t,r,e,s){return'<div class="d-inline-block"><a href="javascript:;" class="btn btn-icon btn-text-secondary waves-effect waves-light rounded-pill dropdown-toggle hide-arrow" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical ti-md"></i></a><div class="dropdown-menu dropdown-menu-end m-0"><a href="javascript:;" class="dropdown-item">Details</a><a href="javascript:;" class="dropdown-item">Archive</a><div class="dropdown-divider"></div><a href="javascript:;" class="dropdown-item text-danger delete-record">Delete</a></div></div>'}}],order:[[2,"desc"]],dom:'<"card-header pb-0 pt-sm-0"<"head-label text-center"><"d-flex justify-content-center justify-content-md-end"f>>t<"row mx-2"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',displayLength:7,lengthMenu:[7,10,25,50,75,100],language:{search:"",searchPlaceholder:"Search Project",paginate:{next:'<i class="ti ti-chevron-right ti-sm"></i>',previous:'<i class="ti ti-chevron-left ti-sm"></i>'}},responsive:{details:{display:$.fn.dataTable.Responsive.display.modal({header:function(t){var r=t.data();return'Details of "'+r.project_name+'" Project'}}),type:"column",renderer:function(t,r,e){var s=$.map(e,function(a,l){return a.title!==""?'<tr data-dt-row="'+a.rowIndex+'" data-dt-column="'+a.columnIndex+'"><td>'+a.title+":</td> <td>"+a.data+"</td></tr>":""}).join("");return s?$('<table class="table"/><tbody />').append(s):!1}}}}),$("div.head-label").html('<h5 class="card-title mb-0">Project List</h5>')),setTimeout(()=>{$(".dataTables_filter .form-control").removeClass("form-control-sm"),$(".dataTables_length .form-select").removeClass("form-select-sm")},300)});
