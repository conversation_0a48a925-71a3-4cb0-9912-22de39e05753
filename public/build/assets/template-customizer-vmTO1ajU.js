const z=`<div id="template-customizer" class="bg-card">
  <a href="javascript:void(0)" class="template-customizer-open-btn" tabindex="-1"></a>

  <div class="p-6 m-0 lh-1 border-bottom template-customizer-header position-relative py-4">
    <h6 class="template-customizer-t-panel_header mb-1"></h6>
    <p class="template-customizer-t-panel_sub_header mb-0 small"></p>
    <div class="d-flex align-items-center gap-2 position-absolute end-0 top-0 mt-6 me-5">
      <a
        href="javascript:void(0)"
        class="template-customizer-reset-btn text-heading"
        data-bs-toggle="tooltip"
        data-bs-placement="bottom"
        title="Reset Customizer"
        ><i class="ti ti-refresh ti-lg"></i
        ><span class="badge rounded-pill bg-danger badge-dot badge-notifications d-none"></span
      ></a>
      <a href="javascript:void(0)" class="template-customizer-close-btn fw-light text-heading" tabindex="-1">
        <i class="ti ti-x ti-lg"></i>
      </a>
    </div>
  </div>

  <div class="template-customizer-inner pt-6">
    <!-- Theming -->
    <div class="template-customizer-theming">
      <h5 class="m-0 px-6 py-6">
        <span class="template-customizer-t-theming_header bg-label-primary rounded-1 py-1 px-3 small"></span>
      </h5>

      <!-- Style -->
      <div class="m-0 px-6 pb-6 template-customizer-style w-100">
        <label for="customizerStyle" class="form-label d-block template-customizer-t-style_label mb-2"></label>
        <div class="row px-1 template-customizer-styles-options"></div>
      </div>

      <!-- Themes -->
      <div class="m-0 px-6 template-customizer-themes w-100">
        <label for="customizerTheme" class="form-label template-customizer-t-theme_label mb-2"></label>
        <div class="row px-1 template-customizer-themes-options"></div>
      </div>
    </div>
    <!--/ Theming -->

    <!-- Layout -->
    <div class="template-customizer-layout">
      <hr class="m-0 px-6 my-6" />
      <h5 class="m-0 px-6 pb-6">
        <span class="template-customizer-t-layout_header bg-label-primary rounded-2 py-1 px-3 small"></span>
      </h5>

      <!-- Layout(Menu) -->
      <div class="m-0 px-6 pb-6 d-block template-customizer-layouts">
        <label for="customizerStyle" class="form-label d-block template-customizer-t-layout_label mb-2"></label>
        <div class="row px-1 template-customizer-layouts-options">
          <!--? Uncomment If using offcanvas layout -->
          <!-- <div class="col-12">
            <div class="form-check">
              <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-offcanvas"
                value="static-offcanvas">
              <label class="form-check-label template-customizer-t-layout_offcanvas"
                for="layoutRadios-offcanvas"></label>
            </div>
          </div> -->
          <!-- <div class="col-12">
            <div class="form-check">
              <input class="form-check-input" type="radio" name="layoutRadios" id="layoutRadios-fixed_offcanvas"
                value="fixed-offcanvas">
              <label class="form-check-label template-customizer-t-layout_fixed_offcanvas"
                for="layoutRadios-fixed_offcanvas"></label>
            </div>
          </div> -->
        </div>
      </div>

      <!-- Header Options for Horizontal -->
      <div class="m-0 px-6 pb-6 template-customizer-headerOptions w-100">
        <label for="customizerHeader" class="form-label template-customizer-t-layout_header_label mb-2"></label>
        <div class="row px-1 template-customizer-header-options"></div>
      </div>

      <!-- Fixed navbar -->
      <div class="m-0 px-6 pb-6 template-customizer-layoutNavbarOptions w-100">
        <label for="customizerNavbar" class="form-label template-customizer-t-layout_navbar_label mb-2"></label>
        <div class="row px-1 template-customizer-navbar-options"></div>
      </div>

      <!-- Content -->
      <div class="m-0 px-6 pb-6 template-customizer-content w-100">
        <label for="customizerContent" class="form-label template-customizer-t-content_label mb-2"></label>
        <div class="row px-1 template-customizer-content-options"></div>
      </div>

      <!-- Directions -->
      <div class="m-0 px-6 pb-6 template-customizer-directions w-100">
        <label for="customizerDirection" class="form-label template-customizer-t-direction_label mb-2"></label>
        <div class="row px-1 template-customizer-directions-options"></div>
      </div>
    </div>
    <!--/ Layout -->
  </div>
</div>
`,A="%name%.scss",F=["rtl","style","headerType","contentLayout","layoutCollapsed","showDropdownOnHover","layoutNavbarOptions","layoutFooterFixed","themes"],R=["light","dark","system"],D=["sticky","static","hidden"];let O;const y=document.documentElement.classList;y.contains("layout-navbar-fixed")?O="sticky":y.contains("layout-navbar-hidden")?O="hidden":O="static";const H=!0,$=document.getElementsByTagName("HTML")[0].getAttribute("data-theme")||0,q=y.contains("dark-style")?"dark":"light",I=document.documentElement.getAttribute("dir")==="rtl",P=!!y.contains("layout-menu-collapsed"),B=!0,U=O,M=y.contains("layout-wide")?"wide":"compact",W=!!y.contains("layout-footer-fixed");let E;y.contains("layout-menu-offcanvas")?E="static-offcanvas":y.contains("layout-menu-fixed")?E="fixed":y.contains("layout-menu-fixed-offcanvas")?E="fixed-offcanvas":E="static";const Y=E;class m{constructor({cssPath:t,themesPath:e,cssFilenamePattern:s,displayCustomizer:i,controls:n,defaultTextDir:f,defaultHeaderType:c,defaultContentLayout:r,defaultMenuCollapsed:h,defaultShowDropdownOnHover:_,defaultNavbarType:d,defaultFooterFixed:S,styles:C,navbarOptions:b,defaultStyle:w,availableContentLayouts:a,availableDirections:u,availableStyles:g,availableThemes:T,availableLayouts:k,availableHeaderTypes:L,availableNavbarOptions:l,defaultTheme:p,pathResolver:o,onSettingsChange:v,lang:x}){if(!this._ssr){if(!window.Helpers)throw new Error("window.Helpers required.");if(this.settings={},this.settings.cssPath=t,this.settings.themesPath=e,this.settings.cssFilenamePattern=s||A,this.settings.displayCustomizer=typeof i<"u"?i:H,this.settings.controls=n||F,this.settings.defaultTextDir=f==="rtl"?!0:I,this.settings.defaultHeaderType=c||Y,this.settings.defaultMenuCollapsed=typeof h<"u"?h:P,this.settings.defaultContentLayout=typeof r<"u"?r:M,this.settings.defaultShowDropdownOnHover=typeof _<"u"?_:B,this.settings.defaultNavbarType=typeof d<"u"?d:U,this.settings.defaultFooterFixed=typeof S<"u"?S:W,this.settings.availableDirections=u||m.DIRECTIONS,this.settings.availableStyles=g||m.STYLES,this.settings.availableThemes=T||m.THEMES,this.settings.availableHeaderTypes=L||m.HEADER_TYPES,this.settings.availableContentLayouts=a||m.CONTENT,this.settings.availableLayouts=k||m.LAYOUTS,this.settings.availableNavbarOptions=l||m.NAVBAR_OPTIONS,this.settings.defaultTheme=this._getDefaultTheme(typeof p<"u"?p:$),this.settings.styles=C||R,this.settings.navbarOptions=b||D,this.settings.defaultStyle=w||q,this.settings.lang=x||"en",this.pathResolver=o||(N=>N),this.settings.styles.length<2){const N=this.settings.controls.indexOf("style");N!==-1&&(this.settings.controls=this.settings.controls.slice(0,N).concat(this.settings.controls.slice(N+1)))}this.settings.onSettingsChange=typeof v=="function"?v:()=>{},this._loadSettings(),this._listeners=[],this._controls={},this._initDirection(),this._initStyle(),this._initTheme(),this.setLayoutType(this.settings.headerType,!1),this.setContentLayout(this.settings.contentLayout,!1),this.setDropdownOnHover(this.settings.showDropdownOnHover,!1),this.setLayoutNavbarOption(this.settings.layoutNavbarOptions,!1),this.setLayoutFooterFixed(this.settings.layoutFooterFixed,!1),this._setup()}}setRtl(t){this._hasControls("rtl")&&(this._setSetting("Rtl",String(t)),this._setCookie("direction",t,365),window.location.reload())}setContentLayout(t,e=!0){this._hasControls("contentLayout")&&(this.settings.contentLayout=t,e&&this._setSetting("contentLayout",t),window.Helpers.setContentLayout(t),e&&this.settings.onSettingsChange.call(this,this.settings))}setStyle(t){const s=!this._getLayoutName().includes("front");this._setSetting("Style",t);const i=s?"admin-mode":"front-mode",n=s?"admin-colorPref":"front-colorPref";t!==""&&this._checkCookie(i)?t==="system"?(this._setCookie(i,"system",365),window.matchMedia("(prefers-color-scheme: dark)").matches?this._setCookie(n,"dark",365):this._setCookie(n,"light",365)):t==="dark"?(this._setCookie(i,"dark",365),this._setCookie(n,"dark",365)):(this._setCookie(i,"light",365),this._setCookie(n,"light",365)):this._setCookie(i,t||"light",365),window.location.reload()}setTheme(t,e=!0,s=null){if(!this._hasControls("themes"))return;const i=this._getThemeByName(t);if(!i)return;this.settings.theme=i,e&&this._setSetting("Theme",t),this._setCookie("theme",t,365);const n=this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t+(this.settings.style!=="light"?`-${this.settings.style}`:"")));this._loadStylesheets({[n]:document.querySelector(".template-customizer-theme-css")},s||(()=>{})),e&&this.settings.onSettingsChange.call(this,this.settings)}setLayoutType(t,e=!0){if(!this._hasControls("headerType")||t!=="static"&&t!=="static-offcanvas"&&t!=="fixed"&&t!=="fixed-offcanvas")return;this.settings.headerType=t,e&&this._setSetting("LayoutType",t),window.Helpers.setPosition(t==="fixed"||t==="fixed-offcanvas",t==="static-offcanvas"||t==="fixed-offcanvas"),e&&this.settings.onSettingsChange.call(this,this.settings);let s=window.Helpers.menuPsScroll;const i=window.PerfectScrollbar;this.settings.headerType==="fixed"||this.settings.headerType==="fixed-offcanvas"?i&&s&&(window.Helpers.menuPsScroll.destroy(),s=new i(document.querySelector(".menu-inner"),{suppressScrollX:!0,wheelPropagation:!1}),window.Helpers.menuPsScroll=s):s&&window.Helpers.menuPsScroll.destroy()}setDropdownOnHover(t,e=!0){if(this._hasControls("showDropdownOnHover")){if(this.settings.showDropdownOnHover=t,e&&this._setSetting("ShowDropdownOnHover",t),window.Helpers.mainMenu){window.Helpers.mainMenu.destroy(),config.showDropdownOnHover=t;const{Menu:s}=window;window.Helpers.mainMenu=new s(document.getElementById("layout-menu"),{orientation:"horizontal",closeChildren:!0,showDropdownOnHover:config.showDropdownOnHover})}e&&this.settings.onSettingsChange.call(this,this.settings)}}setLayoutNavbarOption(t,e=!0){this._hasControls("layoutNavbarOptions")&&(this.settings.layoutNavbarOptions=t,e&&this._setSetting("FixedNavbarOption",t),window.Helpers.setNavbar(t),e&&this.settings.onSettingsChange.call(this,this.settings))}setLayoutFooterFixed(t,e=!0){this.settings.layoutFooterFixed=t,e&&this._setSetting("FixedFooter",t),window.Helpers.setFooterFixed(t),e&&this.settings.onSettingsChange.call(this,this.settings)}setLang(t,e=!0,s=!1){if(t===this.settings.lang&&!s)return;if(!m.LANGUAGES[t])throw new Error(`Language "${t}" not found!`);const i=m.LANGUAGES[t];["panel_header","panel_sub_header","theming_header","style_label","style_switch_light","style_switch_dark","layout_header","layout_label","layout_header_label","content_label","layout_static","layout_offcanvas","layout_fixed","layout_fixed_offcanvas","layout_dd_open_label","layout_navbar_label","layout_footer_label","misc_header","theme_label","direction_label"].forEach(c=>{const r=this.container.querySelector(`.template-customizer-t-${c}`);r&&(r.textContent=i[c])});const n=i.themes||{},f=this.container.querySelectorAll(".template-customizer-theme-item")||[];for(let c=0,r=f.length;c<r;c++){const h=f[c].querySelector('input[type="radio"]').value;f[c].querySelector(".template-customizer-theme-name").textContent=n[h]||this._getThemeByName(h).title}this.settings.lang=t,e&&this._setSetting("Lang",t),e&&this.settings.onSettingsChange.call(this,this.settings)}update(){if(this._ssr)return;const t=!!document.querySelector(".layout-navbar"),e=!!document.querySelector(".layout-menu"),s=!!document.querySelector(".layout-menu-horizontal.menu, .layout-menu-horizontal .menu");document.querySelector(".layout-wrapper.layout-navbar-full");const i=!!document.querySelector(".content-footer");this._controls.showDropdownOnHover&&(e?(this._controls.showDropdownOnHover.setAttribute("disabled","disabled"),this._controls.showDropdownOnHover.classList.add("disabled")):(this._controls.showDropdownOnHover.removeAttribute("disabled"),this._controls.showDropdownOnHover.classList.remove("disabled"))),this._controls.layoutNavbarOptions&&(t?(this._controls.layoutNavbarOptions.removeAttribute("disabled"),this._controls.layoutNavbarOptionsW.classList.remove("disabled")):(this._controls.layoutNavbarOptions.setAttribute("disabled","disabled"),this._controls.layoutNavbarOptionsW.classList.add("disabled")),s&&t&&this.settings.headerType==="fixed"&&(this._controls.layoutNavbarOptions.setAttribute("disabled","disabled"),this._controls.layoutNavbarOptionsW.classList.add("disabled"))),this._controls.layoutFooterFixed&&(i?(this._controls.layoutFooterFixed.removeAttribute("disabled"),this._controls.layoutFooterFixedW.classList.remove("disabled")):(this._controls.layoutFooterFixed.setAttribute("disabled","disabled"),this._controls.layoutFooterFixedW.classList.add("disabled"))),this._controls.headerType&&(e||s?this._controls.headerType.removeAttribute("disabled"):this._controls.headerType.setAttribute("disabled","disabled"))}clearLocalStorage(){if(this._ssr)return;const t=this._getLayoutName();["Theme","Style","LayoutCollapsed","FixedNavbarOption","LayoutType","contentLayout","Rtl","Lang"].forEach(s=>{const i=`templateCustomizer-${t}--${s}`;localStorage.removeItem(i)}),this._showResetBtnNotification(!1)}destroy(){this._ssr||(this._cleanup(),this.settings=null,this.container.parentNode.removeChild(this.container),this.container=null)}_loadSettings(){const t=this._getSetting("Rtl"),e=this._getSetting("Style"),s=this._getSetting("Theme"),i=this._getSetting("contentLayout"),n=this._getSetting("LayoutCollapsed"),f=this._getSetting("ShowDropdownOnHover"),c=this._getSetting("FixedNavbarOption"),r=this._getSetting("FixedFooter"),h=this._getSetting("LayoutType"),d=!this._getLayoutName().includes("front"),S=d?"admin-mode":"front-mode",C=d?"admin-colorPref":"front-colorPref";t!==""||e!==""||s!==""||i!==""||n!==""||c!==""||h!==""?this._showResetBtnNotification(!0):this._showResetBtnNotification(!1);let b;h!==""&&["static","static-offcanvas","fixed","fixed-offcanvas"].indexOf(h)!==-1?b=h:b=this.settings.defaultHeaderType,this.settings.headerType=b,this.settings.rtl=this._checkCookie("direction")?this._getCookie("direction"):t!==""?t==="true":this.settings.defaultTextDir,this.settings.stylesOpt=this.settings.styles.indexOf(e)!==-1?e:this.settings.defaultStyle,this._getCookie(S)==="system"?window.matchMedia("(prefers-color-scheme: dark)").matches?(this._setCookie(C,"dark",365),this.settings.style="dark"):(this._setCookie(C,"light",365),this.settings.style="light"):this.settings.stylesOpt==="system"?window.matchMedia("(prefers-color-scheme: dark)").matches?this.settings.style="dark":this.settings.style="light":this.settings.style=this.settings.styles.indexOf(e)!==-1?e:this.settings.stylesOpt,this.settings.contentLayout=i!==""?i:this.settings.defaultContentLayout,this.settings.layoutCollapsed=n!==""?n==="true":this.settings.defaultMenuCollapsed,this.settings.showDropdownOnHover=f!==""?f==="true":this.settings.defaultShowDropdownOnHover;let w;c!==""&&["static","sticky","hidden"].indexOf(c)!==-1?w=c:w=this.settings.defaultNavbarType,this.settings.layoutNavbarOptions=w,this.settings.layoutFooterFixed=r!==""?r==="true":this.settings.defaultFooterFixed,this._checkCookie("theme")?this.settings.theme=this._getThemeByName(this._getCookie("theme"),!0):this.settings.theme=this._getThemeByName(this._getSetting("Theme"),!0),this._hasControls("rtl")||(this.settings.rtl=document.documentElement.getAttribute("dir")==="rtl"),this._hasControls("style")||(this.settings.style=y.contains("dark-style")?"dark":"light"),this._hasControls("contentLayout")||(this.settings.contentLayout=null),this._hasControls("headerType")||(this.settings.headerType=null),this._hasControls("layoutCollapsed")||(this.settings.layoutCollapsed=null),this._hasControls("layoutNavbarOptions")||(this.settings.layoutNavbarOptions=null),this._hasControls("themes")||(this.settings.theme=null)}_setup(t=document){const e=(a,u,g,T,k)=>(k=k||a,this._getElementFromString(`<div class="col-4 px-2">
      <div class="form-check custom-option custom-option-icon">
        <label class="form-check-label custom-option-content p-0" for="${g}${a}">
          <span class="custom-option-body mb-0">
            <img src="${assetsPath}img/customizer/${k}${T?"-dark":""}.svg" alt="${u}" class="img-fluid scaleX-n1-rtl" />
          </span>
          <input
            name="${g}"
            class="form-check-input d-none"
            type="radio"
            value="${a}"
            id="${g}${a}" />
        </label>
      </div>
      <label class="form-check-label small text-nowrap text-body mt-1" for="${g}${a}">${u}</label>
    </div>`));this._cleanup(),this.container=this._getElementFromString(z);const s=this.container;this.settings.displayCustomizer?s.setAttribute("style","visibility: visible"):s.setAttribute("style","visibility: hidden");const i=this.container.querySelector(".template-customizer-open-btn"),n=()=>{this.container.classList.add("template-customizer-open"),this.update(),this._updateInterval&&clearInterval(this._updateInterval),this._updateInterval=setInterval(()=>{this.update()},500)};i.addEventListener("click",n),this._listeners.push([i,"click",n]);const f=this.container.querySelector(".template-customizer-reset-btn"),c=()=>{this._getLayoutName().includes("front")?(this._deleteCookie("front-mode"),this._deleteCookie("front-colorPref")):(this._deleteCookie("admin-mode"),this._deleteCookie("admin-colorPref")),this.clearLocalStorage(),window.location.reload(),this._deleteCookie("colorPref"),this._deleteCookie("theme"),this._deleteCookie("direction")};f.addEventListener("click",c),this._listeners.push([f,"click",c]);const r=this.container.querySelector(".template-customizer-close-btn"),h=()=>{this.container.classList.remove("template-customizer-open"),this._updateInterval&&(clearInterval(this._updateInterval),this._updateInterval=null)};r.addEventListener("click",h),this._listeners.push([r,"click",h]);const _=this.container.querySelector(".template-customizer-style"),d=_.querySelector(".template-customizer-styles-options");if(!this._hasControls("style"))_.parentNode.removeChild(_);else{this.settings.availableStyles.forEach(u=>{const g=e(u.name,u.title,"customRadioIcon",y.contains("dark-style"));d.appendChild(g)}),d.querySelector(`input[value="${this.settings.stylesOpt}"]`).setAttribute("checked","checked");const a=u=>{this._loadingState(!0),this.setStyle(u.target.value,!0,()=>{this._loadingState(!1)})};d.addEventListener("change",a),this._listeners.push([d,"change",a])}const S=this.container.querySelector(".template-customizer-themes"),C=S.querySelector(".template-customizer-themes-options");if(!this._hasControls("themes"))S.parentNode.removeChild(S);else{this.settings.availableThemes.forEach(u=>{let g="";u.name==="theme-semi-dark"?g="semi-dark":u.name==="theme-bordered"?g="border":g="default";const T=e(u.name,u.title,"themeRadios",y.contains("dark-style"),g);C.appendChild(T)}),C.querySelector(`input[value="${this.settings.theme.name}"]`).setAttribute("checked","checked");const a=u=>{this._loading=!0,this._loadingState(!0,!0),this.setTheme(u.target.value,!0,()=>{this._loading=!1,this._loadingState(!1,!0)})};C.addEventListener("change",a),this._listeners.push([C,"change",a])}const b=this.container.querySelector(".template-customizer-theming");!this._hasControls("style")&&!this._hasControls("themes")&&b.parentNode.removeChild(b);const w=this.container.querySelector(".template-customizer-layout");if(!this._hasControls("rtl headerType contentLayout layoutCollapsed layoutNavbarOptions",!0))w.parentNode.removeChild(w);else{const a=this.container.querySelector(".template-customizer-directions");if(!this._hasControls("rtl")||!rtlSupport)a.parentNode.removeChild(a);else{const l=a.querySelector(".template-customizer-directions-options");this.settings.availableDirections.forEach(o=>{const v=e(o.name,o.title,"directionRadioIcon",y.contains("dark-style"));l.appendChild(v)}),l.querySelector(`input[value="${this.settings.rtl==="true"?"rtl":"ltr"}"]`).setAttribute("checked","checked");const p=o=>{this._loadingState(!0),this._getSetting("Lang")==="ar"?this._setSetting("Lang","en"):this._setSetting("Lang","ar"),this.setRtl(o.target.value==="rtl",!0,()=>{this._loadingState(!1)}),o.target.value==="rtl"?window.location.href=baseUrl+"lang/ar":window.location.href=baseUrl+"lang/en"};l.addEventListener("change",p),this._listeners.push([l,"change",p])}const u=this.container.querySelector(".template-customizer-headerOptions"),g=document.documentElement.getAttribute("data-template").split("-");if(!this._hasControls("headerType"))u.parentNode.removeChild(u);else{const l=u.querySelector(".template-customizer-header-options");setTimeout(()=>{g.includes("vertical")&&u.parentNode.removeChild(u)},100),this.settings.availableHeaderTypes.forEach(o=>{const v=e(o.name,o.title,"headerRadioIcon",y.contains("dark-style"),`horizontal-${o.name}`);l.appendChild(v)}),l.querySelector(`input[value="${this.settings.headerType}"]`).setAttribute("checked","checked");const p=o=>{this.setLayoutType(o.target.value)};l.addEventListener("change",p),this._listeners.push([l,"change",p])}const T=this.container.querySelector(".template-customizer-content");if(!this._hasControls("contentLayout"))T.parentNode.removeChild(T);else{const l=T.querySelector(".template-customizer-content-options");this.settings.availableContentLayouts.forEach(o=>{const v=e(o.name,o.title,"contentRadioIcon",y.contains("dark-style"));l.appendChild(v)}),l.querySelector(`input[value="${this.settings.contentLayout}"]`).setAttribute("checked","checked");const p=o=>{this._loading=!0,this._loadingState(!0,!0),this.setContentLayout(o.target.value,!0,()=>{this._loading=!1,this._loadingState(!1,!0)})};l.addEventListener("change",p),this._listeners.push([l,"change",p])}const k=this.container.querySelector(".template-customizer-layouts");if(!this._hasControls("layoutCollapsed"))k.parentNode.removeChild(k);else{setTimeout(()=>{document.querySelector(".layout-menu-horizontal")&&k.parentNode.removeChild(k)},100);const l=k.querySelector(".template-customizer-layouts-options");this.settings.availableLayouts.forEach(o=>{const v=e(o.name,o.title,"layoutsRadios",y.contains("dark-style"));l.appendChild(v)}),l.querySelector(`input[value="${this.settings.layoutCollapsed?"collapsed":"expanded"}"]`).setAttribute("checked","checked");const p=o=>{window.Helpers.setCollapsed(o.target.value==="collapsed",!0),this._setSetting("LayoutCollapsed",o.target.value==="collapsed")};l.addEventListener("change",p),this._listeners.push([l,"change",p])}const L=this.container.querySelector(".template-customizer-layoutNavbarOptions");if(!this._hasControls("layoutNavbarOptions"))L.parentNode.removeChild(L);else{setTimeout(()=>{g.includes("horizontal")&&L.parentNode.removeChild(L)},100);const l=L.querySelector(".template-customizer-navbar-options");this.settings.availableNavbarOptions.forEach(o=>{const v=e(o.name,o.title,"navbarOptionRadios",y.contains("dark-style"));l.appendChild(v)}),l.querySelector(`input[value="${this.settings.layoutNavbarOptions}"]`).setAttribute("checked","checked");const p=o=>{this._loading=!0,this._loadingState(!0,!0),this.setLayoutNavbarOption(o.target.value,!0,()=>{this._loading=!1,this._loadingState(!1,!0)})};l.addEventListener("change",p),this._listeners.push([l,"change",p])}}setTimeout(()=>{const a=this.container.querySelector(".template-customizer-layout");document.querySelector(".menu-vertical")?this._hasControls("rtl contentLayout layoutCollapsed layoutNavbarOptions",!0)||a&&a.parentNode.removeChild(a):document.querySelector(".menu-horizontal")&&(this._hasControls("rtl contentLayout headerType",!0)||a&&a.parentNode.removeChild(a))},100),this.setLang(this.settings.lang,!1,!0),t===document?t.body?t.body.appendChild(this.container):window.addEventListener("DOMContentLoaded",()=>t.body.appendChild(this.container)):t.appendChild(this.container)}_initDirection(){this._hasControls("rtl")&&document.documentElement.setAttribute("dir",this._checkCookie("direction")?this._getCookie("direction")==="true"?"rtl":"ltr":this.settings.rtl?"rtl":"ltr")}_initStyle(){if(!this._hasControls("style"))return;const{style:t}=this.settings;this._insertStylesheet("template-customizer-core-css",this.pathResolver(this.settings.cssPath+this.settings.cssFilenamePattern.replace("%name%",`core${t!=="light"?`-${t}`:""}`))),(t==="light"?["dark-style"]:["light-style"]).forEach(s=>{document.documentElement.classList.remove(s)}),document.documentElement.classList.add(`${t}-style`)}_initTheme(){if(this._hasControls("themes"))this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",this.settings.theme.name+(this.settings.style!=="light"?`-${this.settings.style}`:""))));else{const t=this._getSetting("Theme");this._insertStylesheet("template-customizer-theme-css",this.pathResolver(this.settings.themesPath+this.settings.cssFilenamePattern.replace("%name%",t||this.settings.defaultTheme.name+(this.settings.style!=="light"?`-${this.settings.style}`:""))))}}_loadStylesheet(t,e){const s=document.createElement("link");s.rel="stylesheet",s.type="text/css",s.href=t,s.className=e,document.head.appendChild(s)}_insertStylesheet(t,e){const s=document.querySelector(`.${t}`);if(typeof document.documentMode=="number"&&document.documentMode<11){if(!s||e===s.getAttribute("href"))return;const i=document.createElement("link");i.setAttribute("rel","stylesheet"),i.setAttribute("type","text/css"),i.className=t,i.setAttribute("href",e),s.parentNode.insertBefore(i,s.nextSibling)}else this._loadStylesheet(e,t);s&&s.parentNode.removeChild(s)}_loadStylesheets(t,e){const s=Object.keys(t),i=s.length;let n=0;function f(r,h,_=()=>{}){const d=document.createElement("link");d.setAttribute("href",r),d.setAttribute("rel","stylesheet"),d.setAttribute("type","text/css"),d.className=h.className;const S="sheet"in d?"sheet":"styleSheet",C="sheet"in d?"cssRules":"rules";let b;const w=setTimeout(()=>{clearInterval(b),clearTimeout(w),h.parentNode.contains(d)&&h.parentNode.removeChild(d),_(!1,r)},15e3);b=setInterval(()=>{try{d[S]&&d[S][C].length&&(clearInterval(b),clearTimeout(w),h.parentNode.removeChild(h),_(!0))}catch{}},10),h.setAttribute("href",d.href)}function c(){(n+=1)>=i&&e()}for(let r=0;r<s.length;r++)f(s[r],t[s[r]],c())}_loadingState(t,e){this.container.classList[t?"add":"remove"](`template-customizer-loading${e?"-theme":""}`)}_getElementFromString(t){const e=document.createElement("div");return e.innerHTML=t,e.firstChild}_getSetting(t){let e=null;const s=this._getLayoutName();try{e=localStorage.getItem(`templateCustomizer-${s}--${t}`)}catch{}return String(e||"")}_showResetBtnNotification(t=!0){setTimeout(()=>{const e=this.container.querySelector(".template-customizer-reset-btn .badge");t?e.classList.remove("d-none"):e.classList.add("d-none")},200)}_setSetting(t,e){const s=this._getLayoutName();try{localStorage.setItem(`templateCustomizer-${s}--${t}`,String(e)),this._showResetBtnNotification()}catch{}}_getLayoutName(){return document.getElementsByTagName("HTML")[0].getAttribute("data-template")}_removeListeners(){for(let t=0,e=this._listeners.length;t<e;t++)this._listeners[t][0].removeEventListener(this._listeners[t][1],this._listeners[t][2])}_cleanup(){this._removeListeners(),this._listeners=[],this._controls={},this._updateInterval&&(clearInterval(this._updateInterval),this._updateInterval=null)}get _ssr(){return typeof window>"u"}_hasControls(t,e=!1){return t.split(" ").reduce((s,i)=>(this.settings.controls.indexOf(i)!==-1?(e||s!==!1)&&(s=!0):(!e||s!==!0)&&(s=!1),s),null)}_getDefaultTheme(t){let e;if(typeof t=="string"?e=this._getThemeByName(t,!1):e=this.settings.availableThemes[t],!e)throw new Error(`Theme ID "${t}" not found!`);return e}_getThemeByName(t,e=!1){const s=this.settings.availableThemes;for(let i=0,n=s.length;i<n;i++)if(s[i].name===t)return s[i];return e?this.settings.defaultTheme:null}_setCookie(t,e,s,i="/",n=""){const f=`${encodeURIComponent(t)}=${encodeURIComponent(e)}`;let c="";if(s){const _=new Date;_.setTime(_.getTime()+s*24*60*60*1e3),c=`; expires=${_.toUTCString()}`}const r=`; path=${i}`,h=n?`; domain=${n}`:"";document.cookie=`${f}${c}${r}${h}`}_getCookie(t){const e=document.cookie.split("; ");for(let s=0;s<e.length;s++){const[i,n]=e[s].split("=");if(decodeURIComponent(i)===t)return decodeURIComponent(n)}return null}_checkCookie(t){return this._getCookie(t)!==null}_deleteCookie(t){document.cookie=t+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"}}m.STYLES=[{name:"light",title:"Light"},{name:"dark",title:"Dark"},{name:"system",title:"System"}];m.THEMES=[{name:"theme-default",title:"Default"},{name:"theme-bordered",title:"Bordered"},{name:"theme-semi-dark",title:"Semi Dark"}];m.LAYOUTS=[{name:"expanded",title:"Expanded"},{name:"collapsed",title:"Collapsed"}];m.NAVBAR_OPTIONS=[{name:"sticky",title:"Sticky"},{name:"static",title:"Static"},{name:"hidden",title:"Hidden"}];m.HEADER_TYPES=[{name:"fixed",title:"Fixed"},{name:"static",title:"Static"}];m.CONTENT=[{name:"compact",title:"Compact"},{name:"wide",title:"Wide"}];m.DIRECTIONS=[{name:"ltr",title:"Left to Right (En)"},{name:"rtl",title:"Right to Left (Ar)"}];m.LANGUAGES={en:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},fr:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},ar:{panel_header:"أداة تخصيص القالب",panel_sub_header:"تخصيص ومعاينة في الوقت الحقيقي",theming_header:"السمات",style_label:"النمط (الوضع)",theme_label:"المواضيع",layout_header:"تَخطِيط",layout_label:"القائمة (الملاحة)",layout_header_label:"أنواع الرأس",content_label:"محتوى",layout_navbar_label:"نوع شريط التنقل",direction_label:"اتجاه"},de:{panel_header:"Vorlagen-Anpasser",panel_sub_header:"Anpassen und Vorschau in Echtzeit",theming_header:"Themen",style_label:"Stil (Modus)",theme_label:"Themen",layout_header:"Layout",layout_label:"Menü (Navigation)",layout_header_label:"Header-Typen",content_label:"Inhalt",layout_navbar_label:"Art der Navigationsleiste",direction_label:"Richtung"}};window.TemplateCustomizer=m;
