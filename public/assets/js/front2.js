// Handle favorite button click
function handleFavoriteClick(productId, button) {
    // Check if user is logged in (this will be set in the HTML)
    if (window.isUserLoggedIn) {
        // User is logged in, call the toggleFavorite function
        toggleFavorite(productId, button);
    } else {
        // User is not logged in, redirect to login page
        window.location.href = window.loginUrl;
    }
}

// Toggle favorite status
function toggleFavorite(productId, button) {
    fetch('/web/favorites/toggle/' + productId, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const icon = button.querySelector('i');
            if (data.isFavorite) {
                icon.classList.remove('far');
                icon.classList.add('fas', 'text-red-500');
            } else {
                icon.classList.remove('fas', 'text-red-500');
                icon.classList.add('far');
            }
        }
    })
    .catch(error => console.error('Error toggling favorite:', error));
}
